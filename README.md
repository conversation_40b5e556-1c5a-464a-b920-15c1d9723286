AGENCY BANKING SWITCH
==============

SPRING BOOT AGENCY BANKING SWITCH FOR VPN CONNECTION USING SOAP, ISO8583, T24

The framework implement the core functionality of:

1. Withdrawal
2. Enquiries
1. Aitime Purchase & Utilities
2. Deposits - Member Deposits & Non Member Deposits.

This is a spring boot with AWT capability, do not include tomcat dependencies in pom while making modification.

Spring Boot make the assumption that you will be creating a Web Application. By default it will bundle Tomcat with your application, unless you add Tomcat to your classpath. For most GUI applications you won't be using Tomcat at all, so we need to completely disable the Web Server behaviour.


