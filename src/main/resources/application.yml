---
server:
  port: 8098
  servlet:
    context-path: /spotcash_switch
---
spring:
  profiles: default
  mvc:
    async:
      request-timeout: 200000
datasource:
  spotcash:
    #url: jdbc:oracle:thin:@*************:1521:orcl
    #username: spotcashadmin
    #password: SPOTCASHADMINv10RACL3
    url: ********************************************
    username: root
    password: mysql
    driverClassName: com.mysql.cj.jdbc.Driver
    defaultSchema:
    maxPoolSize: 20
    connectionTimeout: 60000
    idleTimeout: 600000
    maxLifetime: 1800000
    hibernate:
      hbm2ddl.method: validate
      show_sql: true
      format_sql: true
      dialect: org.hibernate.dialect.MySQL8Dialect
      timeout: 2000
    reverseTransactionCount: 5
    reverseTransactionDelay: 3
    pendingTransactionDelay: 2
    duplicateTimeDelay: 60 # Time in seconds
    pdslAuthToken: eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxIiwiaWF0IjoxNTc1MjczOTAzLCJvcmdhbmlzYXRpb24iOiJUYW5nYXpvbGV0dSIsInN5c3RlbUlkIjoiMSIsInN5c3RlbU5hbWUiOiJVdGlsbGl0eSBTeXN0ZW0iLCJzaGFyZWRTZWNyZXRLZXkiOiJMeTgwMDZtWCtTazR3a1RzaHFMb1ZTcU5GcGFwSGdDYk52SmJpQXJpZVJFPSIsIndhbGxldENsaWVudElkIjoiSUtJQUFCMDlFNEVDQ0I5MkE1MDhBRURFMEQwMUIzNjQ3MkUyN0MzMDlGMDUiLCJ3YWxsZXRTaGFyZWRTZWNyZXRLZXkiOiJmNFZtMG8wYlhCUVRJRzREQUtyYnM0dXJaWmdKU2llaWgvRWlIam5mOS9FPSIsImNsaWVudElkIjoiSUtJQUMwREM4MDg3OThBNTAwNzg0OUE2N0QyQzM5MEUwMzhDODgzOTk3RDciLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJ0dGxNaWxsaXMiOiIxMjAwMDAwMDAwMDAwMDAwIiwic3ViIjoiaW50ZXJzd2l0Y2giLCJpc3MiOiJVdGlsaXR5U3lzdGVtIiwiZXhwIjoxMjAxNTc1MjczOTAzfQ.PnhnBQWpQSszeBeppWwsGJUQ6YN2S33wn_hbuwrCZgM
    kplcValidateUrl: https://*************:9001/spotcashUtilities/utility/pdslGetDetailPrepaid
    kplcTokensUrl: https://*************:9001/spotcashUtilities/utility/pdslPrepaid
    kplcPostpaidUrl: https://*************:9001/spotcashUtilities/utility/pdslPostpaid
    kplcPostpaidBalUrl: https://*************:9001/spotcashUtilities/utility/pdslPostpaidBillInfo
    threadCorePool: 10
    threadMaxPoolSize: 50
    fetchMemberImages: 2,52,39
    smsSendingUrl: https://api.prsp.tangazoletu.com/?User_ID=15571&passkey=2CFKzjE9K3&service=1&sender=21645
    testServerAddress: ************
    swaggerEnable: true
    validateIp: true
    proxyIp: 127.0.0.1
    retryCbsTrxLockedTableCount: 3
    retryCbsTrxLockedTableSleepTime: 5000
#  secondary:
#    #url: jdbc:oracle:thin:@*************:1521:orcl
#    #username: spotcashadmin
#    #password: SPOTCASHADMINv10RACL3
#    url: ***************************************
#    username: BIMAS_MBS
#    password: bimas0RACL32020
#    driverClassName: oracle.jdbc.driver.OracleDriver
#    defaultSchema: BIMAS_MBS
#    maxPoolSize: 20
#    connectionTimeout: 60000
#    idleTimeout: 600000
#    maxLifetime: 1800000
#    hibernate:
#      hbm2ddl.method: validate
#      show_sql: true
#      format_sql: true
#      dialect: org.hibernate.dialect.Oracle10gDialect
#      timeout: 2000
#    reverseTransactionCount: 5
#    reverseTransactionDelay: 3
#    pendingTransactionDelay: 2
#    duplicateTimeDelay: 60
#    pdslAuthToken: eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIxIiwiaWF0IjoxNTc1MjczOTAzLCJvcmdhbmlzYXRpb24iOiJUYW5nYXpvbGV0dSIsInN5c3RlbUlkIjoiMSIsInN5c3RlbU5hbWUiOiJVdGlsbGl0eSBTeXN0ZW0iLCJzaGFyZWRTZWNyZXRLZXkiOiJMeTgwMDZtWCtTazR3a1RzaHFMb1ZTcU5GcGFwSGdDYk52SmJpQXJpZVJFPSIsIndhbGxldENsaWVudElkIjoiSUtJQUFCMDlFNEVDQ0I5MkE1MDhBRURFMEQwMUIzNjQ3MkUyN0MzMDlGMDUiLCJ3YWxsZXRTaGFyZWRTZWNyZXRLZXkiOiJmNFZtMG8wYlhCUVRJRzREQUtyYnM0dXJaWmdKU2llaWgvRWlIam5mOS9FPSIsImNsaWVudElkIjoiSUtJQUMwREM4MDg3OThBNTAwNzg0OUE2N0QyQzM5MEUwMzhDODgzOTk3RDciLCJpcEFkZHJlc3MiOiIxMjcuMC4wLjEiLCJ0dGxNaWxsaXMiOiIxMjAwMDAwMDAwMDAwMDAwIiwic3ViIjoiaW50ZXJzd2l0Y2giLCJpc3MiOiJVdGlsaXR5U3lzdGVtIiwiZXhwIjoxMjAxNTc1MjczOTAzfQ.PnhnBQWpQSszeBeppWwsGJUQ6YN2S33wn_hbuwrCZgM
#    kplcValidateUrl: https://*************:9001/spotcashUtilities/utility/pdslGetDetailPrepaid
#    kplcTokensUrl: https://*************:9001/spotcashUtilities/utility/pdslPrepaid
#    kplcPostpaidUrl: https://*************:9001/spotcashUtilities/utility/pdslPostpaid
#    kplcPostpaidBalUrl: https://*************:9001/spotcashUtilities/utility/pdslPostpaidBillInfo
#    threadCorePool: 10
#    threadMaxPoolSize: 50
#    fetchMemberImages: 2,52,39
#    smsSendingUrl: https://api11.prsp.tangazoletu.com/?User_ID=15571&passkey=2CFKzjE9K3&service=1&sender=21645
#    swaggerEnable: true
#    validateIp: true
#    proxyIp: 127.0.0.1
logging:
  config: log4j2-spring.xml
#Initialize Context
context:
  initializer:
    classes: com.tl.spotcash.agencybanking.ContextInitializer
app:
  secretKey: SP0tcaSHM0VzE1nA
  jwt:
    secret: SP0tcaSHM0VzE1nA
    accessTokenExpiration: 600000   # time in ms(600000) which translates to 10min.
    refreshTokenExpiration: 900000 # time in ms(900000) which translates to 15min.
    signingKey: 4mEYVBZ|k2bODbwG
  otpTimeout: 4
secret:
  key: SP0tcaSHM0VzE1nA
#spring.jpa.database-platform: org.hibernate.dialect.Oracle10gDialect
#spring.jpa.properties.hibernate.current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext

# imsi urls:
#imsi:
#  auth:
#    url: https://sandbox.safaricom.co.ke/oauth/v1/generate
#    token: MVBxOVFrTWsxQ3pSZ2xxek9SYzVwekFzRGVTTWliTHM6SEpjS1N0SFNsQmdRd2t5Vw==
#  checkati:
#    url: https://sandbox.safaricom.co.ke/imsi/v1/checkATI
integrator:
  base_url: http://***********:8087/
  auth_username: <EMAIL>
  auth_password: password
bridgeEncryptionKey:
  key: SP0tcaSHM0VzE1nA
