
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking" targetNamespace="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking">
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking">
            <element name="Spotcash">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trnx_charges" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="cr_account" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="f_key" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="balance" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="customerType" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="description" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="Spotcash_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trnx_charges" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="cr_account" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="f_key" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="balance" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetAgentBalance">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="agentPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetAgentBalance_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="agentPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberBalance">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNo" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberBalance_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="MemberRegistration">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="middleName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="branchCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="surname" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="iDNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="pinNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="address" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupation" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="passportPhoto" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="frontID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="backID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="signature" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agencyUserAccount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateOfBirth" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="maritalStatus" type="string" />
                        <element minOccurs="1" maxOccurs="1" name="iDIssueDate" type="string" />
                    </sequence>
                </complexType>
            </element>
            <element name="MemberRegistration_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="middleName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="branchCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="surname" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="iDNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="pinNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="address" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupation" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="passportPhoto" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="frontID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="backID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="signature" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agencyUserAccount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateOfBirth" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="maritalStatus" type="string" />
                        <element minOccurs="1" maxOccurs="1" name="iDIssueDate" type="string" />
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNonMemberAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="clientID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNonMemberAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="clientID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberImage">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberImage_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="BlockAccountNumber">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="BlockAccountNumber_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetTransactions">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="agentPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="endDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="startTime" type="time"/>
                        <element minOccurs="1" maxOccurs="1" name="endTime" type="time"/>
                        <element minOccurs="1" maxOccurs="1" name="transactionData" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetTransactions_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="agentPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="endDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="startTime" type="time"/>
                        <element minOccurs="1" maxOccurs="1" name="endTime" type="time"/>
                        <element minOccurs="1" maxOccurs="1" name="transactionData" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ProcessReversal">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="ref_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ProcessReversal_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="ref_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateNominees">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateNominees_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>

            <element name="FetchMemberDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchMemberDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>

            <element name="FetchEvents">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="devicePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchEvents_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RegisterMember">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="authenticationMode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agentDevicePhone" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="regComment" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RegisterMember_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="authenticationMode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="agentDevicePhone" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="regComment" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>



            <element name="ValidateAccountDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>

            <element name="GetImage">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetImage_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RedeemRewards">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RedeemRewards_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RedeemItemsSelected">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="event_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="quantity" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="rewardName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="RedeemItemsSelected_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="event_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="quantity" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="rewardName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="NonMemberDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="NonMemberDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ViewDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ViewDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchMemberByMpesa">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchMemberByMpesa_Result">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="FetchTranscationId">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trxId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="paymentReference" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchTranscationId_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trxId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="paymentReference" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eventId" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetServiceCharge">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_Type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Code" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Message" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetServiceCharge_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="agent_phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_Type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Code" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Message" type="string"/>
                    </sequence>
                </complexType>
            </element>

        </schema>
    </types>
    <message name="Spotcash">
        <part name="parameters" element="tns:Spotcash"/>
    </message>
    <message name="Spotcash_Result">
        <part name="parameters" element="tns:Spotcash_Result"/>
    </message>
    <message name="GetAgentBalance">
        <part name="parameters" element="tns:GetAgentBalance"/>
    </message>
    <message name="GetAgentBalance_Result">
        <part name="parameters" element="tns:GetAgentBalance_Result"/>
    </message>
    <message name="GetMemberBalance">
        <part name="parameters" element="tns:GetMemberBalance"/>
    </message>
    <message name="GetMemberBalance_Result">
        <part name="parameters" element="tns:GetMemberBalance_Result"/>
    </message>
    <message name="MemberRegistration">
        <part name="parameters" element="tns:MemberRegistration"/>
    </message>
    <message name="MemberRegistration_Result">
        <part name="parameters" element="tns:MemberRegistration_Result"/>
    </message>
    <message name="GetMemberAccounts">
        <part name="parameters" element="tns:GetMemberAccounts"/>
    </message>
    <message name="GetMemberAccounts_Result">
        <part name="parameters" element="tns:GetMemberAccounts_Result"/>
    </message>
    <message name="GetNonMemberAccounts">
        <part name="parameters" element="tns:GetNonMemberAccounts"/>
    </message>
    <message name="GetNonMemberAccounts_Result">
        <part name="parameters" element="tns:GetNonMemberAccounts_Result"/>
    </message>
    <message name="ValidateAccountDetails">
        <part name="parameters" element="tns:ValidateAccountDetails"/>
    </message>
    <message name="ValidateAccountDetails_Result">
        <part name="parameters" element="tns:ValidateAccountDetails_Result"/>
    </message>
    <message name="GetMemberImage">
        <part name="parameters" element="tns:GetMemberImage"/>
    </message>
    <message name="GetMemberImage_Result">
        <part name="parameters" element="tns:GetMemberImage_Result"/>
    </message>
    <message name="BlockAccountNumber">
        <part name="parameters" element="tns:BlockAccountNumber"/>
    </message>
    <message name="BlockAccountNumber_Result">
        <part name="parameters" element="tns:BlockAccountNumber_Result"/>
    </message>
    <message name="validateMemberRegistered">
        <part name="parameters" element="tns:validateMemberRegistered"/>
    </message>
    <message name="validateMemberRegistered_Result">
        <part name="parameters" element="tns:validateMemberRegistered_Result"/>
    </message>
    <message name="validateMember">
        <part name="parameters" element="tns:validateMember"/>
    </message>
    <message name="validateMember_Result">
        <part name="parameters" element="tns:validateMember_Result"/>
    </message>
    <message name="GetTransactions">
        <part name="parameters" element="tns:GetTransactions"/>
    </message>
    <message name="GetTransactions_Result">
        <part name="parameters" element="tns:GetTransactions_Result"/>
    </message>
    <message name="ProcessReversal">
        <part name="parameters" element="tns:ProcessReversal"/>
    </message>
    <message name="ProcessReversal_Result">
        <part name="parameters" element="tns:ProcessReversal_Result"/>
    </message>
    <message name="CreateNominees">
        <part name="parameters" element="tns:CreateNominees"/>
    </message>
    <message name="CreateNominees_Result">
        <part name="parameters" element="tns:CreateNominees_Result"/>
    </message>
    <message name="FetchEvents">
        <part name="parameters" element="tns:FetchEvents"/>
    </message>
    <message name="FetchEvents_Result">
        <part name="parameters" element="tns:FetchEvents_Result"/>
    </message>
    <message name="FetchMemberDetails">
        <part name="parameters" element="tns:FetchMemberDetails"/>
    </message>
    <message name="FetchMemberDetails_Result">
        <part name="parameters" element="tns:FetchMemberDetails_Result"/>
    </message>
    <message name="RegisterMember">
        <part name="parameters" element="tns:RegisterMember"/>
    </message>
    <message name="RegisterMember_Result">
        <part name="parameters" element="tns:RegisterMember_Result"/>
    </message>
    <message name="GetImage">
        <part name="parameters" element="tns:GetImage"/>
    </message>
    <message name="GetImage_Result">
        <part name="parameters" element="tns:GetImage_Result"/>
    </message>
    <message name="RedeemRewards">
        <part name="parameters" element="tns:RedeemRewards"/>
    </message>
    <message name="RedeemRewards_Result">
        <part name="parameters" element="tns:RedeemRewards_Result"/>
    </message>
    <message name="RedeemItemsSelected">
        <part name="parameters" element="tns:RedeemItemsSelected"/>
    </message>
    <message name="RedeemItemsSelected_Result">
        <part name="parameters" element="tns:RedeemItemsSelected_Result"/>
    </message>
    <message name="NonMemberDetails">
        <part name="parameters" element="tns:NonMemberDetails"/>
    </message>
    <message name="NonMemberDetails">
        <part name="parameters" element="tns:NonMemberDetails_Result"/>
    </message>
    <message name="ViewDetails">
        <part name="parameters" element="tns:ViewDetails"/>
    </message>
    <message name="ViewDetails_Result">
        <part name="parameters" element="tns:ViewDetails_Result"/>
    </message>
    <message name="FetchMemberByMpesa">
        <part name="parameters" element="tns:FetchMemberByMpesa"/>
    </message>
    <message name="FetchMemberByMpesa_Result">
        <part name="parameters" element="tns:FetchMemberByMpesa_Result"/>
    </message>
    <message name="FetchTranscationId">
        <part name="parameters" element="tns:FetchTranscationId"/>
    </message>
    <message name="FetchTranscationId_Result">
        <part name="parameters" element="tns:FetchTranscationId_Result"/>
    </message>
    <message name="GetServiceCharge">
        <part name="parameters" element="tns:GetServiceCharge"/>
    </message>
    <message name="GetServiceCharge_Result">
        <part name="parameters" element="tns:GetServiceCharge_Result"/>
    </message>

    <portType name="AgencyBanking_Port">
        <operation name="Spotcash">
            <input name="Spotcash" message="tns:Spotcash"/>
            <output name="Spotcash_Result" message="tns:Spotcash_Result"/>
        </operation>
        <operation name="GetAgentBalance">
            <input name="GetAgentBalance" message="tns:GetAgentBalance"/>
            <output name="GetAgentBalance_Result" message="tns:GetAgentBalance_Result"/>
        </operation>
        <operation name="GetMemberBalance">
            <input name="GetMemberBalance" message="tns:GetMemberBalance"/>
            <output name="GetMemberBalance_Result" message="tns:GetMemberBalance_Result"/>
        </operation>
        <operation name="MemberRegistration">
            <input name="MemberRegistration" message="tns:MemberRegistration"/>
            <output name="MemberRegistration_Result" message="tns:MemberRegistration_Result"/>
        </operation>
        <operation name="GetMemberAccounts">
            <input name="GetMemberAccounts" message="tns:GetMemberAccounts"/>
            <output name="GetMemberAccounts_Result" message="tns:GetMemberAccounts_Result"/>
        </operation>
        <operation name="GetNonMemberAccounts">
            <input name="GetNonMemberAccounts" message="tns:GetNonMemberAccounts"/>
            <output name="GetNonMemberAccounts_Result" message="tns:GetNonMemberAccounts_Result"/>
        </operation>
        <operation name="ValidateAccountDetails">
            <input name="ValidateAccountDetails" message="tns:ValidateAccountDetails"/>
            <output name="ValidateAccountDetails_Result" message="tns:ValidateAccountDetails_Result"/>
        </operation>
        <operation name="GetMemberImage">
            <input name="GetMemberImage" message="tns:GetMemberImage"/>
            <output name="GetMemberImage_Result" message="tns:GetMemberImage_Result"/>
        </operation>
        <operation name="BlockAccountNumber">
            <input name="BlockAccountNumber" message="tns:BlockAccountNumber"/>
            <output name="BlockAccountNumber_Result" message="tns:BlockAccountNumber_Result"/>
        </operation>
        <operation name="GetTransactions">
            <input name="GetTransactions" message="tns:GetTransactions"/>
            <output name="GetTransactions_Result" message="tns:GetTransactions_Result"/>
        </operation>
        <operation name="ProcessReversal">
            <input name="ProcessReversal" message="tns:ProcessReversal"/>
            <output name="ProcessReversal_Result" message="tns:ProcessReversal_Result"/>
        </operation>
        <operation name="CreateNominees">
            <input name="CreateNominees" message="tns:CreateNominees"/>
            <output name="CreateNominees_Result" message="tns:CreateNominees_Result"/>
        </operation>
        <operation name="FetchMemberDetails">
            <input name="FetchMemberDetails" message="tns:FetchMemberDetails"/>
            <output name="FetchMemberDetails_Result" message="tns:FetchMemberDetails_Result"/>
        </operation>
        <operation name="FetchEvents">
            <input name="FetchEvents" message="tns:FetchEvents"/>
            <output name="FetchEvents_Result" message="tns:FetchEvents_Result"/>
        </operation>
        <operation name="validateMemberRegistered">
            <input name="validateMemberRegistered" message="tns:validateMemberRegistered"/>
            <output name="validateMemberRegistered_Result" message="tns:validateMemberRegistered_Result"/>
        </operation>
        <operation name="validateMember">
            <input name="validateMember" message="tns:validateMember"/>
            <output name="validateMember_Result" message="tns:validateMember_Result"/>
        </operation>
        <operation name="RegisterMember">
            <input name="RegisterMember" message="tns:RegisterMember"/>
            <output name="RegisterMember_Result" message="tns:RegisterMember_Result"/>
        </operation>
        <operation name="GetImage">
            <input name="GetImage" message="tns:GetImage"/>
            <output name="GetImage_Result" message="tns:GetImage_Result"/>
        </operation>
        <operation name="RedeemRewards">
            <input name="RedeemRewards" message="tns:RedeemRewards"/>
            <output name="RedeemRewards_Result" message="tns:RedeemRewards_Result"/>
        </operation>
        <operation name="RedeemItemsSelected">
            <input name="RedeemItemsSelected" message="tns:RedeemItemsSelected"/>
            <output name="RedeemItemsSelected_Result" message="tns:RedeemItemsSelected_Result"/>
        </operation>
        <operation name="NonMemberDetails">
            <input name="NonMemberDetails" message="tns:NonMemberDetails"/>
            <output name="NonMemberDetails_Result" message="tns:NonMemberDetails_Result"/>
        </operation>
        <operation name="ViewDetails">
            <input name="ViewDetails" message="tns:ViewDetails"/>
            <output name="ViewDetails_Result" message="tns:ViewDetails_Result"/>
        </operation>
        <operation name="FetchMemberByMpesa">
            <input name="FetchMemberByMpesa" message="tns:FetchMemberByMpesa"/>
            <output name="FetchMemberByMpesa_Result" message="tns:FetchMemberByMpesa_Result"/>
        </operation>
        <operation name="FetchTranscationId">
            <input name="FetchTranscationId" message="tns:FetchTranscationId"/>
            <output name="FetchTranscationId_Result" message="tns:FetchTranscationId_Result"/>
        </operation>
        <operation name="GetServiceCharge">
            <input name="GetServiceCharge" message="tns:GetServiceCharge"/>
            <output name="GetServiceCharge_Result" message="tns:GetServiceCharge_Result"/>
        </operation>
    </portType>
    <binding name="AgencyBanking_Binding" type="tns:AgencyBanking_Port">
        <binding xmlns="http://schemas.xmlsoap.org/wsdl/soap/" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="Spotcash">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:Spotcash" style="document"/>
            <input name="Spotcash">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="Spotcash_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="validateMemberRegistered">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:validateMemberRegistered" style="document"/>
            <input name="validateMemberRegistered">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="validateMemberRegistered_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="validateMember">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:validateMember" style="document"/>
            <input name="validateMember">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="validateMember_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetAgentBalance">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetAgentBalance" style="document"/>
            <input name="GetAgentBalance">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetAgentBalance_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberBalance">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberBalance" style="document"/>
            <input name="GetMemberBalance">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberBalance_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="MemberRegistration">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:MemberRegistration" style="document"/>
            <input name="MemberRegistration">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="MemberRegistration_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberAccounts" style="document"/>
            <input name="GetMemberAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetNonMemberAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetNonMemberAccounts" style="document"/>
            <input name="GetNonMemberAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetNonMemberAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ValidateAccountDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ValidateAccountDetails" style="document"/>
            <input name="ValidateAccountDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ValidateAccountDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberImage">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberImage" style="document"/>
            <input name="GetMemberImage">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberImage_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="BlockAccountNumber">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:BlockAccountNumber" style="document"/>
            <input name="BlockAccountNumber">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="BlockAccountNumber_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetTransactions">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetTransactions" style="document"/>
            <input name="GetTransactions">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetTransactions_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ProcessReversal">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ProcessReversal" style="document"/>
            <input name="ProcessReversal">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ProcessReversal_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CreateNominees">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:CreateNominees" style="document"/>
            <input name="CreateNominees">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CreateNominees_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="FetchEvents">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchEvents" style="document"/>
            <input name="FetchEvents">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="FetchEvents_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="FetchMemberDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchMemberDetails" style="document"/>
            <input name="FetchMemberDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="FetchMemberDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="RegisterMember">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RegisterMember" style="document"/>
            <input name="RegisterMember">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="RegisterMember_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetImage">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetImage" style="document"/>
            <input name="GetImage">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetImage_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="RedeemRewards">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RedeemRewards" style="document"/>
            <input name="RedeemRewards">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="RedeemRewards_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="RedeemItemsSelected">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RedeemItemsSelected" style="document"/>
            <input name="RedeemItemsSelected">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="RedeemItemsSelected_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="NonMemberDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:NonMemberDetails" style="document"/>
            <input name="NonMemberDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="NonMemberDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ViewDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ViewDetails" style="document"/>
            <input name="ViewDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ViewDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="FetchMemberByMpesa">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchMemberByMpesa" style="document"/>
            <input name="FetchMemberByMpesa">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="FetchMemberByMpesa_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="FetchTranscationId">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchTranscationId" style="document"/>
            <input name="FetchTranscationId">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="FetchTranscationId_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetServiceCharge">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetServiceCharge" style="document"/>
            <input name="GetServiceCharge">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetServiceCharge">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="AgencyBanking">
        <port name="AgencyBanking_Port" binding="tns:AgencyBanking_Binding">
            <address xmlns="http://schemas.xmlsoap.org/wsdl/soap/" location="http://cbs-sandbox.office.tangazoletu.com:27048/TAI2/WS/TAI/Codeunit/AgencyBankingx"/>
        </port>
    </service>
</definitions>
