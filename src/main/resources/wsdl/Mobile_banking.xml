<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="urn:microsoft-dynamics-schemas/codeunit/MobileBanking" targetNamespace="urn:microsoft-dynamics-schemas/codeunit/MobileBanking">
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:microsoft-dynamics-schemas/codeunit/MobileBanking">
            <element name="SpotcashRegistration">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistration_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetSavingsAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetSavingsAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetAgentBalance">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="msisdn" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetAgentBalance_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="msisdn" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="balance" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="Spotcash">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trnx_charges" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="cr_account" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="f_key" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="balance" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="customerType" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="description" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startDate" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="endDate" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startTime" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="endTime" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="emailaddress" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="Spotcash_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="request_id" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phone_no" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="transaction_type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="trnx_charges" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="account__number" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="cr_account" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="f_key" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="balance" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_message" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="customerType" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="description" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startDate" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="endDate" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startTime" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="endTime" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="emailaddress" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetServiceCharge">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phone_No" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="transcation_Type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Code" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Message" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetServiceCharge_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phone_No" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="transcation_Type" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Code" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="response_Message" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetShareCapitalAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetShareCapitalAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNWDAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNWDAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetOverdraftPaid">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberNumber" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetOverdraftPaid_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="decimal"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetOverdraftAmount">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="acco_no" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetOverdraftAmount_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="decimal"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetOverdraftCharge">
                <complexType>
                    <sequence/>
                </complexType>
            </element>
            <element name="GetOverdraftCharge_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="decimal"/>
                    </sequence>
                </complexType>
            </element>
            <element name="MemberRegistration">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="iDNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="middleName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="surname" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="fullName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="branchcode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="physicaladdress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postaladdress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postalCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="nationality" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="membercategory" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="pinNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="payrollNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupation" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupationaddresss" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="frontID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="backID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="passportPhoto" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="signature" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateOfBirth" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="referphoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="maritalStatus" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="MemberRegistration_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="iDNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="middleName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="surname" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="fullName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="branchcode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="physicaladdress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postaladdress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postalCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="nationality" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="membercategory" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="pinNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="payrollNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupation" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="occupationaddresss" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="frontID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="backID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="passportPhoto" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="signature" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateOfBirth" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="referphoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="maritalStatus" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMainSavingsAccount">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMainSavingsAccount_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberEligibility">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eLoanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberEligibility_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eLoanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ApplyLoan">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="requestid" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eLoanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="installments" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ApplyLoan_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="requestid" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="eLoanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="amount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="installments" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberImage">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberImage_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ReferAFriend">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="lastName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="location" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="introducerPhone" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ReferAFriend_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="firstName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="lastName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="gender" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="location" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="introducerPhone" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ProcessReversal">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="requestID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ProcessReversal_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="requestID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GeteLoanTypes">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GeteLoanTypes_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="IsMemberInArrears">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberNo" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="IsMemberInArrears_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="boolean"/>
                    </sequence>
                </complexType>
            </element>
            <element name="IsMemberInArrearsExternalUse">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberNo" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="IsMemberInArrearsExternalUse_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="decimal"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetailsWithImageAMust">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateAccountDetailsWithImageAMust_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistration22">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistration22_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CustomerLookup">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CustomerLookup_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistrationConfirmation">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistrationConfirmation_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistrationActivation">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SpotcashRegistrationActivation_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberScore">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="noofSalaryMonths" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="loanProductCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberScore_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddLoanApplication">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="loanProductCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="repaymentMethod" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="repaymentFrequency" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="repaymentPeriod" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="loanAmount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="loanNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddLoanApplication_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="loanNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetGuarantorScore">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="loanProductCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="noofSalaryMonths" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetGuarantorScore_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddGuarantors">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="loanNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="memberNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="guarantorAmount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddGuarantors_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddGuarantorACK">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="loanNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="guarantorNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="hasAcknowledged" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="AddGuarantorACK_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="hasAcknowledged" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNonDigitalLoanTypes">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetNonDigitalLoanTypes_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateLoanAccountDigital">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="loanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="loanAmount" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="noOfInstallment" type="decimal"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateLoanAccountDigital_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="loanCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetDigitalLoanTypes">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetDigitalLoanTypes_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CheckLoan">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="memberCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="elooanCode" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CheckLoan_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="boolean"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SelfRegistration">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SelfRegistration_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SelfRegistrationActivation">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="SelfRegistrationActivation_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMkopoLoanAccounts">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMkopoLoanAccounts_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateLipaKwaSaccoCode">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="lipaCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="accountNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="businessName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="additionalPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateLipaKwaSaccoCode_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="lipaCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="accountNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="businessName" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="additionalPhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateFinancials">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="spotCashID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="referenceid" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateFinancials_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="spotCashID" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="referenceid" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchStatementTransactions">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="accountNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="startDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="endDate" type="date"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="FetchStatementTransactions_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateMemberDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="emailAddress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dob" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postalAddress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="ValidateMemberDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNumber" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="emailAddress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dob" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="postalAddress" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateNominees">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateofBirth" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="CreateNominees_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="applicationNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="name" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="idNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="dateofBirth" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberDetails">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetMemberDetails_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanBalance1">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phone_No" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="registration_No" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanBalance1_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="phone_No" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="status" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="email" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="registration_No" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanAccountsGuaranteed">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="GetLoanAccountsGuaranteed_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="return_value" type="int"/>
                        <element minOccurs="1" maxOccurs="1" name="mobilePhoneNo" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="KanjaRegistration">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="KanjaRegistration_Result">
                <complexType>
                    <sequence>
                        <element minOccurs="1" maxOccurs="1" name="responseCode" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="responseMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="errorMessage" type="string"/>
                        <element minOccurs="1" maxOccurs="1" name="newMembersPayload" type="string"/>
                    </sequence>
                </complexType>
            </element>
        </schema>
    </types>
    <message name="SpotcashRegistration">
        <part name="parameters" element="tns:SpotcashRegistration"/>
    </message>
    <message name="SpotcashRegistration_Result">
        <part name="parameters" element="tns:SpotcashRegistration_Result"/>
    </message>
    <message name="GetSavingsAccounts">
        <part name="parameters" element="tns:GetSavingsAccounts"/>
    </message>
    <message name="GetSavingsAccounts_Result">
        <part name="parameters" element="tns:GetSavingsAccounts_Result"/>
    </message>
    <message name="GetAgentBalance">
        <part name="parameters" element="tns:GetAgentBalance"/>
    </message>
    <message name="GetAgentBalance_Result">
        <part name="parameters" element="tns:GetAgentBalance_Result"/>
    </message>
    <message name="Spotcash">
        <part name="parameters" element="tns:Spotcash"/>
    </message>
    <message name="Spotcash_Result">
        <part name="parameters" element="tns:Spotcash_Result"/>
    </message>
    <message name="GetServiceCharge">
        <part name="parameters" element="tns:GetServiceCharge"/>
    </message>
    <message name="GetServiceCharge_Result">
        <part name="parameters" element="tns:GetServiceCharge_Result"/>
    </message>
    <message name="GetShareCapitalAccounts">
        <part name="parameters" element="tns:GetShareCapitalAccounts"/>
    </message>
    <message name="GetShareCapitalAccounts_Result">
        <part name="parameters" element="tns:GetShareCapitalAccounts_Result"/>
    </message>
    <message name="GetLoanAccounts">
        <part name="parameters" element="tns:GetLoanAccounts"/>
    </message>
    <message name="GetLoanAccounts_Result">
        <part name="parameters" element="tns:GetLoanAccounts_Result"/>
    </message>
    <message name="GetNWDAccounts">
        <part name="parameters" element="tns:GetNWDAccounts"/>
    </message>
    <message name="GetNWDAccounts_Result">
        <part name="parameters" element="tns:GetNWDAccounts_Result"/>
    </message>
    <message name="GetOverdraftPaid">
        <part name="parameters" element="tns:GetOverdraftPaid"/>
    </message>
    <message name="GetOverdraftPaid_Result">
        <part name="parameters" element="tns:GetOverdraftPaid_Result"/>
    </message>
    <message name="GetOverdraftAmount">
        <part name="parameters" element="tns:GetOverdraftAmount"/>
    </message>
    <message name="GetOverdraftAmount_Result">
        <part name="parameters" element="tns:GetOverdraftAmount_Result"/>
    </message>
    <message name="GetOverdraftCharge">
        <part name="parameters" element="tns:GetOverdraftCharge"/>
    </message>
    <message name="GetOverdraftCharge_Result">
        <part name="parameters" element="tns:GetOverdraftCharge_Result"/>
    </message>
    <message name="MemberRegistration">
        <part name="parameters" element="tns:MemberRegistration"/>
    </message>
    <message name="MemberRegistration_Result">
        <part name="parameters" element="tns:MemberRegistration_Result"/>
    </message>
    <message name="GetMemberAccounts">
        <part name="parameters" element="tns:GetMemberAccounts"/>
    </message>
    <message name="GetMemberAccounts_Result">
        <part name="parameters" element="tns:GetMemberAccounts_Result"/>
    </message>
    <message name="GetMainSavingsAccount">
        <part name="parameters" element="tns:GetMainSavingsAccount"/>
    </message>
    <message name="GetMainSavingsAccount_Result">
        <part name="parameters" element="tns:GetMainSavingsAccount_Result"/>
    </message>
    <message name="GetMemberEligibility">
        <part name="parameters" element="tns:GetMemberEligibility"/>
    </message>
    <message name="GetMemberEligibility_Result">
        <part name="parameters" element="tns:GetMemberEligibility_Result"/>
    </message>
    <message name="ApplyLoan">
        <part name="parameters" element="tns:ApplyLoan"/>
    </message>
    <message name="ApplyLoan_Result">
        <part name="parameters" element="tns:ApplyLoan_Result"/>
    </message>
    <message name="GetMemberImage">
        <part name="parameters" element="tns:GetMemberImage"/>
    </message>
    <message name="GetMemberImage_Result">
        <part name="parameters" element="tns:GetMemberImage_Result"/>
    </message>
    <message name="ReferAFriend">
        <part name="parameters" element="tns:ReferAFriend"/>
    </message>
    <message name="ReferAFriend_Result">
        <part name="parameters" element="tns:ReferAFriend_Result"/>
    </message>
    <message name="ProcessReversal">
        <part name="parameters" element="tns:ProcessReversal"/>
    </message>
    <message name="ProcessReversal_Result">
        <part name="parameters" element="tns:ProcessReversal_Result"/>
    </message>
    <message name="GeteLoanTypes">
        <part name="parameters" element="tns:GeteLoanTypes"/>
    </message>
    <message name="GeteLoanTypes_Result">
        <part name="parameters" element="tns:GeteLoanTypes_Result"/>
    </message>
    <message name="IsMemberInArrears">
        <part name="parameters" element="tns:IsMemberInArrears"/>
    </message>
    <message name="IsMemberInArrears_Result">
        <part name="parameters" element="tns:IsMemberInArrears_Result"/>
    </message>
    <message name="IsMemberInArrearsExternalUse">
        <part name="parameters" element="tns:IsMemberInArrearsExternalUse"/>
    </message>
    <message name="IsMemberInArrearsExternalUse_Result">
        <part name="parameters" element="tns:IsMemberInArrearsExternalUse_Result"/>
    </message>
    <message name="ValidateAccountDetails">
        <part name="parameters" element="tns:ValidateAccountDetails"/>
    </message>
    <message name="ValidateAccountDetails_Result">
        <part name="parameters" element="tns:ValidateAccountDetails_Result"/>
    </message>
    <message name="ValidateAccountDetailsWithImageAMust">
        <part name="parameters" element="tns:ValidateAccountDetailsWithImageAMust"/>
    </message>
    <message name="ValidateAccountDetailsWithImageAMust_Result">
        <part name="parameters" element="tns:ValidateAccountDetailsWithImageAMust_Result"/>
    </message>
    <message name="SpotcashRegistration22">
        <part name="parameters" element="tns:SpotcashRegistration22"/>
    </message>
    <message name="SpotcashRegistration22_Result">
        <part name="parameters" element="tns:SpotcashRegistration22_Result"/>
    </message>
    <message name="CustomerLookup">
        <part name="parameters" element="tns:CustomerLookup"/>
    </message>
    <message name="CustomerLookup_Result">
        <part name="parameters" element="tns:CustomerLookup_Result"/>
    </message>
    <message name="SpotcashRegistrationConfirmation">
        <part name="parameters" element="tns:SpotcashRegistrationConfirmation"/>
    </message>
    <message name="SpotcashRegistrationConfirmation_Result">
        <part name="parameters" element="tns:SpotcashRegistrationConfirmation_Result"/>
    </message>
    <message name="SpotcashRegistrationActivation">
        <part name="parameters" element="tns:SpotcashRegistrationActivation"/>
    </message>
    <message name="SpotcashRegistrationActivation_Result">
        <part name="parameters" element="tns:SpotcashRegistrationActivation_Result"/>
    </message>
    <message name="GetMemberScore">
        <part name="parameters" element="tns:GetMemberScore"/>
    </message>
    <message name="GetMemberScore_Result">
        <part name="parameters" element="tns:GetMemberScore_Result"/>
    </message>
    <message name="AddLoanApplication">
        <part name="parameters" element="tns:AddLoanApplication"/>
    </message>
    <message name="AddLoanApplication_Result">
        <part name="parameters" element="tns:AddLoanApplication_Result"/>
    </message>
    <message name="GetGuarantorScore">
        <part name="parameters" element="tns:GetGuarantorScore"/>
    </message>
    <message name="GetGuarantorScore_Result">
        <part name="parameters" element="tns:GetGuarantorScore_Result"/>
    </message>
    <message name="AddGuarantors">
        <part name="parameters" element="tns:AddGuarantors"/>
    </message>
    <message name="AddGuarantors_Result">
        <part name="parameters" element="tns:AddGuarantors_Result"/>
    </message>
    <message name="AddGuarantorACK">
        <part name="parameters" element="tns:AddGuarantorACK"/>
    </message>
    <message name="AddGuarantorACK_Result">
        <part name="parameters" element="tns:AddGuarantorACK_Result"/>
    </message>
    <message name="GetNonDigitalLoanTypes">
        <part name="parameters" element="tns:GetNonDigitalLoanTypes"/>
    </message>
    <message name="GetNonDigitalLoanTypes_Result">
        <part name="parameters" element="tns:GetNonDigitalLoanTypes_Result"/>
    </message>
    <message name="CreateLoanAccountDigital">
        <part name="parameters" element="tns:CreateLoanAccountDigital"/>
    </message>
    <message name="CreateLoanAccountDigital_Result">
        <part name="parameters" element="tns:CreateLoanAccountDigital_Result"/>
    </message>
    <message name="GetDigitalLoanTypes">
        <part name="parameters" element="tns:GetDigitalLoanTypes"/>
    </message>
    <message name="GetDigitalLoanTypes_Result">
        <part name="parameters" element="tns:GetDigitalLoanTypes_Result"/>
    </message>
    <message name="CheckLoan">
        <part name="parameters" element="tns:CheckLoan"/>
    </message>
    <message name="CheckLoan_Result">
        <part name="parameters" element="tns:CheckLoan_Result"/>
    </message>
    <message name="SelfRegistration">
        <part name="parameters" element="tns:SelfRegistration"/>
    </message>
    <message name="SelfRegistration_Result">
        <part name="parameters" element="tns:SelfRegistration_Result"/>
    </message>
    <message name="SelfRegistrationActivation">
        <part name="parameters" element="tns:SelfRegistrationActivation"/>
    </message>
    <message name="SelfRegistrationActivation_Result">
        <part name="parameters" element="tns:SelfRegistrationActivation_Result"/>
    </message>
    <message name="GetMkopoLoanAccounts">
        <part name="parameters" element="tns:GetMkopoLoanAccounts"/>
    </message>
    <message name="GetMkopoLoanAccounts_Result">
        <part name="parameters" element="tns:GetMkopoLoanAccounts_Result"/>
    </message>
    <message name="CreateLipaKwaSaccoCode">
        <part name="parameters" element="tns:CreateLipaKwaSaccoCode"/>
    </message>
    <message name="CreateLipaKwaSaccoCode_Result">
        <part name="parameters" element="tns:CreateLipaKwaSaccoCode_Result"/>
    </message>
    <message name="ValidateFinancials">
        <part name="parameters" element="tns:ValidateFinancials"/>
    </message>
    <message name="ValidateFinancials_Result">
        <part name="parameters" element="tns:ValidateFinancials_Result"/>
    </message>
    <message name="FetchStatementTransactions">
        <part name="parameters" element="tns:FetchStatementTransactions"/>
    </message>
    <message name="FetchStatementTransactions_Result">
        <part name="parameters" element="tns:FetchStatementTransactions_Result"/>
    </message>
    <message name="ValidateMemberDetails">
        <part name="parameters" element="tns:ValidateMemberDetails"/>
    </message>
    <message name="ValidateMemberDetails_Result">
        <part name="parameters" element="tns:ValidateMemberDetails_Result"/>
    </message>
    <message name="CreateNominees">
        <part name="parameters" element="tns:CreateNominees"/>
    </message>
    <message name="CreateNominees_Result">
        <part name="parameters" element="tns:CreateNominees_Result"/>
    </message>
    <message name="GetMemberDetails">
        <part name="parameters" element="tns:GetMemberDetails"/>
    </message>
    <message name="GetMemberDetails_Result">
        <part name="parameters" element="tns:GetMemberDetails_Result"/>
    </message>
    <message name="GetLoanBalance1">
        <part name="parameters" element="tns:GetLoanBalance1"/>
    </message>
    <message name="GetLoanBalance1_Result">
        <part name="parameters" element="tns:GetLoanBalance1_Result"/>
    </message>
    <message name="GetLoanAccountsGuaranteed">
        <part name="parameters" element="tns:GetLoanAccountsGuaranteed"/>
    </message>
    <message name="GetLoanAccountsGuaranteed_Result">
        <part name="parameters" element="tns:GetLoanAccountsGuaranteed_Result"/>
    </message>
    <message name="KanjaRegistration">
        <part name="parameters" element="tns:KanjaRegistration"/>
    </message>
    <message name="KanjaRegistration_Result">
        <part name="parameters" element="tns:KanjaRegistration_Result"/>
    </message>
    <portType name="MobileBanking_Port">
        <operation name="SpotcashRegistration">
            <input name="SpotcashRegistration" message="tns:SpotcashRegistration"/>
            <output name="SpotcashRegistration_Result" message="tns:SpotcashRegistration_Result"/>
        </operation>
        <operation name="GetSavingsAccounts">
            <input name="GetSavingsAccounts" message="tns:GetSavingsAccounts"/>
            <output name="GetSavingsAccounts_Result" message="tns:GetSavingsAccounts_Result"/>
        </operation>
        <operation name="GetAgentBalance">
            <input name="GetAgentBalance" message="tns:GetAgentBalance"/>
            <output name="GetAgentBalance_Result" message="tns:GetAgentBalance_Result"/>
        </operation>
        <operation name="Spotcash">
            <input name="Spotcash" message="tns:Spotcash"/>
            <output name="Spotcash_Result" message="tns:Spotcash_Result"/>
        </operation>
        <operation name="GetServiceCharge">
            <input name="GetServiceCharge" message="tns:GetServiceCharge"/>
            <output name="GetServiceCharge_Result" message="tns:GetServiceCharge_Result"/>
        </operation>
        <operation name="GetShareCapitalAccounts">
            <input name="GetShareCapitalAccounts" message="tns:GetShareCapitalAccounts"/>
            <output name="GetShareCapitalAccounts_Result" message="tns:GetShareCapitalAccounts_Result"/>
        </operation>
        <operation name="GetLoanAccounts">
            <input name="GetLoanAccounts" message="tns:GetLoanAccounts"/>
            <output name="GetLoanAccounts_Result" message="tns:GetLoanAccounts_Result"/>
        </operation>
        <operation name="GetNWDAccounts">
            <input name="GetNWDAccounts" message="tns:GetNWDAccounts"/>
            <output name="GetNWDAccounts_Result" message="tns:GetNWDAccounts_Result"/>
        </operation>
        <operation name="GetOverdraftPaid">
            <input name="GetOverdraftPaid" message="tns:GetOverdraftPaid"/>
            <output name="GetOverdraftPaid_Result" message="tns:GetOverdraftPaid_Result"/>
        </operation>
        <operation name="GetOverdraftAmount">
            <input name="GetOverdraftAmount" message="tns:GetOverdraftAmount"/>
            <output name="GetOverdraftAmount_Result" message="tns:GetOverdraftAmount_Result"/>
        </operation>
        <operation name="GetOverdraftCharge">
            <input name="GetOverdraftCharge" message="tns:GetOverdraftCharge"/>
            <output name="GetOverdraftCharge_Result" message="tns:GetOverdraftCharge_Result"/>
        </operation>
        <operation name="MemberRegistration">
            <input name="MemberRegistration" message="tns:MemberRegistration"/>
            <output name="MemberRegistration_Result" message="tns:MemberRegistration_Result"/>
        </operation>
        <operation name="GetMemberAccounts">
            <input name="GetMemberAccounts" message="tns:GetMemberAccounts"/>
            <output name="GetMemberAccounts_Result" message="tns:GetMemberAccounts_Result"/>
        </operation>
        <operation name="GetMainSavingsAccount">
            <input name="GetMainSavingsAccount" message="tns:GetMainSavingsAccount"/>
            <output name="GetMainSavingsAccount_Result" message="tns:GetMainSavingsAccount_Result"/>
        </operation>
        <operation name="GetMemberEligibility">
            <input name="GetMemberEligibility" message="tns:GetMemberEligibility"/>
            <output name="GetMemberEligibility_Result" message="tns:GetMemberEligibility_Result"/>
        </operation>
        <operation name="ApplyLoan">
            <input name="ApplyLoan" message="tns:ApplyLoan"/>
            <output name="ApplyLoan_Result" message="tns:ApplyLoan_Result"/>
        </operation>
        <operation name="GetMemberImage">
            <input name="GetMemberImage" message="tns:GetMemberImage"/>
            <output name="GetMemberImage_Result" message="tns:GetMemberImage_Result"/>
        </operation>
        <operation name="ReferAFriend">
            <input name="ReferAFriend" message="tns:ReferAFriend"/>
            <output name="ReferAFriend_Result" message="tns:ReferAFriend_Result"/>
        </operation>
        <operation name="ProcessReversal">
            <input name="ProcessReversal" message="tns:ProcessReversal"/>
            <output name="ProcessReversal_Result" message="tns:ProcessReversal_Result"/>
        </operation>
        <operation name="GeteLoanTypes">
            <input name="GeteLoanTypes" message="tns:GeteLoanTypes"/>
            <output name="GeteLoanTypes_Result" message="tns:GeteLoanTypes_Result"/>
        </operation>
        <operation name="IsMemberInArrears">
            <input name="IsMemberInArrears" message="tns:IsMemberInArrears"/>
            <output name="IsMemberInArrears_Result" message="tns:IsMemberInArrears_Result"/>
        </operation>
        <operation name="IsMemberInArrearsExternalUse">
            <input name="IsMemberInArrearsExternalUse" message="tns:IsMemberInArrearsExternalUse"/>
            <output name="IsMemberInArrearsExternalUse_Result" message="tns:IsMemberInArrearsExternalUse_Result"/>
        </operation>
        <operation name="ValidateAccountDetails">
            <input name="ValidateAccountDetails" message="tns:ValidateAccountDetails"/>
            <output name="ValidateAccountDetails_Result" message="tns:ValidateAccountDetails_Result"/>
        </operation>
        <operation name="ValidateAccountDetailsWithImageAMust">
            <input name="ValidateAccountDetailsWithImageAMust" message="tns:ValidateAccountDetailsWithImageAMust"/>
            <output name="ValidateAccountDetailsWithImageAMust_Result" message="tns:ValidateAccountDetailsWithImageAMust_Result"/>
        </operation>
        <operation name="SpotcashRegistration22">
            <input name="SpotcashRegistration22" message="tns:SpotcashRegistration22"/>
            <output name="SpotcashRegistration22_Result" message="tns:SpotcashRegistration22_Result"/>
        </operation>
        <operation name="CustomerLookup">
            <input name="CustomerLookup" message="tns:CustomerLookup"/>
            <output name="CustomerLookup_Result" message="tns:CustomerLookup_Result"/>
        </operation>
        <operation name="SpotcashRegistrationConfirmation">
            <input name="SpotcashRegistrationConfirmation" message="tns:SpotcashRegistrationConfirmation"/>
            <output name="SpotcashRegistrationConfirmation_Result" message="tns:SpotcashRegistrationConfirmation_Result"/>
        </operation>
        <operation name="SpotcashRegistrationActivation">
            <input name="SpotcashRegistrationActivation" message="tns:SpotcashRegistrationActivation"/>
            <output name="SpotcashRegistrationActivation_Result" message="tns:SpotcashRegistrationActivation_Result"/>
        </operation>
        <operation name="GetMemberScore">
            <input name="GetMemberScore" message="tns:GetMemberScore"/>
            <output name="GetMemberScore_Result" message="tns:GetMemberScore_Result"/>
        </operation>
        <operation name="AddLoanApplication">
            <input name="AddLoanApplication" message="tns:AddLoanApplication"/>
            <output name="AddLoanApplication_Result" message="tns:AddLoanApplication_Result"/>
        </operation>
        <operation name="GetGuarantorScore">
            <input name="GetGuarantorScore" message="tns:GetGuarantorScore"/>
            <output name="GetGuarantorScore_Result" message="tns:GetGuarantorScore_Result"/>
        </operation>
        <operation name="AddGuarantors">
            <input name="AddGuarantors" message="tns:AddGuarantors"/>
            <output name="AddGuarantors_Result" message="tns:AddGuarantors_Result"/>
        </operation>
        <operation name="AddGuarantorACK">
            <input name="AddGuarantorACK" message="tns:AddGuarantorACK"/>
            <output name="AddGuarantorACK_Result" message="tns:AddGuarantorACK_Result"/>
        </operation>
        <operation name="GetNonDigitalLoanTypes">
            <input name="GetNonDigitalLoanTypes" message="tns:GetNonDigitalLoanTypes"/>
            <output name="GetNonDigitalLoanTypes_Result" message="tns:GetNonDigitalLoanTypes_Result"/>
        </operation>
        <operation name="CreateLoanAccountDigital">
            <input name="CreateLoanAccountDigital" message="tns:CreateLoanAccountDigital"/>
            <output name="CreateLoanAccountDigital_Result" message="tns:CreateLoanAccountDigital_Result"/>
        </operation>
        <operation name="GetDigitalLoanTypes">
            <input name="GetDigitalLoanTypes" message="tns:GetDigitalLoanTypes"/>
            <output name="GetDigitalLoanTypes_Result" message="tns:GetDigitalLoanTypes_Result"/>
        </operation>
        <operation name="CheckLoan">
            <input name="CheckLoan" message="tns:CheckLoan"/>
            <output name="CheckLoan_Result" message="tns:CheckLoan_Result"/>
        </operation>
        <operation name="SelfRegistration">
            <input name="SelfRegistration" message="tns:SelfRegistration"/>
            <output name="SelfRegistration_Result" message="tns:SelfRegistration_Result"/>
        </operation>
        <operation name="SelfRegistrationActivation">
            <input name="SelfRegistrationActivation" message="tns:SelfRegistrationActivation"/>
            <output name="SelfRegistrationActivation_Result" message="tns:SelfRegistrationActivation_Result"/>
        </operation>
        <operation name="GetMkopoLoanAccounts">
            <input name="GetMkopoLoanAccounts" message="tns:GetMkopoLoanAccounts"/>
            <output name="GetMkopoLoanAccounts_Result" message="tns:GetMkopoLoanAccounts_Result"/>
        </operation>
        <operation name="CreateLipaKwaSaccoCode">
            <input name="CreateLipaKwaSaccoCode" message="tns:CreateLipaKwaSaccoCode"/>
            <output name="CreateLipaKwaSaccoCode_Result" message="tns:CreateLipaKwaSaccoCode_Result"/>
        </operation>
        <operation name="ValidateFinancials">
            <input name="ValidateFinancials" message="tns:ValidateFinancials"/>
            <output name="ValidateFinancials_Result" message="tns:ValidateFinancials_Result"/>
        </operation>
        <operation name="FetchStatementTransactions">
            <input name="FetchStatementTransactions" message="tns:FetchStatementTransactions"/>
            <output name="FetchStatementTransactions_Result" message="tns:FetchStatementTransactions_Result"/>
        </operation>
        <operation name="ValidateMemberDetails">
            <input name="ValidateMemberDetails" message="tns:ValidateMemberDetails"/>
            <output name="ValidateMemberDetails_Result" message="tns:ValidateMemberDetails_Result"/>
        </operation>
        <operation name="CreateNominees">
            <input name="CreateNominees" message="tns:CreateNominees"/>
            <output name="CreateNominees_Result" message="tns:CreateNominees_Result"/>
        </operation>
        <operation name="GetMemberDetails">
            <input name="GetMemberDetails" message="tns:GetMemberDetails"/>
            <output name="GetMemberDetails_Result" message="tns:GetMemberDetails_Result"/>
        </operation>
        <operation name="GetLoanBalance1">
            <input name="GetLoanBalance1" message="tns:GetLoanBalance1"/>
            <output name="GetLoanBalance1_Result" message="tns:GetLoanBalance1_Result"/>
        </operation>
        <operation name="GetLoanAccountsGuaranteed">
            <input name="GetLoanAccountsGuaranteed" message="tns:GetLoanAccountsGuaranteed"/>
            <output name="GetLoanAccountsGuaranteed_Result" message="tns:GetLoanAccountsGuaranteed_Result"/>
        </operation>
        <operation name="KanjaRegistration">
            <input name="KanjaRegistration" message="tns:KanjaRegistration"/>
            <output name="KanjaRegistration_Result" message="tns:KanjaRegistration_Result"/>
        </operation>
    </portType>
    <binding name="MobileBanking_Binding" type="tns:MobileBanking_Port">
        <binding xmlns="http://schemas.xmlsoap.org/wsdl/soap/" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="SpotcashRegistration">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SpotcashRegistration" style="document"/>
            <input name="SpotcashRegistration">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SpotcashRegistration_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetSavingsAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetSavingsAccounts" style="document"/>
            <input name="GetSavingsAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetSavingsAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetAgentBalance">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetAgentBalance" style="document"/>
            <input name="GetAgentBalance">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetAgentBalance_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="Spotcash">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:Spotcash" style="document"/>
            <input name="Spotcash">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="Spotcash_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetServiceCharge">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetServiceCharge" style="document"/>
            <input name="GetServiceCharge">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetServiceCharge_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetShareCapitalAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetShareCapitalAccounts" style="document"/>
            <input name="GetShareCapitalAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetShareCapitalAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetLoanAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetLoanAccounts" style="document"/>
            <input name="GetLoanAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetLoanAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetNWDAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetNWDAccounts" style="document"/>
            <input name="GetNWDAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetNWDAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetOverdraftPaid">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetOverdraftPaid" style="document"/>
            <input name="GetOverdraftPaid">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetOverdraftPaid_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetOverdraftAmount">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetOverdraftAmount" style="document"/>
            <input name="GetOverdraftAmount">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetOverdraftAmount_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetOverdraftCharge">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetOverdraftCharge" style="document"/>
            <input name="GetOverdraftCharge">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetOverdraftCharge_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="MemberRegistration">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:MemberRegistration" style="document"/>
            <input name="MemberRegistration">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="MemberRegistration_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberAccounts" style="document"/>
            <input name="GetMemberAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMainSavingsAccount">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMainSavingsAccount" style="document"/>
            <input name="GetMainSavingsAccount">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMainSavingsAccount_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberEligibility">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberEligibility" style="document"/>
            <input name="GetMemberEligibility">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberEligibility_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ApplyLoan">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ApplyLoan" style="document"/>
            <input name="ApplyLoan">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ApplyLoan_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberImage">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberImage" style="document"/>
            <input name="GetMemberImage">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberImage_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ReferAFriend">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ReferAFriend" style="document"/>
            <input name="ReferAFriend">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ReferAFriend_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ProcessReversal">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ProcessReversal" style="document"/>
            <input name="ProcessReversal">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ProcessReversal_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GeteLoanTypes">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GeteLoanTypes" style="document"/>
            <input name="GeteLoanTypes">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GeteLoanTypes_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="IsMemberInArrears">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:IsMemberInArrears" style="document"/>
            <input name="IsMemberInArrears">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="IsMemberInArrears_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="IsMemberInArrearsExternalUse">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:IsMemberInArrearsExternalUse" style="document"/>
            <input name="IsMemberInArrearsExternalUse">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="IsMemberInArrearsExternalUse_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ValidateAccountDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateAccountDetails" style="document"/>
            <input name="ValidateAccountDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ValidateAccountDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ValidateAccountDetailsWithImageAMust">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateAccountDetailsWithImageAMust" style="document"/>
            <input name="ValidateAccountDetailsWithImageAMust">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ValidateAccountDetailsWithImageAMust_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="SpotcashRegistration22">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SpotcashRegistration22" style="document"/>
            <input name="SpotcashRegistration22">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SpotcashRegistration22_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CustomerLookup">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:CustomerLookup" style="document"/>
            <input name="CustomerLookup">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CustomerLookup_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="SpotcashRegistrationConfirmation">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SpotcashRegistrationConfirmation" style="document"/>
            <input name="SpotcashRegistrationConfirmation">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SpotcashRegistrationConfirmation_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="SpotcashRegistrationActivation">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SpotcashRegistrationActivation" style="document"/>
            <input name="SpotcashRegistrationActivation">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SpotcashRegistrationActivation_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberScore">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberScore" style="document"/>
            <input name="GetMemberScore">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberScore_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="AddLoanApplication">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:AddLoanApplication" style="document"/>
            <input name="AddLoanApplication">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="AddLoanApplication_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetGuarantorScore">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetGuarantorScore" style="document"/>
            <input name="GetGuarantorScore">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetGuarantorScore_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="AddGuarantors">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:AddGuarantors" style="document"/>
            <input name="AddGuarantors">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="AddGuarantors_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="AddGuarantorACK">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:AddGuarantorACK" style="document"/>
            <input name="AddGuarantorACK">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="AddGuarantorACK_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetNonDigitalLoanTypes">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetNonDigitalLoanTypes" style="document"/>
            <input name="GetNonDigitalLoanTypes">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetNonDigitalLoanTypes_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CreateLoanAccountDigital">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:CreateLoanAccountDigital" style="document"/>
            <input name="CreateLoanAccountDigital">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CreateLoanAccountDigital_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetDigitalLoanTypes">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetDigitalLoanTypes" style="document"/>
            <input name="GetDigitalLoanTypes">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetDigitalLoanTypes_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CheckLoan">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:CheckLoan" style="document"/>
            <input name="CheckLoan">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CheckLoan_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="SelfRegistration">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SelfRegistration" style="document"/>
            <input name="SelfRegistration">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SelfRegistration_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="SelfRegistrationActivation">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:SelfRegistrationActivation" style="document"/>
            <input name="SelfRegistrationActivation">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="SelfRegistrationActivation_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMkopoLoanAccounts">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMkopoLoanAccounts" style="document"/>
            <input name="GetMkopoLoanAccounts">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMkopoLoanAccounts_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CreateLipaKwaSaccoCode">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:CreateLipaKwaSaccoCode" style="document"/>
            <input name="CreateLipaKwaSaccoCode">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CreateLipaKwaSaccoCode_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ValidateFinancials">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateFinancials" style="document"/>
            <input name="ValidateFinancials">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ValidateFinancials_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="FetchStatementTransactions">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:FetchStatementTransactions" style="document"/>
            <input name="FetchStatementTransactions">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="FetchStatementTransactions_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="ValidateMemberDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateMemberDetails" style="document"/>
            <input name="ValidateMemberDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="ValidateMemberDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="CreateNominees">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:CreateNominees" style="document"/>
            <input name="CreateNominees">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="CreateNominees_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetMemberDetails">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberDetails" style="document"/>
            <input name="GetMemberDetails">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetMemberDetails_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetLoanBalance1">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetLoanBalance1" style="document"/>
            <input name="GetLoanBalance1">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetLoanBalance1_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="GetLoanAccountsGuaranteed">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetLoanAccountsGuaranteed" style="document"/>
            <input name="GetLoanAccountsGuaranteed">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="GetLoanAccountsGuaranteed_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
        <operation name="KanjaRegistration">
            <operation xmlns="http://schemas.xmlsoap.org/wsdl/soap/" soapAction="urn:microsoft-dynamics-schemas/codeunit/MobileBanking:KanjaRegistration" style="document"/>
            <input name="KanjaRegistration">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </input>
            <output name="KanjaRegistration_Result">
                <body xmlns="http://schemas.xmlsoap.org/wsdl/soap/" use="literal"/>
            </output>
        </operation>
    </binding>
    <service name="MobileBanking">
        <port name="MobileBanking_Port" binding="tns:MobileBanking_Binding">
            <address xmlns="http://schemas.xmlsoap.org/wsdl/soap/" location="http://nav-cbs.office.tangazoletu.com:7347/OLLIN/WS/Ollin/Codeunit/MobileBanking"/>
        </port>
    </service>
</definitions>