package com.tl.spotcash.agencybanking.threads;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.ResponseCodes;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.ReversalResponseMapper;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.service.SpotcashCbsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.math.BigInteger;
import java.util.Optional;

/**
 * Created by mwendwakelvin on 27/02/2020.
 */
public class ProcessReversalThread implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessReversalThread.class);

    SpTransTempTable trx, originalTransaction;

    Environment environment;

    CrudTransactionController crudTransactionController;

    SpotcashCbsService cbsService;

    public ProcessReversalThread(SpTransTempTable trx, Environment environment, CrudTransactionController crudTransactionController,
                                 SpotcashCbsService cbsService, SpTransTempTable originalTransaction) {
        this.trx = trx;
        this.environment = environment;
        this.crudTransactionController = crudTransactionController;
        this.cbsService = cbsService;
        this.originalTransaction = originalTransaction;
    }

    @Override
    public void run() {
        processReversal(trx, originalTransaction);
    }

    public void processReversal(SpTransTempTable trx, SpTransTempTable originalTransaction) {
        LOGGER.info("Initiating Reversal for TRX Id: "+trx.getTrxId()+" Retry Count: "+trx.getReverseCount());
        BigInteger count = new BigInteger("" + environment.getProperty("datasource.spotcash.reverseTransactionCount"));
        ReversalResponseMapper responseMapper = null;
        Optional<ReversalResponseMapper> optionalReversalResponseMapper = Optional.ofNullable(cbsService.reverseTransaction(trx, originalTransaction));
        if (optionalReversalResponseMapper.isPresent()) {
            responseMapper = optionalReversalResponseMapper.get();
        }
        LOGGER.info(" ---------------- CBS RESPONSE RECEIVED ---------------- ");
        if (!responseMapper.getResponseCode().trim().matches(ResponseCodes.SUCCESS.getResponseCode()) &&
                !responseMapper.getResponseCode().trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
            LOGGER.info(" ---------------- CBS RESPONSE FAIL ---------------- ");
            LOGGER.info(" ---------------- CBS RESPONSE ERROR ---------------- "+ responseMapper.getResponseMessage());
            if(responseMapper.getResponseCode().trim().matches(ResponseCodes.REVERSE_FAIL.getResponseCode()))
            {
                trx.setReverse(new BigInteger("2"));
                trx.setDescription("Transaction was Not Available for Reversal.");
                trx.setRespCode("00");
                trx.setTrxStatus(BigInteger.valueOf(2));
                trx.setCustomerName(originalTransaction.getCustomerName());
                trx.setMsisdn(originalTransaction.getMsisdn());
                crudTransactionController.updateTransTable(trx);
                LOGGER.info("Processing Reversal Request: "+trx.getTrxId()+" . Transaction was Not Available for Reversal.");
            }
            else
            {
                if((count.subtract(new BigInteger("1"))).equals((trx.getReverseCount().subtract(new BigInteger("1"))))){//Retries Over
                    trx.setReverse(new BigInteger("2"));
                    trx.setRespCode(com.tl.spotcash.agencybanking.custommodels.ResponseCodes.FAILED_REVERSIBLE_TRANSACTION.getResponsecode());
                    trx.setDescription("Error Processing Reversal Request. Max Retries Reached. Manual Intervention Required.");
                    trx.setTrxStatus(BigInteger.valueOf(4));
                    trx.setCustomerName(originalTransaction.getCustomerName());
                    trx.setMsisdn(originalTransaction.getMsisdn());
                    crudTransactionController.updateTransTable(trx);
                    LOGGER.info("Error Processing Reversal Request: "+trx.getTrxId()+" . Max Retries Reached. Manual Intervention Required.");
                }
                else {
                    trx.setReverse(new BigInteger("0"));
                    crudTransactionController.updateTransTable(trx);
                    LOGGER.info("Error Processing Reversal Request: "+trx.getTrxId());
                }
            }
        }
        else{
            trx.setReverse(new BigInteger("2"));
            trx.setRespCode("00");
            trx.setTrxStatus(BigInteger.valueOf(2));
            trx.setCustomerName(originalTransaction.getCustomerName());
            trx.setMsisdn(originalTransaction.getMsisdn());
            crudTransactionController.updateTransTable(trx);
            LOGGER.info("Reversal for TRX Id: "+trx.getTrxId()+" successfully processed.");
        }
    }
}
