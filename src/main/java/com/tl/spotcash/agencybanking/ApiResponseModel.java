package com.tl.spotcash.agencybanking;

/**
 *
 * <AUTHOR>
 */
public class ApiResponseModel {

    String responseheader_sc;
    String sd;
    String trx_id;
    String st;
    String responsedata_sc;
    String msm;
    String sod;
    String xid;
    String amt;
    String feeamt;
    String reason;
    String eod;
    String httpStatus;
    String transaction_type;
    String agent_id;
    Object transactionData;
    private Object utilityPaymentResponse;
    String trx_balance;

    public String getResponseheader_sc() {
        return responseheader_sc;
    }

    public void setResponseheader_sc(String responseheader_sc) {
        this.responseheader_sc = responseheader_sc;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getTrx_id() {
        return trx_id;
    }

    public void setTrx_id(String trx_id) {
        this.trx_id = trx_id;
    }

    public String getSt() {
        return st;
    }

    public void setSt(String st) {
        this.st = st;
    }

    public String getResponsedata_sc() {
        return responsedata_sc;
    }

    public void setResponsedata_sc(String responsedata_sc) {
        this.responsedata_sc = responsedata_sc;
    }

    public String getMsm() {
        return msm;
    }

    public void setMsm(String msm) {
        this.msm = msm;
    }

    public String getSod() {
        return sod;
    }

    public void setSod(String sod) {
        this.sod = sod;
    }

    public String getXid() {
        return xid;
    }

    public void setXid(String xid) {
        this.xid = xid;
    }

    public String getAmt() {
        return amt;
    }

    public void setAmt(String amt) {
        this.amt = amt;
    }

    public String getFeeamt() {
        return feeamt;
    }

    public void setFeeamt(String feeamt) {
        this.feeamt = feeamt;
    }

    public String getEod() {
        return eod;
    }

    public void setEod(String eod) {
        this.eod = eod;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public Object getTransactionData() {
        return transactionData;
    }

    public void setTransactionData(Object transactionData) {
        this.transactionData = transactionData;
    }

    public String getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(String httpStatus) {
        this.httpStatus = httpStatus;
    }

    public Object getUtilityPaymentResponse() {
        return utilityPaymentResponse;
    }

    public void setUtilityPaymentResponse(Object utilityPaymentResponse) {
        this.utilityPaymentResponse = utilityPaymentResponse;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAgent_id() {
        return agent_id;
    }

    public void setAgent_id(String agent_id) {
        this.agent_id = agent_id;
    }

    public String getTrx_balance() {
        return trx_balance;
    }

    public void setTrx_balance(String trx_balance) {
        this.trx_balance = trx_balance;
    }
}
