package com.tl.spotcash.agencybanking.dao;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.spotcash.agencybanking.custommodels.SpotcashUtilities;
import com.tl.spotcash.agencybanking.custommodels.UtilityCodes;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.service.ErrorMessageService;
import com.tl.spotcash.agencybanking.service.OtpService;
import com.tl.spotcash.agencybanking.service.SpotcashserviceMap;
import com.tl.spotcash.agencybanking.utils.AesEncryption;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import org.hibernate.HibernateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.BadCredentialsException;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.sql.Date;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 */
public class Spotcashdaoimpl {

    SharedFunctions sharedFunctions = new SharedFunctions();

    private static final Logger LOGGER = LoggerFactory.getLogger(Spotcashdaoimpl.class);

    private CrudService databaseCrudService;
    private Environment environment;
    private ErrorMessageService errorMessageService;

    public String fetchBMSdata(String query) {
        String responseData = "";
        List<Object> results = databaseCrudService.fetchWithNativeQuerySec(query, Collections.EMPTY_MAP, 0, 10);
        for (int i = 0; i < results.size(); i++) {
            try {
                String resultString = (new ObjectMapper().writeValueAsString(results.get(i))).replace("[", "").replace("]", "");
                String[] result = resultString.split(",");
                responseData += result[1] + " ";
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return responseData;
    }

    //Fetch App Details
    public SpClientsAppConfigurations fetchAppDetails(BigInteger clientId) {
        List<SpClientsAppConfigurations> spClientsAppConfigurations = databaseCrudService.fetchWithHibernateQuery("Select r from SpClientsAppConfigurations r "
                + "where clientId= '" + clientId + "' and intrash='NO' and rownum<=1", Collections.EMPTY_MAP);
        return spClientsAppConfigurations.isEmpty() ? null : spClientsAppConfigurations.get(0);
    }

    //Fetch Message Templates
    public SpMsgTemplates fetchMessageTemplate(BigInteger clientId, String messageType){
        LOGGER.info("FETCHING Message Template "+ messageType+ " for client id "+clientId);
        List<SpMsgTemplates> messageTemplates = databaseCrudService.fetchWithHibernateQuery("Select m from SpMsgTemplates m where clientId = '" +clientId+"' and templateName = '"+messageType+"'",Collections.EMPTY_MAP);
        return messageTemplates.get(0);
    }


    public String fetchSpotcashdata(String query) {
        String responseData = "";
        List<Object> results = databaseCrudService.fetchWithNativeQuery(query, Collections.EMPTY_MAP, 0, 5);
        for (int i = 0; i < results.size(); i++) {
            try {
                String resultString = (new ObjectMapper().writeValueAsString(results.get(i))).replace("[", "").replace("]", "");
                String[] result = resultString.split(",");
                responseData += result[12] + " ";
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return responseData;
    }


    public Spotcashdaoimpl(Environment environment, CrudService databaseCrudService) {
        this.environment = environment;
        this.databaseCrudService = databaseCrudService;
    }

    public Spotcashdaoimpl(Environment environment, CrudService databaseCrudService,
                           ErrorMessageService errorMessageService) {
        this.environment = environment;
        this.databaseCrudService = databaseCrudService;
        this.errorMessageService = errorMessageService;
    }

    public BigDecimal createStageTransactions(SpTransTempTable tempTransactionsData) {
        Object entityId = databaseCrudService.save(tempTransactionsData);
        BigDecimal id = tempTransactionsData.getId();
        return id;
    }

    public BigDecimal createAgencyTransaction(SpAgencyTransactions agencyTransactionsData) {
        Object entityId = databaseCrudService.save(agencyTransactionsData);
        BigDecimal id = agencyTransactionsData.getId();
        return id;
    }

    public BigDecimal createAccountingEntries(SpAccountingEntries accountEntryData) {
        Object objectId = databaseCrudService.save(accountEntryData);
        BigDecimal id = accountEntryData.getId();
        return id;
    }


    public BigDecimal insertUpdateAccounts(SpAccounts accountsData) {
        Object objectId = databaseCrudService.save(accountsData);
        BigDecimal id = accountsData.getId();
        return id;
    }


    public BigDecimal insertPaymentRecords(SpMpesaRecords paymentData) {
        Object objectId = databaseCrudService.save(paymentData);
        BigDecimal id = paymentData.getId();
        return id;
    }


    public BigDecimal insertRequestsLogs(SpRequestLogs logsData) {
        Object objectId = databaseCrudService.save(logsData);
        BigDecimal id = logsData.getId();
        return id;
    }


    public List<SpTariff> fetchTariffs(BigDecimal id) {
        LOGGER.info("Tarrif  query here>>>>>>>>>");
        List<SpTariff> thetariff = databaseCrudService.fetchWithHibernateQuery("select r from SpTariff r where id='" + id + "'", Collections.EMPTY_MAP);
        LOGGER.info("List of tariff data" + thetariff);

        return thetariff;
    }



    public List<SpTariffDetails> fetchTariffDetails(String tariffId, BigDecimal amount) {
        LOGGER.info("------ tariff id " + tariffId + " <<>> amount " + amount);
        List<SpTariffDetails> thetariffdetail
                = databaseCrudService.fetchWithHibernateQuery("Select r from SpTariffDetails r "
                + "where tariffId='" + tariffId + "' and amount>=" + amount + " order by amount asc ", Collections.EMPTY_MAP);
        return thetariffdetail;
    }






    public List<SpAgencyTariffDetails> fetchAgencyTariffDetails(String tariffId, BigDecimal amount, String tariffType) {
        List<SpAgencyTariffDetails> tariffDetails = new ArrayList<>();
        LOGGER.info("Incoming Tariff Type >>>>>>>>>" + tariffType);
        if (tariffType.equalsIgnoreCase("PERCENTAGE")) {//Percentage Tariff
            List<SpAgencyTariffDetails> thetariffs = databaseCrudService.fetchWithHibernateQuery("select r from SpAgencyTariffDetails r where agencyTariffId='" + tariffId + "'", Collections.EMPTY_MAP);
            SpAgencyTariffDetails agencyTariffDetails = thetariffs.get(0);

            BigDecimal spotcashCommission = agencyTariffDetails.getSpotcashComission().multiply(agencyTariffDetails.getAmount());
            LOGGER.info("Percentage Spotcash Commission >>>>>>>>>" + agencyTariffDetails.getSpotcashComission());
            LOGGER.info("Tariff Amount >>>>>>>>>" + agencyTariffDetails.getAmount());
            LOGGER.info("Computed Spotcash Commission >>>>>>>>>" + spotcashCommission);
            agencyTariffDetails.setSpotcashComission(spotcashCommission);
            tariffDetails.add(agencyTariffDetails);
            return tariffDetails;
        } else {
            tariffDetails
                    = databaseCrudService.fetchWithHibernateQuery("select r from SpAgencyTariffDetails r "
                    + "where agencyTariffId='" + tariffId + "' and amount>=" + amount + " order by amount asc", Collections.EMPTY_MAP);
            LOGGER.info("List of tariff detail data" + tariffDetails);
            List<SpAgencyTariffDetails> newList = new ArrayList<>();
            newList.add(tariffDetails.get(0));
            return newList;
        }
    }


    public String fetchsetLastTransactionId() {
        List<SpTrxidSeq> lastTransactionId = databaseCrudService.fetchWithHibernateQuery("select r from SpTrxidSeq r order by id asc", Collections.EMPTY_MAP);
        LOGGER.info("The list is " + lastTransactionId);
        String transactionId = null;
        for (SpTrxidSeq results : lastTransactionId) {
            transactionId = results.getAbCurrTxnId();
        }
        return transactionId;
    }


    public String updateLastTransactionId(String trxId) {
        final String lastTrxId = "from SpTrxidSeq s";
        List<SpTrxidSeq> spTrxidSeqs = databaseCrudService.fetchWithHibernateQuery(lastTrxId, Collections.EMPTY_MAP);
        SpTrxidSeq e = spTrxidSeqs.get(0);
        e.setAbCurrTxnId(trxId);
        databaseCrudService.saveOrUpdate(e);
        String theupdatedTrxid = e.getAbCurrTxnId();
        return theupdatedTrxid;
    }


    public SpClients fetchClientType(BigDecimal clientId) {
        SpClients spClient = databaseCrudService.findEntity(clientId, SpClients.class);
        return spClient;
    }


    public List<SpTransTempTable> checkTransactionByStatus(BigInteger status) {
        List<SpTransTempTable> transaction = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r where trxStatus in ('0','1','2','3','4')", Collections.EMPTY_MAP);
        return transaction;
    }


    public BigDecimal updateTransactionStatus(BigDecimal theid, BigInteger status) {
        SpTransTempTable e = databaseCrudService.findEntity(theid, SpTransTempTable.class);
        e.setTrxStatus(status);
        databaseCrudService.saveOrUpdate(e);
        BigDecimal theupdateid = e.getId();
        return theupdateid;
    }


    public String updateTempTableTransactionStatus(SpTransTempTable updateData) {
        try {
            String whereClause = " where 1=1 ";
            if (updateData.getId() == new BigDecimal("99")) {
                whereClause += "and trx_id= '" + updateData.getOriginalTxnId() + "'";
            } else {
                whereClause += "and id= '" + updateData.getId() + "'";
            }
            List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r " + whereClause, Collections.EMPTY_MAP);
            SpTransTempTable e = spTransTempTables.get(0);
            e.setTrxStatus(updateData.getTrxStatus());
            e.setRespCode(updateData.getRespCode());
            e.setDescription(updateData.getDescription());
            e.setCbsDocNo(updateData.getCbsDocNo());
            e.setNotifyCbs(updateData.getNotifyCbs());
            e.setOtherFields(updateData.getOtherFields());
            if (null != updateData.getMsisdn()) {
                e.setMsisdn(updateData.getMsisdn());
            }
            if (null != updateData.getCustBal()) {
                e.setCustBal(updateData.getCustBal());
            } else {
                e.setCustBal(new BigDecimal("00"));
            }
            if (null != updateData.getIntermediateStatus()) {
                e.setIntermediateStatus(updateData.getIntermediateStatus());
            }
            if (updateData.getToAccount() != null) {
                e.setToAccount(updateData.getToAccount());
            } else {
                e.setToAccount(""); // or skip / log / leave null
            }

            if (updateData.getPaymentReference() != null) {
                e.setPaymentReference(updateData.getPaymentReference());
            } else {
                e.setPaymentReference(""); // or skip / log / leave null
            }

            databaseCrudService.saveOrUpdate(e);
            String theupdateresponseid = "";
            if ("00".equals(updateData.getRespCode())) {
                theupdateresponseid = e.getId().toString() + "|" + updateData.getOriginalTxnId();
            } else { //
                theupdateresponseid = "0" + "|" + updateData.getOriginalTxnId();
            }
            return theupdateresponseid;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("COULD NOT UPDATE DATA >> ERROR IS " + e.getMessage());
            return null;
        }
    }


    public void updateTransTempTable(SpTransTempTable trx) {
        databaseCrudService.saveOrUpdate(trx);
    }

    /**
     * Record Agency Banking Transaction from SP_TRANS_TEMP_TABLE Record
     *
     * @param updateData TransTempTable Object
     * @return
     */

    public String createAgencyTransaction(SpTransTempTable updateData) {
        try {
            BigDecimal return_id = null;
            String whereClause = " where 1=1 ";
            if (updateData.getId() == new BigDecimal("99")) {
                whereClause += "and trx_id= '" + updateData.getOriginalTxnId() + "'";
            } else {
                whereClause += "and id= '" + updateData.getId() + "'";
            }
            List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r " + whereClause, Collections.EMPTY_MAP);
            SpTransTempTable transTempObj = spTransTempTables.isEmpty() ? null : spTransTempTables.get(0);

            if (transTempObj != null) {
                SpAgencyTransactions agencyTransactionObj = new SpAgencyTransactions();
                agencyTransactionObj.setTransId(transTempObj.getId());
                agencyTransactionObj.setTrxId(transTempObj.getTrxId());
                agencyTransactionObj.setClientId(transTempObj.getClientId());
                if (null != updateData.getMsisdn()) {
                    agencyTransactionObj.setMsisdn(updateData.getMsisdn());
                } else {
                    agencyTransactionObj.setMsisdn(transTempObj.getMsisdn());
                }
                agencyTransactionObj.setAmount(transTempObj.getAmount());
                agencyTransactionObj.setSpotcashComission(transTempObj.getSpotcashCommission());
                agencyTransactionObj.setThirdpartyCharge(transTempObj.getThirdpartyCharge());
                agencyTransactionObj.setTrxStatus(updateData.getTrxStatus());
                agencyTransactionObj.setRespCode(updateData.getRespCode());
                agencyTransactionObj.setDescription(updateData.getDescription());
                agencyTransactionObj.setCustomerName(transTempObj.getCustomerName());
                agencyTransactionObj.setRequestTime(transTempObj.getRequestTime());
                agencyTransactionObj.setTimeCompleted(transTempObj.getTimeCompleted());
                agencyTransactionObj.setAgentId(transTempObj.getAgentId());
                agencyTransactionObj.setAgentStoreId(transTempObj.getAgentStoreId());
                agencyTransactionObj.setServiceId(transTempObj.getServiceId());
                LOGGER.info("ABOUT TO INSERT AGENCY TRANSACTION OBJECT #### ");
                return_id = createAgencyTransaction(agencyTransactionObj);
                LOGGER.info("[TRANSACTION PROCESSORS. CREATE AGENCY TRANSACTION] >> " + return_id);
            }
            return return_id.toString();
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("COULD NOT CREATE AGENCY TRANSACTION >> ERROR IS " + e.getMessage());
            return null;
        }
    }


    public String updateTempTableStatus(BigInteger status, Map updateData) {
        Calendar currenttime = Calendar.getInstance();
        Date sqldate = new Date((currenttime.getTime()).getTime());
        try {
            List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery("Select r from SpTransTempTable r "
                    + "where id= '" + updateData.get("transtemptableid") + "' and msisdn= '" + updateData.get("msisdn") + "' ", Collections.EMPTY_MAP);
            SpTransTempTable e = spTransTempTables.get(0);
            e.setRespCode((String) updateData.get("response_code"));
            e.setTrxStatus(status);
            e.setDescription((String) updateData.get("desc"));
            e.setTimeCompleted(sqldate);
            databaseCrudService.saveOrUpdate(e);
            String theupdateresponseid = e.getId().toString();
            return theupdateresponseid;
        } catch (Exception e) {
            LOGGER.info("COULD NOT UPDATE DATA >> ERROR IS " + e.getMessage());
            return null;
        }
    }

    public boolean checkForDuplicateTransactions(HttpProcessorRequest requestData){
        try {
            String clientId = requestData.getHeader().getClientId();
            String msisdn = requestData.getHeader().getMsisdn();


            Class serviceMappingsClass = SpotcashserviceMap.class;
            Object serviceObj = serviceMappingsClass.newInstance();;
            String xipService = requestData.getRequestData().getTxnType();//SABE,SDT,SPS
            Method serviceMethod = serviceMappingsClass.getMethod("get" + xipService + "");
            String xip_service_id = (String) serviceMethod.invoke(serviceObj);

            String serviceId = xip_service_id;

            LOGGER.info("SERVICE PASSED " + xip_service_id.toUpperCase());
            switch (xip_service_id.toUpperCase().trim()) {
                case "AGBAL":
                    //SID = 59
                    serviceId = "59";
                    break;
                case "UTIL":
                    serviceId = UtilityCodes.valueOf(requestData.getRequestData().getUtilityKeyword()).getUtilityCode();
                    break;
                case "60"://NON MEMBER DEPOSIT REQUEST
                default:
                    LOGGER.info("....................in the default............");
                    //All Numeric transactions based on spotcash service IDs
                    //See Spotcash DB for reference on service ID definitions.
                        serviceId = xip_service_id;
                    break;
            }

            String query = "select * from sp_trans_temp_table where service_id = '" + serviceId
                    + "' and msisdn = '" + msisdn + "' and client_id='" + clientId
                    + "' and trx_status < 4 and REQUEST_TIME > sysdate - (" + environment.getRequiredProperty("datasource.spotcash.duplicateTimeDelay")
                    + " / 60) / (24 * 60)";
            //REQUEST_TIME > sysdate  - 40/(24*60)

            LOGGER.info("<><><> NATIVE QUERY FOR CHECKING DUPLICATE TRANSACTION RECORD :: {} <><><>", query);

            List<SpTransTempTable> duplicateRecord = databaseCrudService.fetchWithNativeQuery(query, Collections.EMPTY_MAP, 0, 1000);

            if (duplicateRecord.size() > 0){
                LOGGER.info("<><><>DUPLICATE TRANSACTION FOUND FOR MSISDN :: {}, SERVICE_ID:: {}, CLIENT_ID :: {} <><><>", msisdn, serviceId, clientId);
                LOGGER.info("<><><>DUPLICATE TRANSACTION RECORD :: {} <><><>", duplicateRecord.get(0));
                return true;
            } else{
                LOGGER.info("<><><>NO DUPLICATE TRANSACTION FOUND FOR MSISDN :: {}, SERVICE_ID:: {}, CLIENT_ID :: {} <><><>", msisdn, serviceId, clientId);
                return false;
            }
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            e.printStackTrace();
            LOGGER.error("<><><> ERROR FETCHING SERVICE ID :: {} <><><>", e.getLocalizedMessage());
            return false;
        }
    }

    public BigDecimal transtemptableid(String msisdn, String transactionId) {
        List<SpTransTempTable> transaction = databaseCrudService.fetchWithHibernateQuery("Select r from SpTransTempTable r "
                + "where trxId= '" + transactionId + "' and msisdn= '" + msisdn + "' ", Collections.EMPTY_MAP);

        BigDecimal theId = null;
        for (SpTransTempTable idData : transaction) {
            theId = idData.getId();
        }
        LOGGER.info("TRANS TEMP TABLE ID >> " + theId);
        return theId;

    }


    public SpServiceSubscriptions fetchServicesubscriptionData(BigInteger service_id, BigInteger client_id) {
        List<SpServiceSubscriptions> serviceSubscriptionses = databaseCrudService.fetchWithHibernateQuery("Select r from SpServiceSubscriptions r "
                + "where serviceId= '"+service_id+"'  and clientId= '" + client_id + "' ", Collections.EMPTY_MAP);
        return serviceSubscriptionses.isEmpty() ? null : serviceSubscriptionses.get(0);
    }

    //overloaded
    public SpServiceSubscriptions fetchServicesubscriptionData(String service_code, BigInteger client_id) {
        List<SpServiceSubscriptions> serviceSubscriptionses = databaseCrudService.fetchWithHibernateQuery("Select r from SpServiceSubscriptions r "
                + "where serviceId in (Select s.id from SpServices s where serviceCode= '"+service_code+"' ) and clientId= '" + client_id + "' and status=1 ", Collections.EMPTY_MAP);
        return serviceSubscriptionses.isEmpty() ? null : serviceSubscriptionses.get(0);
    }

    public SpXmlTemplates fetchXmlTemplate(String function_name, BigInteger client_id) {
        List<SpXmlTemplates> spXmlTemplates = databaseCrudService.fetchWithHibernateQuery("Select r from SpXmlTemplates r "
                + "where function_name= '"+function_name+"' and clientId= '" + client_id + "'", Collections.EMPTY_MAP);
        return spXmlTemplates.isEmpty() ? null : spXmlTemplates.get(0);
    }


    public SpServiceSubscriptions fetchServicesubscriptionData1(String service_code, BigInteger client_id) {
        List<SpServiceSubscriptions> serviceSubscriptionses = databaseCrudService.fetchWithHibernateQuery("Select r from SpServiceSubscriptions r "
                + "where serviceId in (Select s.id from SpServices s where serviceCode= '"+service_code+"' ) and clientId= '" + client_id + "' ", Collections.EMPTY_MAP);
        return serviceSubscriptionses.isEmpty() ? null : serviceSubscriptionses.get(0);
    }


    public SpAgencyTariff fetchAgencyTariffData(BigInteger service_id, BigInteger client_id) {
        List<SpAgencyTariff> agencyTariffs = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgencyTariff r "
                + "where serviceId= '" + service_id + "' and clientId= '" + client_id + "' and intrash='NO' ", Collections.EMPTY_MAP);
        return agencyTariffs.isEmpty() ? null : agencyTariffs.get(0);
    }


    public SpVpnclientsConfigs fetch_vpn_configs(BigInteger client_id) {
        List<SpVpnclientsConfigs> spVpnclientsConfigses = databaseCrudService.fetchWithHibernateQuery("Select r from SpVpnclientsConfigs r "
                + "where clientId= '" + client_id + "' and intrash='NO' ", Collections.EMPTY_MAP);
        return spVpnclientsConfigses.isEmpty() ? null : spVpnclientsConfigses.get(0);
    }


    public SpCustomers fetchCustomerDetails(String identifier, String identifierValue) {
        System.out.println(identifier + " ::: " + identifierValue);
        LOGGER.info("FETCHING CUSTOMER DETAILS USING IDENTIFIER " + identifier.toUpperCase() + " OF VALUE #" + identifierValue);
        List<SpCustomers> customerses = databaseCrudService.fetchWithHibernateQuery("Select r from SpCustomers r "
                + "where " + identifier + "= '" + identifierValue + "' ", Collections.EMPTY_MAP);
        return customerses.isEmpty() ? null : customerses.get(0);
    }

    public SpCustomers fetchCustomerDetailsNewCon(String identifier, String identifierValue) {
        System.out.println(identifier + " ::: " + identifierValue);
        LOGGER.info("FETCHING CUSTOMER DETAILS USING IDENTIFIER " + identifier.toUpperCase() + " OF VALUE #" + identifierValue);
        List<SpCustomers> customerses = databaseCrudService.fetchWithHibernateQueryNewCon("Select r from SpCustomers r "
                + "where " + identifier + "= '" + identifierValue + "' ", Collections.EMPTY_MAP);
        return customerses.isEmpty() ? null : customerses.get(0);
    }


    public SpAgentStores fetchAgentStoreData(String device_id, String msisdn) {
        LOGGER.info("GETTING AGENT STORES DATA#");
        LOGGER.info("DEVICE ID PASSED # " + device_id);
        LOGGER.info("DEVICE MSISDN PASSED # " + msisdn);
        List<SpAgentStores> agentStoreses = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgentStores r "
                + "where deviceId= '" + device_id + "' ", Collections.EMPTY_MAP);
        return agentStoreses.isEmpty() ? null : agentStoreses.get(0);
    }


    public SpAgentStores fetchAgentStoreDataWithMsisdnAndPin(String msisdn, String pin) {
        LOGGER.info("Agent device msisdn | " + msisdn);
        LOGGER.info("Agent pin passed    | " + pin);
        String hashedPassword = new SpotcashUtilities().md5password(pin);
        LOGGER.info("Hashed password     | " + hashedPassword);
        List<SpAgentStores> agentStoreses = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgentStores r "
                + "where contactMsisdn= '" + msisdn + "' and pin= '" + hashedPassword + "' and intrash='NO' ", Collections.EMPTY_MAP);
        try {
            SpAgentStores agentStore = agentStoreses.isEmpty() ? null : agentStoreses.get(0);
            if (agentStore != null) {
                LOGGER.info("Agent store result| " + agentStore.getAgentStoreName());
                return agentStore;
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error executing query " + e.getMessage());
        }
        return null;
    }


    public SpStoreUsers fetchAgentStoreUserDataWithMsisdnAndPin(String msisdn, String pin) {
        LOGGER.info("Agent device msisdn | " + msisdn);
        LOGGER.info("Agent pin passed    | " + pin);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where contactMsisdn= '" + msisdn + "' and intrash='NO' and activeStatus= 1 ", Collections.EMPTY_MAP);

        String loaded_password = null;
        SpStoreUsers agentStoreUser = null;
        for (SpStoreUsers passwordCheckobj : storeData) {
            loaded_password = passwordCheckobj.getPin();
            agentStoreUser = passwordCheckobj;
        }
        LOGGER.info("LOADED PIN is " + loaded_password);
        if (loaded_password != null) {
            String hashedPassword = new SpotcashUtilities().md5password(pin);
            LOGGER.info("Hashed password     | " + hashedPassword);
            if (loaded_password.equals(hashedPassword)) {
                LOGGER.info("Agent store user result| " + agentStoreUser.getContactName());
                return agentStoreUser;
            } else {
                LOGGER.warn("AGENT PIN INPUT DOESN'T MATCH THE ONE ON DB FOR AGENT WITH PHONE " + msisdn);
                return null;
            }
        } else {
            LOGGER.warn("AGENT PIN HAS NOT BEEN FOUND FROM DB FOR AGENT WITH PHONE " + msisdn);
            return null;
        }
    }

    public SpStoreUsers fetchAgentStoreUserDataWithAgentStoreId(String agentStoreId) {
        LOGGER.info("Agent_Store_Id | " + agentStoreId);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where agentStoreId= " + agentStoreId + " and intrash='NO' and activeStatus= 1 ", Collections.EMPTY_MAP);

        return storeData.isEmpty() ? null : storeData.get(0);
    }

    public SpStoreUsers fetchAgentStoreUserDataWithMsisdn(String msisdn) {
        LOGGER.info("Agent device msisdn | " + msisdn);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where contactMsisdn= '" + msisdn + "' and intrash='NO' ", Collections.EMPTY_MAP);
        return storeData.isEmpty() ? null : storeData.get(0);
    }


    public SpAgentStores fetchAgentStoreConfiguration(String clientId) {
        LOGGER.info("GETTING AGENT STORES DATA#");
        LOGGER.info("DEVICE ID PASSED # " + clientId);
        List<SpAgentStores> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgentStores r "
                + "where clientId= '" + clientId + "' and activeStatus= 1 ", Collections.EMPTY_MAP);
        return storeData.isEmpty() ? null : storeData.get(0);
    }


    public List<SpTransTempTable> fetchTransTempTableData(String agent_id, String store_id, String clientmsisdn, String trxId, Integer numberofTransactions) {
        String whereClause = " where 1=1 ";
        if (trxId != null) {
            whereClause += " and trxId='" + trxId + "' and rownum<=1 ";
            LOGGER.info("REQUEST BY TRX ID TRANSACTION DETAILS " + trxId);
        } else {
            LOGGER.info("agent ID to fetch on TransTable-> " + agent_id);
            LOGGER.info("agent store ID to fetch on TransTable-> " + store_id);
            whereClause += " and agentId='" + agent_id + "'and agentStoreId='" + store_id + "' and rownum<=" + numberofTransactions + " order by id desc ";
            LOGGER.info("5 FIVE TRANSACTION DETAILS REQUESTED");
        }
        LOGGER.info("GETTING TRANS TEMP TABLE DATA #");
        List<SpTransTempTable> stores = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r " + whereClause, Collections.EMPTY_MAP);
        return stores;
    }


    public SpServices getserviceName(String service_id) {
        return databaseCrudService.findEntity(new BigDecimal(service_id), SpServices.class);
    }

    public boolean authenticateAgent(String agentPin, String agent_id, String deviceId) {
        List<SpAgentStores> serviceData = databaseCrudService.fetchWithHibernateQuery("select r from SpAgentStores r"
                + " agentStoreId='" + agent_id + "' and deviceId='" + deviceId + "' ", Collections.EMPTY_MAP);
        String loaded_password = null;
        for (SpAgentStores passwordCheckobj : serviceData) {
            loaded_password = passwordCheckobj.getPin();
        }
        LOGGER.info("LOADED PIN is " + loaded_password);
        if (loaded_password != null) {
            LOGGER.info("AGENT PIN FETCHED FROM DB     " + loaded_password);
            LOGGER.info("AGENT PIN HASHED FROM REQUEST " + new SpotcashUtilities().md5password(agentPin));
            if (loaded_password.equals(new SpotcashUtilities().md5password(agentPin))) {
                return true;
            } else {
                return false;
            }
        } else {
            LOGGER.warn("AGENT PIN HAS NOT BEEN FOUND FROM DB FOR AGENT WITH ID " + agent_id + "");
            return false;
        }
    }

    public boolean authenticateAgent(String agentPin, String agent_id, String user_id, String deviceId) {
        String loaded_password = null;
        Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(fetchStoreUserWithId(new BigInteger(user_id), deviceId));
        if (optionalStoreUser.isPresent()) {
            SpStoreUsers spStoreUser = optionalStoreUser.get();
            loaded_password = spStoreUser.getPin();
        }
        LOGGER.info("LOADED PIN is " + loaded_password);
        if (loaded_password != null) {
            LOGGER.info("AGENT PIN FETCHED FROM DB     " + loaded_password);
            LOGGER.info("AGENT PIN HASHED FROM REQUEST " + new SpotcashUtilities().md5password(agentPin));
            if (loaded_password.equals(new SpotcashUtilities().md5password(agentPin))) {
                return true;
            } else {
                return false;
            }
        } else {
            LOGGER.warn("AGENT PIN HAS NOT BEEN FOUND FROM DB FOR AGENT USER WITH ID " + user_id + " AND AGENT ID " + agent_id);
            return false;
        }
    }


    public boolean authenticateCustomer(String custPin, String customer_id) {
        List<SpCustomers> serviceData = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " id='" + customer_id + "' ", Collections.EMPTY_MAP);

        String loaded_password = null;
        for (SpCustomers passwordCheckobj : serviceData) {
            loaded_password = passwordCheckobj.getPinNo();
        }
        if (loaded_password != null) {
            LOGGER.info("CUSTOMER PIN LOADED FROM DB   " + loaded_password);
            LOGGER.info("CUSTOMER PIN HASHED FROM REQUEST " + new SpotcashUtilities().md5password(custPin));
            if (loaded_password.equals(new SpotcashUtilities().md5password(custPin))) {

                LOGGER.info("CUSTOMER AUTHENTICATED SUCCESSFULLY");
                return true;
            } else {
                LOGGER.info("CUSTOMER DID NOT AUTHENTICATED SUCCESSFULLY");
                return false;
            }
        } else {
            LOGGER.warn("CUSTOMER PIN HAS NOT BEEN FOUND FROM DB FOR CUSTOMER WITH ID " + customer_id + "");
            return false;
        }
    }


    public SpAccounts accountInfo(String agent_id, String agentStore_id) {
        LOGGER.info("GETTING ACCOUNTS INFO DATA #");
        List<SpAccounts> spAccounts = databaseCrudService.fetchWithHibernateQuery("select r from SpAccounts r"
                + " agentId='" + agent_id + "' and agentStoreId='" + agentStore_id + "' ", Collections.EMPTY_MAP);
        return spAccounts.isEmpty() ? null : spAccounts.get(0);
    }


    public List<SpAirtimeVouchers> getVoucherbyDenomination(String amount, String agent_id, String agent_store_id) {
        Calendar currenttime = Calendar.getInstance();
        Date sqldate = new Date((currenttime.getTime()).getTime());
        List<SpAirtimeVouchers> airtimeData = databaseCrudService.fetchWithHibernateQuery("select r from SpAccounts r"
                + " amount='" + amount + "' and trxStatus='0' and agentId='" + agent_id + "' and agentStoreId='" + agent_store_id + "' ", Collections.EMPTY_MAP);

        BigDecimal theid = null;
        for (SpAirtimeVouchers spAirtimeVouchersData : airtimeData) {
            theid = spAirtimeVouchersData.getId();
        }
        if (theid != null) {
            SpAirtimeVouchers e = airtimeData.get(0);
            e.setTrxStatus(BigInteger.ONE);
            e.setPurchaseOn(sqldate);
            databaseCrudService.saveOrUpdate(e);
            BigDecimal theupdateid = e.getId();
            if (theupdateid.compareTo(BigDecimal.ZERO) > 0) {
                return airtimeData;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }


    public SpTransResponseTable insertAgencyMsgs(SpTransResponseTable msgsObj) {
        try {
            databaseCrudService.save(msgsObj);
            return msgsObj;
        } catch (Exception e) {
            LOGGER.error("There was an error persisting gancy messages " + e.toString());
            return null;
        }
    }
    public SpMessages insertAgentMsgs(SpMessages msgsObj) {
        try {
            databaseCrudService.save(msgsObj);
            return msgsObj;
        } catch (Exception e) {
            LOGGER.error("There was an error persisting gancy messages " + e.toString());
            return null;
        }
    }


    public List<ChargeableAccounts> chargeableAccounts(BigInteger service_id) {
        LOGGER.info("getting the chargable accounts of "+service_id);
        List<ChargeableAccounts> results = databaseCrudService.fetchWithNativeQuery("SELECT FIELD as transactionField,DR_PARTY as drParty ,"
                + "CR_PARTY as crParty,(SELECT SP_COMM_AC_ID FROM SP_SERVICES WHERE ID=SERVICE_ID) as spCommAccId,"
                + "(SELECT IS_AMNT_CHARGEABLE FROM SP_SERVICES WHERE SP_SERVICES.ID=SERVICE_ID) as isAmountChargeable"
                + " FROM SP_ACTION_STEPS WHERE SERVICE_ID = '" + service_id + "'", Collections.EMPTY_MAP, 0, 1000);

        return results;
    }

    public List<Object[]> getChargeableAccounts(BigInteger service_id) {
        LOGGER.info("getting the chargable accounts of "+service_id);
        List<Object[]> results = databaseCrudService.fetchWithNativeQuery("SELECT FIELD as transactionField,DR_PARTY as drParty ,"
                + "CR_PARTY as crParty,(SELECT SP_COMM_AC_ID FROM SP_SERVICES WHERE ID=SERVICE_ID) as spCommAccId,"
                + "(SELECT IS_AMNT_CHARGEABLE FROM SP_SERVICES WHERE SP_SERVICES.ID=SERVICE_ID) as isAmountChargeable"
                + " FROM SP_ACTION_STEPS WHERE SERVICE_ID = '" + service_id + "'", Collections.EMPTY_MAP, 0, 1000);

        return results;
    }


    public Object returnSpotcashAccountId(ChargeableAccounts chargeableObject) {
        Object acc_id;
        String filter = null;
        String column = null;
        if ("SUPPLIER".equals(chargeableObject.getTRXPARTY()) && chargeableObject.getAGENTSTOREID() < 1) {
            return 0;
        }
        if (null != chargeableObject.getTRXPARTY()) {
            switch (chargeableObject.getTRXPARTY()) {
                case "PLATFORM":
                    return chargeableObject.getSPCOMMACCID();
                case "SUPPLIER":
                    filter = "AGENT_STORE_ID = '" + chargeableObject.getAGENTSTOREID() + "'";
                    break;
                default:
                    filter = "CLIENT_ID = '" + chargeableObject.getCLIENTID() + "'";
                    break;
            }
        }

        if (null != chargeableObject.getTRANSACTIONFIELD()) {
            switch (chargeableObject.getTRANSACTIONFIELD()) {
                case "CHARGE":
                    column = "CHARGE_ACC_ID";
                    break;
                case "COMMISSION":
                    column = "COMM_ACC_ID";
                    break;
                default:
                    column = "AMOUNT_ACC_ID";
                    break;
            }
        }
        if (null != column && filter != null) {
            List<Object> result = databaseCrudService.fetchWithHibernateQuery("SELECT " + column + " FROM SP_SERVICE_SUBSCRIPTIONS WHERE " + filter + ""
                    + " AND SERVICE_ID = '" + chargeableObject.getSERVICEID() + "'", Collections.EMPTY_MAP, 0, 1000);

            acc_id = (Object) result.get(0);
            return acc_id;
        } else {
            return 0;
        }
    }


    public void updateAccountBalances(String queryAmountDebit, String queryAmountCredit,
                                      String queryCommDebit, String queryCommCredit,
                                      String queryChargeDebit, String queryChargeCredit) {
        if (null != queryAmountCredit) {
            databaseCrudService.executeNativeQuery(queryAmountCredit, Collections.EMPTY_MAP);
        }
        if (null != queryAmountDebit) {
            databaseCrudService.executeNativeQuery(queryAmountDebit, Collections.EMPTY_MAP);
        }
        if (null != queryCommDebit) {
            databaseCrudService.executeNativeQuery(queryCommDebit, Collections.EMPTY_MAP);
        }
        if (null != queryCommCredit) {
            databaseCrudService.executeNativeQuery(queryCommCredit, Collections.EMPTY_MAP);
        }
        if (null != queryChargeCredit) {
            databaseCrudService.executeNativeQuery(queryChargeCredit, Collections.EMPTY_MAP);
        }
        if (null != queryChargeDebit) {
            databaseCrudService.executeNativeQuery(queryChargeDebit, Collections.EMPTY_MAP);
        }
    }


    public void updateAccountBalances(String queryAmountDebit) {
        databaseCrudService.executeNativeQuery(queryAmountDebit, Collections.EMPTY_MAP);
    }


    public SpAgents fetchAgentWithId(String id) {
        LOGGER.info("FETCHING AGENT DETAILS USING AGENT ID " + id);
        List<SpAgents> spAgents = databaseCrudService.fetchWithHibernateQuery("select r from SpAgents r"
                + " where agentId='" + id + "' and intrash='NO' ", Collections.EMPTY_MAP);

        return spAgents.isEmpty() ? null : spAgents.get(0);
    }


    public Double getAgentBalance(String phoneNumber) {

        return null;
    }


    public SpCustomers fetchCustomerByPin(String pin, String nationalId, String clientid) {
        LOGGER.info("** HASHED PIN ** : " + new SpotcashUtilities().md5password(pin));
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and pinNo='" + new SpotcashUtilities().md5password(pin) + "' and clientId = '" + clientid + "' ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }

    public SpCustomers fetchCustomerByPinNewConn(String pin, String nationalId, String clientid) {
        LOGGER.info("** HASHED PIN ** : " + new SpotcashUtilities().md5password(pin));
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQueryNewCon("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and pinNo='" + new SpotcashUtilities().md5password(pin) + "'  and clientId = '" + clientid + "'  ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }

    //..................................................
    public SpCustomers fetchCustomerByPinNoClient(String pin, String nationalId) {
        LOGGER.info("** HASHED PIN ** : " + new SpotcashUtilities().md5password(pin));
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and pinNo='" + new SpotcashUtilities().md5password(pin) + "' ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }

    public SpCustomers fetchCustomerByPinNewConnNoClient(String pin, String nationalId) {
        LOGGER.info("** HASHED PIN ** : " + new SpotcashUtilities().md5password(pin));
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQueryNewCon("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and pinNo='" + new SpotcashUtilities().md5password(pin) + "'  ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }
    //..................................................

    public SpCustomers fetchCustomerByClientId(Long clientId, String nationalId) {
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and clientId='" + clientId + "' ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }
    public SpCustomers fetchSpCustomerByClientIdAndMsisdn(Long clientId, String msisdn) {
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where msisdn='" + msisdn + "' and clientId='" + clientId + "' ", Collections.EMPTY_MAP);

        return customers.isEmpty() ? null : customers.get(0);
    }

    public SpCustomers fetchCustomerByClientIdAndNationalId(Long clientId, String nationalId) {
        if (clientId != null) {
            return fetchCustomerByClientId(clientId, nationalId);
        } else {
            List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                    + " where nationalId='" + nationalId + "' ", Collections.EMPTY_MAP);
            return customers.isEmpty() ? null : customers.get(0);
        }
    }

    public SpCustomers fetchCustomerByClientIdAndMsisdn(Long clientId, String msisdn) {
        if (clientId != null) {
            return fetchSpCustomerByClientIdAndMsisdn(clientId, msisdn);
        } else {
            List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                    + " where msisdn='" + msisdn + "' ", Collections.EMPTY_MAP);
            return customers.isEmpty() ? null : customers.get(0);
        }
    }

    public SpCustomers fetchCustomerByNationalId(String nationalId) {
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' ", Collections.EMPTY_MAP);
        LOGGER.info("Customer records fetched for nationalId :: {} - {}", nationalId, customers.size());
        return customers.isEmpty() ? null : customers.get(0);
    }

    public SpCustomers fetchSubscribedCustomerByNationalId(String nationalId) {
        List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                + " where nationalId='" + nationalId + "' and subscriptionStatus = 1", Collections.EMPTY_MAP);
        LOGGER.info("Customer records fetched for nationalId :: {} - {} -", nationalId, customers.size());
        return customers.isEmpty() ? null : customers.get(0);
    }


    public void saveCustomerFingerprint(String nationalId, byte[] fingerprint) {
        try {
            List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                    + " nationalId='" + nationalId + "' ", Collections.EMPTY_MAP);

            SpCustomers spCustomer = customers.isEmpty() ? null : customers.get(0);
            spCustomer.setFingerprint(fingerprint);
            databaseCrudService.saveOrUpdate(spCustomer);
            LOGGER.info("FINGERPRINT SAVED SUCCESSFULY" + nationalId);

        } catch (Exception erro) {
            erro.printStackTrace();
        }

    }


    public SpAgents fetchAgentDetails(BigInteger clientId) {
        LOGGER.info("FETCHING AGENT DETAILS USING CLIENT ID " + clientId);
        List<SpAgents> spAgents = databaseCrudService.fetchWithHibernateQuery("select r from SpAgents r where "
                + " clientId='" + clientId + "' and inTrash='NO' ", Collections.EMPTY_MAP);

        return spAgents.isEmpty() ? null : spAgents.get(0);
    }


    public List<SpAgentAuthentication> fetchAuthData(BigInteger agentId) {
        LOGGER.info("Agent Authentication  query here>>>>>>>>>");
        List<SpAgentAuthentication> authType = databaseCrudService.fetchWithHibernateQuery("select r from SpAgentAuthentication r"
                + " where agentId='" + agentId + "' ", Collections.EMPTY_MAP);
        LOGGER.info("List of auth type data" + authType);
        return authType;
    }


    public SpAgentStores fetchAgentWithStoreId(BigInteger agentStoreId) {
        LOGGER.info("FETCHING AGENT STORE DETAILS USING ID " + agentStoreId);
        List<SpAgentStores> agentStores = databaseCrudService.fetchWithHibernateQuery("select r from SpAgentStores r"
                + " where agentStoreId='" + agentStoreId + "' and intrash='NO' ", Collections.EMPTY_MAP);

        return agentStores.isEmpty() ? null : agentStores.get(0);
    }


    public SpStoreUsers fetchStoreUserWithId(BigInteger user_id, String deviceId) {
        LOGGER.info("FETCHING AGENT STORE USER DETAILS USING ID " + user_id + " Device ID => " + deviceId);
        try {
            List<SpStoreUsers> agentStoresUsers = databaseCrudService.fetchWithHibernateQuery("select r from SpStoreUsers r"
                    + " where storeUserId='" + user_id + "' ", Collections.EMPTY_MAP);
            BigInteger agentStoreId = agentStoresUsers.get(0).getAgentStoreId();
            List<SpAgentStores> agentStores = databaseCrudService.fetchWithHibernateQuery("select r from SpAgentStores r"
                    + " where agentStoreId='" + agentStoreId + "' and deviceId='" + deviceId + "' and intrash='NO' ", Collections.EMPTY_MAP);
            return agentStores.isEmpty() ? null : agentStoresUsers.get(0);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error executing query " + e.getMessage());
        }
        return null;
    }

    public SpStoreUsers fetchStoreUserWithId(String storeUserId) {
        LOGGER.info("FETCHING AGENT STORE USER DETAILS USING ID " + storeUserId);
        try {
            List<SpStoreUsers> agentStoresUsers = databaseCrudService.fetchWithHibernateQuery("select r from SpStoreUsers r"
                    + " where storeUserId='" + storeUserId + "' ", Collections.EMPTY_MAP);

//            BigInteger agentStoreId = agentStoresUsers.get(0).getAgentStoreId();
//            List<SpAgentStores> agentStores = databaseCrudService.fetchWithHibernateQuery("select r from SpAgentStores r"
//                    + " where agentStoreId='" + agentStoreId + "' and deviceId='" + deviceId + "' and intrash='NO' ", Collections.EMPTY_MAP);
//            return agentStores.isEmpty() ? null : agentStoresUsers.get(0);

            return agentStoresUsers.isEmpty() ? null : agentStoresUsers.get(0);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error executing query " + e.getMessage());
        }
        return null;
    }

    public String resetAgentStorePin(String msisdn) {
        LOGGER.info("Agent device msisdn | " + msisdn);
        SpStoreUsers originalAgentStoreUser = fetchAgentStoreUserDataWithMsisdn(msisdn);
        String pin = null;
        try {
            pin = OtpService.generatePin();
            if (originalAgentStoreUser != null) {
                String smsUrl = environment.getRequiredProperty("datasource.spotcash.smsSendingUrl");
                Optional<SpAgents> optionalAgent = Optional.ofNullable(fetchAgentDetails(originalAgentStoreUser.getClientId()));
                if (optionalAgent.isPresent()) {
                    SpAgents spAgent = optionalAgent.get();
                    if (spAgent.getSmsUrl() != null) {
                        if (!spAgent.getSmsUrl().equals("")) {
                            smsUrl = spAgent.getSmsUrl();
                        }
                    }
                }
                String msg = "" + smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode("Dear agent, your SpotCash Agency Banking PIN has been reset. Your new secret PIN is: " + pin, "UTF-8");
                if (sharedFunctions.makeGetRequest(msg).contains("Success")) {
                    LOGGER.info("SMS Sent Successfully");
                    String hashedPassword = new SpotcashUtilities().md5password(pin);
                    databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN='" + hashedPassword + "' "
                            + " where CONTACT_MSISDN='" + msisdn + "' ", Collections.EMPTY_MAP);
                } else {
                    LOGGER.info("SMS Sending Failed");
                    pin = null;
                }
            } else {
                pin = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error executing query " + e.getMessage());
        }
        return pin;
    }


    public ArrayList<Map<String, Object>> updateAgentStorePin(String msisdn, String pin) {
        ArrayList<Map<String, Object>> respList = new ArrayList<>();
        try {
            LOGGER.info("Pin" + pin);
            LOGGER.info("Msisdn" + msisdn);
            String hashedPassword = new SpotcashUtilities().md5password(pin);
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN='" + hashedPassword + "' "
                    + " where CONTACT_MSISDN='" + msisdn + "' ", Collections.EMPTY_MAP);
            respList.add(trxnResponse("01", "26"));
            LOGGER.info("respList {}", new ObjectMapper().writeValueAsString(respList));

        } catch (Exception ex) {
            LOGGER.error("Error Occured {}", ex.getMessage(), ex);
        }
        return respList;
    }

    public Map<String, Object> updateAgentStorePinOld(String msisdn, String pin) {
        ArrayList<Map<String, Object>> respList = new ArrayList<>();
        try {
            LOGGER.info("Pin" + pin);
            LOGGER.info("Msisdn" + msisdn);
            String hashedPassword = new SpotcashUtilities().md5password(pin);
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN='" + hashedPassword + "' "
                    + " where CONTACT_MSISDN='" + msisdn + "' ", Collections.EMPTY_MAP);
            System.out.println("...............................pin change......................");
            respList.add(trxnResponse("00", "26"));
            LOGGER.info("respList {}", new ObjectMapper().writeValueAsString(respList));

        } catch (Exception ex) {
            LOGGER.error("Error Occured {}", ex.getMessage(), ex);
        }
        return respList.get(0);
    }


    public Map<String, Object> trxnResponse(String code, String respCode) {
        Map<String, Object> respMap = new HashMap<>();
        respMap.put("code", code);
        respMap.put("response_description", getMessageResponse(respCode).getMessage());
        return respMap;
    }

    /*
     * Message Description from Response Code
     */
    public SpSystemMessages getMessageResponse(String respCode) {
        List<SpSystemMessages> spSystemMessages = databaseCrudService.fetchWithHibernateQuery("select r from SpSystemMessages r where responseCode='" + respCode + "' ", Collections.EMPTY_MAP);
        return spSystemMessages.isEmpty() ? null : spSystemMessages.get(0);
    }


    public SpTransTempTable getTransaction(String trxId) {
        List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r where trxId='" + trxId + "' ", Collections.EMPTY_MAP);
        return spTransTempTables.isEmpty() ? null : spTransTempTables.get(0);
    }
    public SpTransTempTable getTransactionById(String trxId) {
        List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery("select r from SpTransTempTable r where id='" + trxId + "' ", Collections.EMPTY_MAP);
        return spTransTempTables.isEmpty() ? null : spTransTempTables.get(0);
    }


    public BigDecimal updateTransTmpTable(SpTransTempTable tempTransactionsData) {
        databaseCrudService.saveOrUpdate(tempTransactionsData);
        BigDecimal id = tempTransactionsData.getId();
        return id;
    }


    public ArrayList<SpAgencyReverseTransactions> fetchReverseTransactions(BigInteger count) {
        //LOGGER.info("Fetch reversal transactions with status 0");
        List<SpAgencyReverseTransactions> records = databaseCrudService.fetchWithHibernateQuery("select r from SpAgencyReverseTransactions r where reverseCount <=" + count, Collections.EMPTY_MAP);
        //LOGGER.info("Reversal Records found are: \n" + records.size());
        return (ArrayList<SpAgencyReverseTransactions>) records;
    }

    public ArrayList<SpAgencyPendingTransactions> fetchPendingTransactions() {
        List<SpAgencyPendingTransactions> pendingTransactions = databaseCrudService.fetchWithNamedQuery("SpAgencyPendingTransactions.findAll", Collections.EMPTY_MAP);
        return (ArrayList<SpAgencyPendingTransactions>) pendingTransactions;
    }

    public String fetchforceUpdateStatus(BigInteger clientId) {
        String response;
        List<Object> spAgents = databaseCrudService.fetchWithNativeQuery("SELECT FORCED_UPDATE FROM SP_AGENTS"
                + " WHERE CLIENT_ID in (SELECT CLIENT_ID FROM SP_AGENT_STORES WHERE CLIENT_ID ='" + clientId + "') AND INTRASH='NO'", Collections.EMPTY_MAP, 0, 1);
        response = spAgents.get(0).toString();
        LOGGER.info("Awesome " + response);
        return response.isEmpty() ? null : response;
    }

    public void sendSmswithLink(BigInteger clientId, String msisdn) {
        String smsUrl = environment.getRequiredProperty("datasource.spotcash.smsSendingUrl");
        Optional<SpAgents> agent = Optional.ofNullable(fetchAgentDetails(clientId));
        if (agent.isPresent()) {
            SpAgents spAgent = agent.get();
            if (spAgent.getSmsUrl() != null) {
                if (!spAgent.getSmsUrl().equals("")) {
                    smsUrl = spAgent.getSmsUrl();
                }
            }
            // String downloadUrl = environment.getRequiredProperty("datasource.spotcash.downloadurl");//This url is obtained from the Tangazoletu Cloud
            String new_version = spAgent.getVersionNew();
            String downloadUrl = "";
            String[] str = new_version.split("[,\\,]");
            for (int i = 0; i < str.length; i++) {
                downloadUrl = str[1];
            }
            String msg = null;
            try {
                msg = "" + smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode("Dear agent,Click here to download the latest agent app: " + downloadUrl, "UTF-8");
                LOGGER.info("Sms Sent " + msg);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (SharedFunctions.makeGetRequest(msg).contains("Success")) {
                LOGGER.info("SMS Sent Successfully");
            } else {
                LOGGER.info("SMS Sending Failed");
            }
        }
    }

    public void updateCustomerPinRetries(BigDecimal spCustomerId, Integer pinRetries) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set pinRetries='" + pinRetries + "' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }

    public void updateCustomerOtpRetries(BigDecimal spCustomerId, Integer otpRetries) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set otpRetries='" + otpRetries + "' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }

    public void lockCustomerDueToPin(BigDecimal spCustomerId, Integer pinRetries) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set pinRetries='" + pinRetries + "',subscriptionStatus='0',comments='Account deactivated after max pin retries reached. (Agency)' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }

    public void lockCustomerDueToOtp(BigDecimal spCustomerId, Integer pinRetries) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set otpRetries='" + pinRetries + "',subscriptionStatus='0',comments='Account deactivated after max otp retries reached. (Agency)' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }

    public void resetCustomerPinRetries(BigDecimal spCustomerId) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set pinRetries='0',comments='' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }

    public void resetCustomerOtpRetries(BigDecimal spCustomerId) {
        databaseCrudService.executeHibernateQuery("Update SpCustomers set otpRetries='0',comments='' where id='" + spCustomerId + "' ", Collections.EMPTY_MAP);
    }
    public void updateAppUniqueId(String agentStoreId, String appUniqueId) {
        databaseCrudService.executeHibernateQuery("Update SpAgentStores set uniqueId='" + appUniqueId + "' where agentStoreId='" + agentStoreId + "' ", Collections.EMPTY_MAP);
    }

    public List<SpAgencyRequestLog> fetchPreviousRequestLogsLimitedToMins(String ipAddress, Long timeLimitInMinutes) {
        Map<String, Object> params = new HashMap<>();
        LocalDateTime tenMinsAgo = LocalDateTime.now().minusMinutes(timeLimitInMinutes);
        params.put("time", tenMinsAgo);
        params.put("sourceIp", ipAddress);
        return databaseCrudService.fetchWithNamedQuery("SpAgencyRequestLog.findByIpAndTime", params, 0, Integer.MAX_VALUE);
    }

    public void archiveRequestLogs(String ipAddress) {
        List<SpAgencyRequestLog> requestLogs = fetchPreviousRequestLogsLimitedToMins(ipAddress, 1000L);
        requestLogs.forEach((requestLog) -> {
            SpAgencyRequestLogAch archiveRecord = new SpAgencyRequestLogAch(requestLog.getSourceIp(), requestLog.getUrl(), requestLog.getRequestBody(), requestLog.getThread(), requestLog.getTime());
            databaseCrudService.save(archiveRecord);
        });
        databaseCrudService.executeNativeQuery("DELETE from SP_AGENCY_REQUEST_LOG RL where RL.SOURCE_IP ='" + ipAddress + "' ", Collections.EMPTY_MAP);
    }


    public SpCustomers fetchCustomerByPinEncrypted(String pin, String nationalId) {
        SpCustomers customer = null;
        try {
            String decryptedPin = AesEncryption.decrypt(pin.replaceAll("\n", ""), AesEncryption.encodeKey(environment.getRequiredProperty("secret.key")));
            String newEncrypt = AesEncryption.encrypt(decryptedPin, AesEncryption.encodeKey(environment.getRequiredProperty("secret.key")));
            LOGGER.info("Mogaka " + newEncrypt);
            List<SpCustomers> customers = databaseCrudService.fetchWithHibernateQuery("select r from SpCustomers r"
                    + " where nationalId='" + nationalId + "' and newPinNo ='" + newEncrypt + "' ", Collections.EMPTY_MAP);
            LOGGER.info("size :: " + customers.size());
            customer = customers.get(0);


            LOGGER.info("NEW.... " + customer.getNewEncrypt().toString());
            switch (customer.getNewEncrypt().toString()) {
                case "0":
                    if (customer.getPinNo().equals(new SpotcashUtilities().md5password(decryptedPin))) {
                        return customer;
                    } else
                        return null;
                case "1":
                    if (customer.getNewPinNo().equals(newEncrypt)) {
                        return customer;
                    } else
                        return null;
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    public SpAccounts fetchAccounts(String clientId, String serviceId) {
        LOGGER.info(
                " ----------  FETCHING AVAILABLE BALANCE FOR CLIENT ID " + clientId + " AND SERVICE ID " + serviceId);
        SpAccounts accounts = databaseCrudService.findEntity(
                new BigDecimal(getIdFromServiceSubscriptions(clientId, serviceId)), SpAccounts.class
        );
        LOGGER.info("------- RETURNED AVAILABLE BALANCE AS " + accounts.getAvailBal());
        return accounts;
    }

    private BigInteger getIdFromServiceSubscriptions(String clientId, String serviceId) {
        List<SpServiceSubscriptions> sSubscriptions = databaseCrudService.fetchWithHibernateQuery("Select r from SpServiceSubscriptions r "
                + "where serviceId= '" + new BigInteger(serviceId) + "' and clientId = '" + new BigInteger(clientId) + "' ", Collections.EMPTY_MAP);
        LOGGER.info(" ---------- ACCOUNTS ID FOR CLIENT ID " + clientId + " FOR SERVICE ID " + serviceId + " IS "
                + sSubscriptions.get(0).getAmountAccId() + " ------- ");
        return sSubscriptions.get(0).getAmountAccId();
    }


    public SpServiceSubscriptions getTransactionCharges(BigDecimal clientId, String serviceId) {
        List<SpServiceSubscriptions> spServiceSubscriptions = databaseCrudService.fetchWithHibernateQuery("Select r from SpServiceSubscriptions r "
                + "where serviceId= '" + new BigInteger(serviceId) + "' and clientId = '" + new BigInteger(String.valueOf(clientId)) + "' ", Collections.EMPTY_MAP);
        LOGGER.info(" ---------- FETCHING SUBSCRIPTION DETAILS FOR CLIENT ID " + clientId
                + " FOR SERVICE ID " + serviceId);
        return spServiceSubscriptions.isEmpty() ? null : spServiceSubscriptions.get(0);
    }

    public BigInteger getMaxAllowed(String clientId, String msisdn, String serviceId) {
        String querry = "select nvl(sum(amount), 0) as amount from sp_trans_temp_table where (substr(msisdn,-9,9) = substr('" + msisdn + "',-9,9) or substr(source_msisdn,-9,9) = substr('" + msisdn + "',9,9) )\n"
                + "    and to_char(request_time,'DD-MM-YYYY') = to_char(sysdate, 'DD-MM-YYYY') and trx_status<=4 and resp_code='00' and client_id = "
                + clientId + " and service_id = " + serviceId + "";
        LOGGER.info("------------ GETTING MAXIMUM DAILY ALLOWABLE AMOUNT QUERRY AS " + querry + " ---------------");
        List<Object> spTransTemp = databaseCrudService.fetchWithNativeQuery(querry, Collections.EMPTY_MAP, 0, 1000);
        LOGGER.info(" ---------- FETCHING MAXIMUM DAILY AMOUNT RESPONSE AS " + spTransTemp.get(0) + " FOR CLIENT ID "
                + clientId + " SERVICE ID " + serviceId + " MSISDN " + msisdn + " ------- ");
        return BigInteger.valueOf(Long.parseLong(String.valueOf(spTransTemp.get(0))));
    }

    public SpAccounts getAccountDetails(String spotcashAccountId) {
        List<SpAccounts> spAccounts = databaseCrudService.fetchWithHibernateQuery("select r from SpAccounts r where r.id = "+new BigInteger(spotcashAccountId)+"", Collections.EMPTY_MAP);
        if(spAccounts.size() > 0){
            return spAccounts.get(0);
        }else{
            LOGGER.info("account with id  "+spotcashAccountId+" is not found");
            return null;
        }
    }

    public String getServiceID(String xipService) {
        List<SpServices> spAccounts = databaseCrudService.fetchWithHibernateQuery("select r from SpServices r where r.serviceCode = '"+xipService+"'", Collections.EMPTY_MAP);
        if(spAccounts.size() > 0){
            return String.valueOf(spAccounts.get(0).getId());
        }else{
            return null;
        }
    }
    public Optional<SpAgencyOtpLog> fetchOtpLogsByNationalId(String nationalId) {
        try {
            String query = "select s from SpAgencyOtpLog s where s.nationalId = :nationalId and s.status = 0 order by id desc";
            Map<String, Object> params = new HashMap<>();
            params.put("nationalId", nationalId);
            LOGGER.warn("fetchOtpLogsByNationalId - Query :: "+ query);
            List<SpAgencyOtpLog> spAgencyOtpLogs = databaseCrudService.fetchWithHibernateQuery(query, params, 0, 1);
            if (spAgencyOtpLogs.isEmpty()) {
                return Optional.empty();
            } else {
                return Optional.of(spAgencyOtpLogs.get(0));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    public Optional<List<SpAgencyOtpLog>> fetchWrongOtpLogEntriesUsingStoreUserId(String storeUserId, Timestamp timePeriod) {
        try {
            String query = "select s from SpAgencyOtpLog s where s.storeUserId = :storeUserId and s.storeUserRetryCount > 0 ";
            query += "and (s.timeCreated >= to_timestamp('" + timePeriod +"', 'YYYY-MM-DD HH24:MI:SS.FF') ";
            query += "or s.timeUpdated >= to_timestamp('" + timePeriod +"', 'YYYY-MM-DD HH24:MI:SS.FF')) ";
            Map<String, Object> params = new HashMap<>();
            params.put("storeUserId", Long.parseLong(storeUserId));
            LOGGER.warn("fetchWrongOtpLogEntriesUsingStoreUserId - Query :: "+ query);
            List<SpAgencyOtpLog> spAgencyOtpLogs = databaseCrudService.fetchWithHibernateQuery(query, params, 0, 99);
            if (spAgencyOtpLogs.isEmpty()) {
                return Optional.empty();
            } else {
                return Optional.of(spAgencyOtpLogs);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    public void createOtpLog(String msisdn, String nationalId, Long clientId, String otp) {
        try {
            LOGGER.info("Find current otpLogs for nationalId :: {}, status :: {}...", nationalId, 0);

            Map<String, Object> params = new HashMap<>();
            params.put("nationalId", nationalId);
            List<SpAgencyOtpLog> otpLogs = databaseCrudService.fetchWithHibernateQuery("select c from SpAgencyOtpLog c where c.nationalId = :nationalId and status = 0 order by id desc", params, 0,1);
            if(otpLogs == null || otpLogs.isEmpty()) {
                LOGGER.info("Otp Log does not exist, Creating otp log msisdn :: {}, clientId :: {} to otpLog", msisdn, clientId);
                SpAgencyOtpLog agencyOtpLog = new SpAgencyOtpLog();
                agencyOtpLog.setOtp(maskNCharacters(otp, 3, "*", "start"));
                agencyOtpLog.setMsisdn(msisdn);
                agencyOtpLog.setStatus(BigInteger.ZERO);
                agencyOtpLog.setRetryCount(0);
                agencyOtpLog.setStoreUserRetryCount(0);
                agencyOtpLog.setTimeCreated(Timestamp.from(Instant.now()));
                agencyOtpLog.setClientId(clientId);
                agencyOtpLog.setNationalId(nationalId);
                databaseCrudService.save(agencyOtpLog);

            } else {
                LOGGER.info("Otp Log found, updating otp ans clientId :: {}, clientId :: {} to otpLog", msisdn, clientId);
                SpAgencyOtpLog agencyOtpLog = otpLogs.get(0);
                agencyOtpLog.setOtp(maskNCharacters(otp, 3, "*", "start"));
                agencyOtpLog.setClientId(clientId);
                databaseCrudService.saveOrUpdate(agencyOtpLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void createOtpLog(String msisdn, String nationalId, Long clientId, Long storeUserId, String otp) {
        try {
            LOGGER.info("Find current otpLogs for nationalId :: {}, status :: {}...", nationalId, 0);

            Map<String, Object> params = new HashMap<>();
            params.put("nationalId", nationalId);
            List<SpAgencyOtpLog> otpLogs = databaseCrudService.fetchWithHibernateQuery("select c from SpAgencyOtpLog c where c.nationalId = :nationalId and status = 0 order by id desc", params, 0,1);
            if(otpLogs == null || otpLogs.isEmpty()) {
                LOGGER.info("Otp Log does not exist, Creating otp log msisdn :: {}, clientId :: {}, storeUserId :: {} to otpLog", msisdn, clientId, storeUserId);
                SpAgencyOtpLog agencyOtpLog = new SpAgencyOtpLog();
                agencyOtpLog.setOtp(maskNCharacters(otp, 3, "*", "start"));
                agencyOtpLog.setMsisdn(msisdn);
                agencyOtpLog.setStatus(BigInteger.ZERO);
                agencyOtpLog.setRetryCount(0);
                agencyOtpLog.setTimeCreated(Timestamp.from(Instant.now()));
                agencyOtpLog.setTimeUpdated(Timestamp.from(Instant.now()));
                agencyOtpLog.setClientId(clientId);
                agencyOtpLog.setNationalId(nationalId);
                agencyOtpLog.setStoreUserId(storeUserId);
                agencyOtpLog.setStoreUserRetryCount(0);
                databaseCrudService.save(agencyOtpLog);
            } else {
                LOGGER.info("Otp Log found, updating otp ans msisdn :: {}, clientId :: {}, storeUserId :: {} to otpLog", msisdn, clientId, storeUserId);
                SpAgencyOtpLog agencyOtpLog = otpLogs.get(0);
                agencyOtpLog.setOtp(maskNCharacters(otp, 3, "*", "start"));
                agencyOtpLog.setClientId(clientId);
                agencyOtpLog.setStoreUserId(storeUserId);
                databaseCrudService.saveOrUpdate(agencyOtpLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    String maskNCharacters(String string, Integer noOfCharactersToMask, String maskingChar, String maskPosition) {
        switch (maskPosition) {
            case "end":
                double maskOffset = string.length() - noOfCharactersToMask - 1;
                for (int i = 0; i < string.length(); i++) {
                    if(i >= maskOffset) {
                        string = string.replace(string.charAt(i), '*');
                    }
                }
                return string;
            case "middle":
                maskOffset = Math.floor((double) ((string.length() - noOfCharactersToMask) / 2)) - 1;
                for (int i = 0; i < string.length(); i++) {
                    if(i >= maskOffset && i <= (maskOffset + noOfCharactersToMask)) {
                       string = string.replace(string.charAt(i), '*');
                    }
                }
                return string;
            case "start":
            default:
                // start
                maskOffset = 0;
                for (int i = 0; i < string.length(); i++) {
                    if(i < maskOffset + noOfCharactersToMask) {
                        string = string.replace(string.charAt(i), '*');
                    }
                }
                return string;
        }
    }

    public void blacklistMsisdnForClient(String msisdn, Long clientId, String ipAddress) {
        try {
            LOGGER.info("Checking if already in blacklist - msisdn :: {}, clientId :: {} to blacklist", msisdn, clientId);
            Map<String, Object> params = new HashMap<>();
            params.put("msisdn", msisdn);
            List<SpAgencyBlacklist> blacklistList = databaseCrudService.fetchWithHibernateQuery("select c from SpAgencyBlacklist c where c.msisdn = :msisdn order by id desc", params, 0,100);
            if(blacklistList != null && !blacklistList.isEmpty()) {
                SpAgencyBlacklist agencyBlacklist = null;
                boolean blacklistForClientIdAlreadyExists = blacklistList.stream().anyMatch(blacklist -> blacklist.getClientId().equals(clientId));
                if(!blacklistForClientIdAlreadyExists) {
                    LOGGER.info("Adding msisdn :: {}, clientId :: {} to blacklist", msisdn, clientId);
                    agencyBlacklist = new SpAgencyBlacklist();
                    agencyBlacklist.setMsisdn(msisdn);
                    agencyBlacklist.setClientId(clientId);
                    agencyBlacklist.setDateCreated(Timestamp.from(Instant.now()));
                    agencyBlacklist.setIpAddress(ipAddress);
                    databaseCrudService.save(agencyBlacklist);

                    boolean isGloballyBlacklisted = blacklistList.stream().anyMatch(blacklist -> blacklist.getClientId().equals(0L));
                    if(!isGloballyBlacklisted) {
                        if(blacklistList.size() >= 3) {
                            LOGGER.info("This msisdn has been blacklisted {} times, blacklisting globally...", blacklistList.size());
                            blacklistMsisdnGlobally(agencyBlacklist);
                        } else {
                            LOGGER.debug("This msisdn has been blacklisted {} times...", blacklistList.size());
                        }
                    } else {
                        LOGGER.debug("This msisdn has already been blacklisted globally...");
                    }
                } else {
                    LOGGER.info("This msisdn has already been blacklisted for this clientId...");
                }
            } else {
                LOGGER.info("Msisdn not in blacklist, Adding msisdn :: {}, clientId :: {} to blacklist", msisdn, clientId);
                SpAgencyBlacklist agencyBlacklist = new SpAgencyBlacklist();
                agencyBlacklist.setMsisdn(msisdn);
                agencyBlacklist.setClientId(clientId);
                agencyBlacklist.setDateCreated(Timestamp.from(Instant.now()));
                agencyBlacklist.setIpAddress(ipAddress);
                databaseCrudService.save(agencyBlacklist);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void blacklistMsisdnForClient(String msisdn, Long clientId, Long storeUserId, String ipAddress) {
        try {
            LOGGER.info("Checking if already in blacklist - msisdn :: {}, clientId :: {} to blacklist", msisdn, clientId);
            Map<String, Object> params = new HashMap<>();
            params.put("msisdn", msisdn);
            List<SpAgencyBlacklist> blacklistList = databaseCrudService.fetchWithHibernateQuery("select c from SpAgencyBlacklist c where c.msisdn = :msisdn order by id desc", params, 0,100);
            if(blacklistList != null && !blacklistList.isEmpty()) {
                SpAgencyBlacklist agencyBlacklist = null;
                boolean blacklistForClientIdAlreadyExists = blacklistList.stream().anyMatch(blacklist -> blacklist.getClientId().equals(clientId));
                if(!blacklistForClientIdAlreadyExists) {
                    Timestamp currentTime = Timestamp.from(Instant.now());
                    LOGGER.info("Adding msisdn :: {}, clientId :: {}, storeUserId :: {} to blacklist", msisdn, clientId, storeUserId);
                    agencyBlacklist = new SpAgencyBlacklist();
                    agencyBlacklist.setMsisdn(msisdn);
                    agencyBlacklist.setClientId(clientId);
                    agencyBlacklist.setDateCreated(currentTime);
                    agencyBlacklist.setIpAddress(ipAddress);
                    agencyBlacklist.setStoreUserId(storeUserId);
                    databaseCrudService.save(agencyBlacklist);
                    //Archive the blacklist record
                    databaseCrudService.save(new SpAgencyBlacklistArchive(msisdn, currentTime, ipAddress, clientId, storeUserId));

                    boolean isGloballyBlacklisted = blacklistList.stream().anyMatch(blacklist -> blacklist.getClientId().equals(0L));
                    if(!isGloballyBlacklisted) {
                        if(blacklistList.size() >= 3) {
                            LOGGER.info("This msisdn has been blacklisted {} times, blacklisting globally...", blacklistList.size());
                            blacklistMsisdnGlobally(agencyBlacklist);
                        } else {
                            LOGGER.debug("This msisdn has been blacklisted {} times...", blacklistList.size());
                        }
                    }
                    else {LOGGER.debug("This msisdn has already been blacklisted globally...");}
                }
                else {LOGGER.info("This msisdn has already been blacklisted for this clientId...");}
            } else {
                Timestamp currentTime = Timestamp.from(Instant.now());
                LOGGER.info("Msisdn not in blacklist, Adding msisdn :: {}, clientId :: {} to blacklist", msisdn, clientId);
                SpAgencyBlacklist agencyBlacklist = new SpAgencyBlacklist();
                agencyBlacklist.setMsisdn(msisdn);
                agencyBlacklist.setClientId(clientId);
                agencyBlacklist.setDateCreated(Timestamp.from(Instant.now()));
                agencyBlacklist.setIpAddress(ipAddress);
                agencyBlacklist.setStoreUserId(storeUserId);
                databaseCrudService.save(agencyBlacklist);
                //Archive the blacklist record
                databaseCrudService.save(new SpAgencyBlacklistArchive(msisdn, currentTime, ipAddress, clientId, storeUserId));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void blacklistStoreUserId(String storeUserId, String ipAddress, long clientId, String msisdn) {
        try {
            LOGGER.info("CHECKING IF ALREADY IN BLACKLIST - STORE_USER_ID :: {} TO BLACKLIST", storeUserId);
            Map<String, Object> params = new HashMap<>();
            params.put("storeUserId", Long.parseLong(storeUserId));
            List<SpStoreUserBlacklist> blacklistList = databaseCrudService.fetchWithHibernateQuery("select c from SpStoreUserBlacklist c where c.storeUserId = :storeUserId order by id desc", params, 0,100);
            if(blacklistList != null && !blacklistList.isEmpty()) {
                SpStoreUserBlacklist storeUserBlacklistEntry = blacklistList.get(0);
                Timestamp dateBlacklisted = storeUserBlacklistEntry.getDateCreated();
                LOGGER.info("STORE USER WITH STORE_USER_ID :: {}, ALREADY BLACKLISTED  on DATE :: {}, IP_ADDRESS :: {}", storeUserId, dateBlacklisted, storeUserBlacklistEntry.getIpAddress());
            } else {
                Timestamp currentTime = Timestamp.from(Instant.now());
                LOGGER.info("StoreUserId not in blacklist, Adding storeUserId :: {}, to blacklist", storeUserId);
                SpStoreUserBlacklist storeUserBlacklist = new SpStoreUserBlacklist();
                storeUserBlacklist.setStoreUserId(Long.parseLong(storeUserId));
                storeUserBlacklist.setDateCreated(currentTime);
                storeUserBlacklist.setIpAddress(ipAddress);
                databaseCrudService.save(storeUserBlacklist);
                //Archive the blacklist entry
                databaseCrudService.save(new SpStoreUserBlacklistArchive(
                        Long.parseLong(storeUserId.trim()), currentTime, ipAddress, clientId, msisdn));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void blacklistMsisdnGlobally(SpAgencyBlacklist blacklist) {
        try {
            blacklist.setClientId(0L);
            databaseCrudService.save(blacklist);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isBlacklisted(String msisdn, Integer clientId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("msisdn", msisdn);
            params.put("clientId", clientId.longValue());
            List<SpAgencyBlacklist> blacklistList = databaseCrudService.fetchWithHibernateQuery("select s from SpAgencyBlacklist s where s.msisdn = :msisdn and (s.clientId = :clientId or s.clientId = 0)", params, 0, 3);
            return !blacklistList.isEmpty();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isAgentBlacklisted(Long storeUserId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("storeUserId", storeUserId);
            List<SpStoreUserBlacklist> blacklistList = databaseCrudService.fetchWithHibernateQuery("select s from SpStoreUserBlacklist s where s.storeUserId = :storeUserId", params, 0, 3);
            return !blacklistList.isEmpty();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void resetOtpLog(String nationalId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("nationalId", nationalId);
            List<SpAgencyOtpLog> otpLogs = databaseCrudService.fetchWithHibernateQuery("select s from SpAgencyOtpLog s where s.nationalId = :nationalId", params, 0, 3);
            if(otpLogs.isEmpty()) {
                LOGGER.warn("Otp log for nationalId :: {} not found...", nationalId);
            } else {
                otpLogs.forEach((otpLog) -> {
                    Map<String, Object> removeQueryParams = new HashMap<>();
                    removeQueryParams.put("id", otpLog.getId());
                    databaseCrudService.executeNativeQuery("delete from SP_AGENCY_OTP_LOG where id = :id", removeQueryParams);
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public <T> void save(T entity) {
        try  {
            databaseCrudService.saveOrUpdate(entity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String fetchAgentIdForAuthentication(String agentStoreId) {
        LOGGER.info("FETCHING AGENT_ID FOR JWT AUTHENTICATION WITH AGENT_STORE_ID :: {}", agentStoreId);

        // Gets the AgentStore of the storeUser inorder to get the agentId .
        List<SpAgentStores> agentStores = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgentStores r "
                + "where agentStoreId= '" + agentStoreId + "'", Collections.EMPTY_MAP);

        // and activeStatus= '1' and intrash='NO'
        try {
            SpAgentStores agentStore = agentStores.isEmpty() ? null : agentStores.get(0);
            if (agentStore != null) {
                String agentStoreName = agentStore.getAgentStoreName();
                String agentId = String.valueOf(agentStore.getAgentId());

                // Checks if the agentStore is active.
                if(!agentStore.getIntrash().trim().equalsIgnoreCase("NO") || Objects.equals(agentStore.getActiveStatus(), BigInteger.ZERO)){
                    String msgLog = !agentStore.getIntrash().trim().equalsIgnoreCase("NO")
                            ? "THE AGENT_STORE :: " + agentStoreName + ", AGENT_ID :: "+ agentId + "IS IN TRASH/DELETED!"
                            : "THE AGENT_ID  :: " + agentId + ", AGENT_STORE :: "
                                + agentStoreName + " HAS ACTIVE_STATUS " + agentStore.getActiveStatus();

                    LOGGER.info(msgLog);
                }
                else {
                    LOGGER.info("AGENT_STORE :: {}, WITH AGENT_STORE_ID :: {} FOUND!", agentStoreName, agentStoreId);

                    LOGGER.info("FETCHING AGENT WITH AGENT_ID :: {}", agentId);
                    // Gets the Agent info using agentId and confirms that the agent is active.
                    List<SpAgents> agents = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgents r "
                            + "where agentId= '" + agentId + "'", Collections.EMPTY_MAP);

                    SpAgents agent = agents.isEmpty() ? null : agents.get(0);
                    if(agent != null){
                        String agentName = agent.getAgentName();
                        String agentsId = String.valueOf(agent.getAgentId());

                        // Checks if the Agent is active.
                        if(!agent.getInTrash().equals("NO") || Objects.equals(agent.getActiveStatus(), BigInteger.ZERO)){
                            String msgLog = !agentStore.getIntrash().equals("NO")
                                    ? "THE AGENT :: " + agentName + ", AGENT_ID :: "+ agentsId + "IS IN TRASH/DELETED!"
                                    : "THE AGENT  :: " + agentName + ", AGENT_ID :: "
                                    + agentsId + " HAS ACTIVE_STATUS " + agent.getActiveStatus();

                            LOGGER.info(msgLog);
                        }
                        else {
                            LOGGER.info("AGENT :: {}, WITH AGENT_ID :: {} FOUND!", agentName, agentsId);
                            return String.valueOf(agent.getAgentId());
                        }
                    }
                    else{
                        LOGGER.info("AGENT WITH AGENT_ID :: {} NOT FOUND!", agentId);
                    }
                }
            }
            else {
                LOGGER.info("AGENT_STORE WITH AGENT_STORE_ID :: {} NOT FOUND!", agentStoreId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error executing query " + e.getMessage());
        }
        return null;
    }

    public SpStoreUsers fetchStoreUserWithMsIdn(String msisdn) {
        LOGGER.info("FETCHING STORE_USER DATA WITH AGENT MSISDN ==>  " + msisdn);

        try {
            List<SpStoreUsers> storeUsers = databaseCrudService.fetchWithHibernateQuery("Select s from SpStoreUsers s "
                    + "where contactMsisdn = '" + msisdn + "' ", Collections.EMPTY_MAP);
            // and intrash='NO' and activeStatus=1

            SpStoreUsers storeUser = storeUsers.isEmpty() ? null : storeUsers.get(0);

            if(storeUser != null){
                String storeUserName = storeUser.getContactName();
                String storeUserId = String.valueOf(storeUser.getStoreUserId());
                boolean isStoreUserDeleted = !storeUser.getIntrash().equals("NO");
                boolean isStoreUserInActive = Objects.equals(storeUser.getActiveStatus(), BigInteger.ZERO);
                String clientId = String.valueOf(storeUser.getClientId());

                if(isStoreUserDeleted || isStoreUserInActive){
                    String msg = isStoreUserDeleted
                            ? errorMessageService.getErrorMessage(clientId, "invalidCredentials",
                            "Invalid Phone number or Pin!")
                            : errorMessageService.getErrorMessage(clientId, "agentDeactivated",
                            "Dear Agent, your account is deactivated, please contact sacco for activation");

                    String msgLog = isStoreUserDeleted
                            ? "THE STORE_USER :: " + storeUserName + ", STORE_USER_ID :: "+ storeUserId + "IS IN TRASH/DELETED!"
                            : "THE STORE_USER_ID  :: " + storeUserId + ", STORE_USER :: "
                            + storeUserName + " HAS ACTIVE_STATUS :: " + storeUser.getActiveStatus();

                    LOGGER.info(msgLog);
                    throw new BadCredentialsException(msg);
                }
                else {
                    LOGGER.info("STORE_USER :: {}, WITH STORE_USER_ID :: {} FOUND!", storeUserName, storeUserId);
                    return storeUser;
                }
            }
            else {
                LOGGER.info("STORE_USER WITH PHONE NUMBER :: {}, NOT FOUND!", msisdn);
                throw new BadCredentialsException("Invalid Phone Number or Pin");
            }
        } catch (HibernateException e) {
            e.printStackTrace();
            LOGGER.error("ERROR EXECUTING QUERY: " + e.getMessage());
        }

        throw new BadCredentialsException("Sorry, Authentication Failed. Please Try Again Later.");
    }
    public String fetchUniqueDeviceId(String storeId){
        String id = "";
        try{
            List<SpAgentStores> agentStoreList = databaseCrudService.fetchWithHibernateQuery("Select s from SpAgentStores s "
                    + "where agentStoreId = '" + storeId + "' ", Collections.EMPTY_MAP);
            SpAgentStores agentStore = agentStoreList.isEmpty() ? null : agentStoreList.get(0);
            if(agentStore != null){
                id = agentStore.getUniqueId();
            }
            return id;
        }catch(Exception e){
            LOGGER.error("ERROR: {}", e.getLocalizedMessage());
            return null;
        }
    }

    public SpImsiRecords fetchImsiRecord(String msisdn) {
        try{
            List<SpImsiRecords> spImsiRecords = databaseCrudService.fetchWithHibernateQuery("Select i from SpImsiRecords i "
                    + "where msisdn = '" + msisdn + "' ", Collections.EMPTY_MAP);
            SpImsiRecords imsiRecord = spImsiRecords.isEmpty() ? null : spImsiRecords.get(0);
            return imsiRecord;
        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());
            return null;
        }
    }
    public void updateImsiRecord(SpImsiRecords imsiRecords){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

        String msisdn = imsiRecords.getMsisdn();
        LocalDate lastSwapTime = imsiRecords.getLastSwapTime();
        String lastImsiCheck = sdf.format(imsiRecords.getLastImsiCheck());
        databaseCrudService.executeNativeQuery("Update SP_IMSI_RECORDS set LAST_SWAP_TIME= to_timestamp('" + lastSwapTime + "', 'YYYY-MM-DD HH24:MI:SS.FF'), LAST_IMSI_CHECK= to_timestamp('" + lastImsiCheck + "','YYYY-MM-DD HH24:MI:SS.FF') "
                + " where MSISDN='" + msisdn + "' ", Collections.EMPTY_MAP);
    }
    public void deactivatedStoreUser(String msisdn) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set ACTIVE_STATUS=0"
                    + " where CONTACT_MSISDN = '" + msisdn + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }
    public BigDecimal updateLocationLog(SpLocationLog spLocationLog) {
        databaseCrudService.saveOrUpdate(spLocationLog);
        BigDecimal id = spLocationLog.getId();
        return id;
    }

    public SpServices fetchServiceWithId(String serviceId) {
        try{
            List<SpServices> spServicesList = databaseCrudService.fetchWithHibernateQuery("Select s from SpServices s "
                    + "where id = '" + serviceId + "' ", Collections.EMPTY_MAP);
            SpServices service = spServicesList.isEmpty() ? null : spServicesList.get(0);

            return service;
        }catch(Exception e){
            LOGGER.error("ERROR FETCHING SERVICE: {}", e.getLocalizedMessage());
            return null;
        }
    }    
    public void saveUpdateTokenAndKey(SpTokenKey tokenKey) {
        SpTokenKey spTokenKey = fetchTokenAndKey(tokenKey.getStoreUserId());
        if(spTokenKey != null){
            //update current record
            updateTokeKey(tokenKey);
        }else{
            //create new record
            databaseCrudService.saveOrUpdate(tokenKey);
        }
    }

    public SpTokenKey fetchTokenAndKey(BigInteger storeUserId) {
        try{
            List<SpTokenKey> spTokenKeys = databaseCrudService.fetchWithHibernateQuery("Select tk from SpTokenKey tk "
                    + "where storeUserId = '" + storeUserId + "' ", Collections.EMPTY_MAP);
            SpTokenKey tokenKey = spTokenKeys.isEmpty() ? null : spTokenKeys.get(0);
            return tokenKey;
        }catch(Exception e){
            LOGGER.error("Exception fetching Token and Key: {}",e.getLocalizedMessage());
            return null;
        }
    }
    public void updateTokeKey(SpTokenKey tokenKey) {
        BigInteger storeUserId = tokenKey.getStoreUserId();
        Timestamp requestTime = new Timestamp(System.currentTimeMillis());
        String key = tokenKey.getDataKey();
        String token = tokenKey.getJwtToken();
        try{
            databaseCrudService.executeNativeQuery("Update SP_TOKENKEY set IS_VALID= 1, REQUEST_TIME= STR_TO_DATE('" + requestTime + "', '%Y-%m-%d %H:%i:%s.%f'), DATAKEY= '" + key + "', TOKEN= '" + token + "' "
                    + "where STORE_USER_ID = '" + storeUserId + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            e.printStackTrace();
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public SpTokenKey fetchTokenAndKeyWithToken(String token) {
        try{
            List<SpTokenKey> spTokenKeys = databaseCrudService.fetchWithHibernateQuery("Select tk from SpTokenKey tk "
                    + "where validToken = 1 and jwtToken = '" + token + "' ", Collections.EMPTY_MAP);
            return spTokenKeys.isEmpty() ? null : spTokenKeys.get(0);
        }catch(Exception e){
            LOGGER.error("Exception fetching Token and Key: {}",e.getLocalizedMessage());
            return null;
        }
    }
    public void deactivateAgentStore(BigInteger storeId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_AGENT_STORES set ACTIVE_STATUS=0"
                    + "where AGENT_STORE_ID = '" + storeId + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }
    public void saveNewDeviceId(BigInteger agentStoreId, String deviceId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_AGENT_STORES set DEVICE_ID = '" + deviceId + "' "
                    + "where AGENT_STORE_ID = '" + agentStoreId + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public SpAgencyConfigMessages fetchAgencyConfigMessages(String clientId){
        List<SpAgencyConfigMessages> serviceSubscriptionses = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgencyConfigMessages r "
                + "where clientId= '" + clientId + "' and inTrash='NO' ", Collections.EMPTY_MAP);
        return serviceSubscriptionses.isEmpty() ? null : serviceSubscriptionses.get(0);
    }

    public SpStoreUsers fetchStoreUserWithPhoneNumber(String msisdn) {
        LOGGER.info("Agent_Msisdn | " + msisdn);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where contactMsisdn= '" + msisdn + "' and intrash='NO'", Collections.EMPTY_MAP);

        return storeData.isEmpty() ? null : storeData.get(0);
    }

    public void resetCustomerRetryCount(String nationalId, String clientId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_CUSTOMERS set OTP_RETRIES = '0' "
                    + " where NATIONAL_ID = '" + nationalId + "' and CLIENT_ID = '" + clientId +"'", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public BigDecimal getMaximumDailyAmount(String clientId, String msisdn, String serviceId) {
        String querry  = " ";
        if(serviceId.equalsIgnoreCase("706") || serviceId.equalsIgnoreCase("756")) {
            querry = "select nvl(sum(amount), 0) as amount from sp_trans_temp_table where (substr(msisdn,-9,9) = substr('" + msisdn + "',-9,9) or substr(source_msisdn,-9,9) = substr('" + msisdn + "',9,9) )\n"
                    + "    and to_char(request_time,'DD-MM-YYYY') = to_char(sysdate, 'DD-MM-YYYY') and trx_status<=4 and resp_code='00' and client_id = "
                    + clientId + " and service_id  in (706,756)  and accesschannel_id = 5";
        }else{
            querry = "select nvl(sum(amount), 0) as amount from sp_trans_temp_table where (substr(msisdn,-9,9) = substr('" + msisdn + "',-9,9) or substr(source_msisdn,-9,9) = substr('" + msisdn + "',9,9) )\n"
                    + "    and to_char(request_time,'DD-MM-YYYY') = to_char(sysdate, 'DD-MM-YYYY') and trx_status<=4 and resp_code='00' and client_id = "
                    + clientId + " and service_id = " + serviceId + " and accesschannel_id = 5";
        }
        LOGGER.info("------------ GETTING MAXIMUM DAILY ALLOWABLE AMOUNT QUERRY AS " + querry + " ---------------");
        List<Object> spTransTemp = databaseCrudService.fetchWithNativeQuery(querry, Collections.EMPTY_MAP, 0, 1000);
        LOGGER.info(" ---------- FETCHING MAXIMUM DAILY AMOUNT RESPONSE AS " + spTransTemp.get(0) + " FOR CLIENT ID "
                + clientId + " SERVICE ID " + serviceId + " MSISDN " + msisdn + " ------- ");
        return BigDecimal.valueOf(Long.parseLong(String.valueOf(spTransTemp.get(0))));
    }

    public boolean isServiceChargeable(String serviceCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("serviceCode", serviceCode);
        List<SpServices> spServices = databaseCrudService.fetchWithNamedQuery(
                "SpServices.findByServiceCode", params);
        SpServices spService = spServices.isEmpty() ? null : spServices.get(0);
        if (spService == null) return false;
        else {
            return Objects.equals(spService.getIsAmntChargeable(), BigInteger.ONE);
        }
    }

    public void resetStoreUserPinRetry(String msisdn) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN_RETRIES = '0' "
                    + " where CONTACT_MSISDN = '" + msisdn + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public void updateStoreUserPinRetry(String msisdn, String retryCount) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN_RETRIES = '" +retryCount
                    + "' where CONTACT_MSISDN = '" + msisdn + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public void updateStoreUserDeactivationCount(String msisdn, String retryCount) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set DEACTIVATION_COUNT = '" +retryCount
                    + "' where CONTACT_MSISDN = '" + msisdn + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());
        }
    }

    public void resetStoreUserDeactivationCount(String msisdn) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set DEACTIVATION_COUNT = '0' "
                    + " where CONTACT_MSISDN = '" + msisdn + "' ", Collections.EMPTY_MAP);

        }catch(Exception e){
            LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());

        }
    }

    public SpAgencyEncryptionKey fetchEncryptionKey(String deviceId, String clientId) {
        LOGGER.info("FETCHING ENCRYPTION KEY FOR DEVICE_ID:{}, CLIENT_ID:{} :: ", deviceId, clientId);
        List<SpAgencyEncryptionKey> encryptionKeys = databaseCrudService.fetchWithHibernateQuery("Select r from SpAgencyEncryptionKey r "
                + "where deviceId= '" + deviceId + "' and clientId= '" +clientId +"'" , Collections.EMPTY_MAP);

        return encryptionKeys.isEmpty() ? null : encryptionKeys.get(0);
    }

    public SpStoreUsers fetchStoreUser(String msisdn, String clientId) {
        LOGGER.info("Agent_Msisdn | " + msisdn);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where contactMsisdn= '" + msisdn + "' and intrash='NO' and clientId='" +clientId +"'", Collections.EMPTY_MAP);

        return storeData.isEmpty() ? null : storeData.get(0);
    }

    public void updateStoreUserPinRetry(String msisdn, String retryCount, String clientId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN_RETRIES = '" +retryCount
                    + "' where CONTACT_MSISDN = '" + msisdn + "' and CLIENT_ID = '" +clientId +"' " , Collections.EMPTY_MAP);
        }
        catch(Exception e){ LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());}
    }

    public void deactivatedStoreUser(String msisdn, String clientId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set ACTIVE_STATUS=0"
                    + " where CONTACT_MSISDN = '" + msisdn + "' and CLIENT_ID='" +clientId +"' ", Collections.EMPTY_MAP);
        }
        catch(Exception e){ LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());}
    }

    public void resetStoreUserPinRetry(String msisdn, String clientId) {
        try{
            databaseCrudService.executeNativeQuery("Update SP_STORE_USERS set PIN_RETRIES = '0' "
                    + " where CONTACT_MSISDN = '" + msisdn + "' and CLIENT_ID = '" +clientId +"' ", Collections.EMPTY_MAP);
        }
        catch(Exception e){ LOGGER.error("Exception occurred: {}",e.getLocalizedMessage());}
    }

    public String fetchStoreUserMsisdn(String id) {
        LOGGER.info("Store user ID | " + id);
        List<SpStoreUsers> storeData = databaseCrudService.fetchWithHibernateQuery("Select r from SpStoreUsers r "
                + "where id= '" + id + "'", Collections.EMPTY_MAP);

        return storeData.isEmpty() ? null : storeData.get(0).getContactMsisdn();
    }
    public BigInteger createtrxValue(SpTrxValue spTrxValue) {
        Object entityId = databaseCrudService.save(spTrxValue);
        BigInteger id = spTrxValue.getId();
        return id;
    }
    public String updateSpTrxValue(SpTrxValue spTrxValue) {
        databaseCrudService.saveOrUpdate(spTrxValue);
        String id = String.valueOf(spTrxValue.getId());
        return id;
    }
    public SpTrxValue fetchSptrxValue(String trxId){
        LOGGER.info("Transaction Id || " + trxId);
        List<SpTrxValue> spTrxValues = databaseCrudService.fetchWithHibernateQuery("Select r from SpTrxValue r "
                + "where trxId= '" + trxId + "'", Collections.EMPTY_MAP);

        return spTrxValues.isEmpty() ? null : spTrxValues.get(0);
    }
    public Optional<List<SpPostCompletedTransactionsCbs>> fetchSpPostCompleteTransactionsCbs() {
        try {
            String query = "select s from SpPostCompletedTransactionsCbs s where s.notifyCbs = :notifyCbs ";
            Map<String, Object> params = new HashMap<>();
            params.put("notifyCbs", "0");
            LOGGER.warn("fetchSpPostCompleteTransactionsCbs - Query :: "+ query);
            List<SpPostCompletedTransactionsCbs> spPostCompletedTransactionsCbs = databaseCrudService.fetchWithHibernateQuery(query, params, 0, 99);
            if (spPostCompletedTransactionsCbs.isEmpty()) {
                return Optional.empty();
            } else {
                return Optional.of(spPostCompletedTransactionsCbs);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    public SpTransTempTable getEventTransaction(String msisdn, String eventId, String amount, String serviceId) {
        List<SpTransTempTable> spTransTempTables = databaseCrudService.fetchWithHibernateQuery(
                "select r from SpTransTempTable r where msisdn='" + msisdn + "' and otherFields='" + eventId
                        + "' and amount='" + amount +"' and serviceId='" + serviceId + "'", Collections.EMPTY_MAP);
        return spTransTempTables.isEmpty() ? null : spTransTempTables.get(0);
    }


}
