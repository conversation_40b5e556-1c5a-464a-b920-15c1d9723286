package com.tl.spotcash.agencybanking.pojo;

import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.AccountDetails;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.Member;

import java.util.List;
import java.util.Map;

public class AgencyResponseData {

    private String idNumber;


    private Member member;
    private AgentBalanceResponseMessage responseMessage;
    private String agentPhoneNo;
    private String returnValue;



    private String narration;

    public void setAccounts(List<Map<String, Object>> accounts) {
        this.accounts = accounts;
    }

    private List<Map<String, Object>> accounts;

    public String getAccount_number() {
        return account_number;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    private String account_number;

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getErrorString() {
        return errorString;
    }

    public void setErrorString(String errorString) {
        this.errorString = errorString;
    }

    private String errorString;
    private String transaction_type;

//    public AccountDetails getAccounts() {
//        return accounts;
//    }
//
//    public void setAccounts(AccountDetails accounts) {
//        this.accounts = accounts;
//    }
//
//    private AccountDetails accounts;
//
//
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    private String responseCode;

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    public AgentBalanceResponseMessage getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(AgentBalanceResponseMessage responseMessage) {
        this.responseMessage = responseMessage;
    }


    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public String getAgentPhoneNo() {
        return agentPhoneNo;
    }

    public void setAgentPhoneNo(String agentPhoneNo) {
        this.agentPhoneNo = agentPhoneNo;
    }

    public String getReturnValue() {
        return returnValue;
    }

    public void setReturnValue(String returnValue) {
        this.returnValue = returnValue;
    }
    public String getNarration() {
        return narration;
    }

    public void setNarration(String narration) {
        this.narration = narration;
    }


}
