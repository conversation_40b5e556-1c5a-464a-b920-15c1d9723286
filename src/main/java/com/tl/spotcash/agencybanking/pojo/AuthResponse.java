package com.tl.spotcash.agencybanking.pojo;

import com.tl.spotcash.agencybanking.entity.SpAgentStores;
import com.tl.spotcash.agencybanking.entity.SpStoreUsers;

public class AuthResponse {
    private String successStatus;

    private String errorNarration;
    private boolean requireOTP;
    private boolean otpResponse;

    private SpAgentStores agentStores;

    private SpStoreUsers agentStoreUsers;

    public AuthResponse() {
    }

    //successStatus ==> '00', '01'
    //errorNarration

    public AuthResponse(String successStatus, String errorNarration) {
        this.successStatus = successStatus;
        this.errorNarration = errorNarration;
    }

    public AuthResponse(String successStatus) {
        this.successStatus = successStatus;
    }

    public boolean getOtpResponse() {
        return otpResponse;
    }

    public void setOtpResponse(boolean otpResponse) {
        this.otpResponse = otpResponse;
    }

    public boolean getRequireOTP() {
        return requireOTP;
    }

    public void setRequireOTP(boolean requireOTP) {
        this.requireOTP = requireOTP;
    }

    public String getSuccessStatus() {return successStatus;}

    public void setSuccessStatus(String successStatus) {this.successStatus = successStatus;}

    public String getErrorNarration() {return errorNarration;}

    public void setErrorNarration(String errorNarration) {this.errorNarration = errorNarration;}

    public SpAgentStores getAgentStores() {
        return agentStores;
    }

    public void setAgentStores(SpAgentStores agentStores) {
        this.agentStores = agentStores;
    }

    public SpStoreUsers getAgentStoreUsers() {
        return agentStoreUsers;
    }

    public void setAgentStoreUsers(SpStoreUsers agentStoreUsers) {
        this.agentStoreUsers = agentStoreUsers;
    }
}
