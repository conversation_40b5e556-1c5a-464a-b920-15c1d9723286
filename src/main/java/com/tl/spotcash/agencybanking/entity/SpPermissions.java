/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_PERMISSIONS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpPermissions.findAll", query = "SELECT s FROM SpPermissions s"),
    @NamedQuery(name = "SpPermissions.findById", query = "SELECT s FROM SpPermissions s WHERE s.id = :id"),
    @NamedQuery(name = "SpPermissions.findByTitle", query = "SELECT s FROM SpPermissions s WHERE s.title = :title"),
    @NamedQuery(name = "SpPermissions.findByModule", query = "SELECT s FROM SpPermissions s WHERE s.module = :module"),
    @NamedQuery(name = "SpPermissions.findByDescription", query = "SELECT s FROM SpPermissions s WHERE s.description = :description"),
    @NamedQuery(name = "SpPermissions.findByIsMenu", query = "SELECT s FROM SpPermissions s WHERE s.isMenu = :isMenu"),
    @NamedQuery(name = "SpPermissions.findByMenuPos", query = "SELECT s FROM SpPermissions s WHERE s.menuPos = :menuPos"),
    @NamedQuery(name = "SpPermissions.findByMenuName", query = "SELECT s FROM SpPermissions s WHERE s.menuName = :menuName"),
    @NamedQuery(name = "SpPermissions.findByMenuLevel", query = "SELECT s FROM SpPermissions s WHERE s.menuLevel = :menuLevel"),
    @NamedQuery(name = "SpPermissions.findByChildOf", query = "SELECT s FROM SpPermissions s WHERE s.childOf = :childOf"),
    @NamedQuery(name = "SpPermissions.findByUrl", query = "SELECT s FROM SpPermissions s WHERE s.url = :url"),
    @NamedQuery(name = "SpPermissions.findByDateCreated", query = "SELECT s FROM SpPermissions s WHERE s.dateCreated = :dateCreated")})
public class SpPermissions implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "MODULE")
    private String module;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "IS_MENU")
    private BigInteger isMenu;
    @Size(max = 255)
    @Column(name = "MENU_POS")
    private String menuPos;
    @Size(max = 255)
    @Column(name = "MENU_NAME")
    private String menuName;
    @Size(max = 255)
    @Column(name = "MENU_LEVEL")
    private String menuLevel;
    @Size(max = 255)
    @Column(name = "CHILD_OF")
    private String childOf;
    @Size(max = 255)
    @Column(name = "URL")
    private String url;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;

    public SpPermissions() {
    }

    public SpPermissions(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigInteger getIsMenu() {
        return isMenu;
    }

    public void setIsMenu(BigInteger isMenu) {
        this.isMenu = isMenu;
    }

    public String getMenuPos() {
        return menuPos;
    }

    public void setMenuPos(String menuPos) {
        this.menuPos = menuPos;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuLevel() {
        return menuLevel;
    }

    public void setMenuLevel(String menuLevel) {
        this.menuLevel = menuLevel;
    }

    public String getChildOf() {
        return childOf;
    }

    public void setChildOf(String childOf) {
        this.childOf = childOf;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpPermissions)) {
            return false;
        }
        SpPermissions other = (SpPermissions) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpPermissions[ id=" + id + " ]";
    }
    
}
