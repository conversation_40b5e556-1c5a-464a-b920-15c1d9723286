/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TRXID_SEQ")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTrxidSeq.findAll", query = "SELECT s FROM SpTrxidSeq s"),
    @NamedQuery(name = "SpTrxidSeq.findById", query = "SELECT s FROM SpTrxidSeq s WHERE s.id = :id"),
    @NamedQuery(name = "SpTrxidSeq.findByCurrTxnId", query = "SELECT s FROM SpTrxidSeq s WHERE s.currTxnId = :currTxnId"),
    @NamedQuery(name = "SpTrxidSeq.findByDateUpdated", query = "SELECT s FROM SpTrxidSeq s WHERE s.dateUpdated = :dateUpdated")})
public class SpTrxidSeq implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "CURR_TXN_ID")
    private String currTxnId;
    @Size(max = 20)
    @Column(name = "AB_CURR_TXN_ID")
    private String abCurrTxnId;
    @Column(name = "DATE_UPDATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateUpdated;

    public SpTrxidSeq() {
    }

    public SpTrxidSeq(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getCurrTxnId() {
        return currTxnId;
    }

    public void setCurrTxnId(String currTxnId) {
        this.currTxnId = currTxnId;
    }

    public Date getDateUpdated() {
        return dateUpdated;
    }

    public void setDateUpdated(Date dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTrxidSeq)) {
            return false;
        }
        SpTrxidSeq other = (SpTrxidSeq) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTrxidSeq[ id=" + id + " ]";
    }

    public String getAbCurrTxnId() {
        return abCurrTxnId;
    }

    public void setAbCurrTxnId(String abCurrTxnId) {
        this.abCurrTxnId = abCurrTxnId;
    }

}
