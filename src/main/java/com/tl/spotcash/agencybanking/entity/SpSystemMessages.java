/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_SYSTEM_MESSAGES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpSystemMessages.findAll", query = "SELECT s FROM SpSystemMessages s"),
    @NamedQuery(name = "SpSystemMessages.findById", query = "SELECT s FROM SpSystemMessages s WHERE s.id = :id"),
    @NamedQuery(name = "SpSystemMessages.findByResponseCode", query = "SELECT s FROM SpSystemMessages s WHERE s.responseCode = :responseCode"),
    @NamedQuery(name = "SpSystemMessages.findByDescription", query = "SELECT s FROM SpSystemMessages s WHERE s.description = :description"),
    @NamedQuery(name = "SpSystemMessages.findByMessage", query = "SELECT s FROM SpSystemMessages s WHERE s.message = :message"),
    @NamedQuery(name = "SpSystemMessages.findByThirdpartyServiceId", query = "SELECT s FROM SpSystemMessages s WHERE s.thirdpartyServiceId = :thirdpartyServiceId"),
    @NamedQuery(name = "SpSystemMessages.findByDateCreated", query = "SELECT s FROM SpSystemMessages s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpSystemMessages.findByIntrash", query = "SELECT s FROM SpSystemMessages s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpSystemMessages.findByCoreApiCode", query = "SELECT s FROM SpSystemMessages s WHERE s.coreApiCode = :coreApiCode")})
public class SpSystemMessages implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "RESPONSE_CODE")
    private String responseCode;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "MESSAGE")
    private String message;
    @Column(name = "THIRDPARTY_SERVICE_ID")
    private BigInteger thirdpartyServiceId;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Size(max = 255)
    @Column(name = "CORE_API_CODE")
    private String coreApiCode;

    public SpSystemMessages() {
    }

    public SpSystemMessages(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigInteger getThirdpartyServiceId() {
        return thirdpartyServiceId;
    }

    public void setThirdpartyServiceId(BigInteger thirdpartyServiceId) {
        this.thirdpartyServiceId = thirdpartyServiceId;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public String getCoreApiCode() {
        return coreApiCode;
    }

    public void setCoreApiCode(String coreApiCode) {
        this.coreApiCode = coreApiCode;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpSystemMessages)) {
            return false;
        }
        SpSystemMessages other = (SpSystemMessages) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpSystemMessages[ id=" + id + " ]";
    }
    
}
