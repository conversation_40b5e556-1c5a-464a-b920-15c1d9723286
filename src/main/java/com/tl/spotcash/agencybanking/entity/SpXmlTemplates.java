package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Entity
@Table(name = "SP_XML_TEMPLATES")
public class SpXmlTemplates {
    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;

    @Column(name="CLIENT_ID")
    private String clientId;

    @Column(name="FUNCTION_NAME")
    private String functionName;

    @Column(name="REQUEST", columnDefinition = "CLOB")
    @Lob
    private String requestXml;

    @Column(name="RESPONSE_KEYS")
    private String responseKeys;

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }

    public String getRequestXml() {
        return requestXml;
    }

    public void setRequestXml(String requestXml) {
        this.requestXml = requestXml;
    }

    public String getResponseKeys() {
        return responseKeys;
    }

    public void setResponseKeys(String responseKeys) {
        this.responseKeys = responseKeys;
    }
}