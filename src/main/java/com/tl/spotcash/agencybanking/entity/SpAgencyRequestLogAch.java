package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
@Table(name = "SP_AGENCY_REQUEST_LOG_ACH")
public class SpAgencyRequestLogAch {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SP_AGENCY_REQUEST_LOG_ACH_SEQ")
    @SequenceGenerator(name = "SP_AGENCY_REQUEST_LOG_ACH_SEQ", sequenceName = "SP_AGENCY_REQUEST_LOG_ACH_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;
    @Column(name = "SOURCE_IP")
    private String sourceIp;
    @Column(name = "URL")
    private String Url;
    @Column(name = "REQUEST_BODY")
    private String requestBody;
    @Column(name = "THREAD")
    private String thread;
    @Column(name = "TIME")
    private LocalDateTime time;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getUrl() {
        return Url;
    }

    public void setUrl(String url) {
        Url = url;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public String getThread() {
        return thread;
    }

    public void setThread(String thread) {
        this.thread = thread;
    }

    public SpAgencyRequestLogAch(String sourceIp, String url, String requestBody, String thread, LocalDateTime time) {
        this.sourceIp = sourceIp;
        Url = url;
        this.requestBody = requestBody;
        this.thread = thread;
        this.time = time;
    }
}
