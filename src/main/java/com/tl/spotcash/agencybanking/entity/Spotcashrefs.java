/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SPOTCASHREFS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "Spotcashrefs.findAll", query = "SELECT s FROM Spotcashrefs s"),
    @NamedQuery(name = "Spotcashrefs.findByMpesaid", query = "SELECT s FROM Spotcashrefs s WHERE s.mpesaid = :mpesaid"),
    @NamedQuery(name = "Spotcashrefs.findBySpId", query = "SELECT s FROM Spotcashrefs s WHERE s.spId = :spId")})
public class Spotcashrefs implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "MPESAID")
    private BigDecimal mpesaid;
    @Size(max = 255)
    @Column(name = "SP_ID")
    private String spId;

    public Spotcashrefs() {
    }

    public Spotcashrefs(BigDecimal mpesaid) {
        this.mpesaid = mpesaid;
    }

    public BigDecimal getMpesaid() {
        return mpesaid;
    }

    public void setMpesaid(BigDecimal mpesaid) {
        this.mpesaid = mpesaid;
    }

    public String getSpId() {
        return spId;
    }

    public void setSpId(String spId) {
        this.spId = spId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (mpesaid != null ? mpesaid.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Spotcashrefs)) {
            return false;
        }
        Spotcashrefs other = (Spotcashrefs) object;
        if ((this.mpesaid == null && other.mpesaid != null) || (this.mpesaid != null && !this.mpesaid.equals(other.mpesaid))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.Spotcashrefs[ mpesaid=" + mpesaid + " ]";
    }
    
}
