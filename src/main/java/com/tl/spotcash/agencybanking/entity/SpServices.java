/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_SERVICES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpServices.findAll", query = "SELECT s FROM SpServices s"),
    @NamedQuery(name = "SpServices.findById", query = "SELECT s FROM SpServices s WHERE s.id = :id"),
    @NamedQuery(name = "SpServices.findByTitle", query = "SELECT s FROM SpServices s WHERE s.title = :title"),
    @NamedQuery(name = "SpServices.findByDescription", query = "SELECT s FROM SpServices s WHERE s.description = :description"),
    @NamedQuery(name = "SpServices.findByMessageCode", query = "SELECT s FROM SpServices s WHERE s.messageCode = :messageCode"),
    @NamedQuery(name = "SpServices.findByStatus", query = "SELECT s FROM SpServices s WHERE s.status = :status"),
    @NamedQuery(name = "SpServices.findByThirdpartyServiceId", query = "SELECT s FROM SpServices s WHERE s.thirdpartyServiceId = :thirdpartyServiceId"),
    @NamedQuery(name = "SpServices.findByIntrash", query = "SELECT s FROM SpServices s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpServices.findByDateCreated", query = "SELECT s FROM SpServices s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpServices.findByServiceType", query = "SELECT s FROM SpServices s WHERE s.serviceType = :serviceType"),
    @NamedQuery(name = "SpServices.findByRequireApproval", query = "SELECT s FROM SpServices s WHERE s.requireApproval = :requireApproval"),
    @NamedQuery(name = "SpServices.findBySpCommAcId", query = "SELECT s FROM SpServices s WHERE s.spCommAcId = :spCommAcId"),
    @NamedQuery(name = "SpServices.findByIsAmntChargeable", query = "SELECT s FROM SpServices s WHERE s.isAmntChargeable = :isAmntChargeable"),
    @NamedQuery(name = "SpServices.findByServiceCode", query = "SELECT s FROM SpServices s WHERE s.serviceCode = :serviceCode")})
public class SpServices implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "MESSAGE_CODE")
    private String messageCode;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "THIRDPARTY_SERVICE_ID")
    private BigInteger thirdpartyServiceId;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "SERVICE_TYPE")
    private String serviceType;
    @Size(max = 255)
    @Column(name = "REQUIRE_APPROVAL")
    private String requireApproval;
    @Column(name = "SP_COMM_AC_ID")
    private BigInteger spCommAcId;
    @Column(name = "IS_AMNT_CHARGEABLE")
    private BigInteger isAmntChargeable;
    @Column(name = "SERVICE_CODE")
    private String serviceCode;

    public SpServices() {
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    public SpServices(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(String messageCode) {
        this.messageCode = messageCode;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public BigInteger getThirdpartyServiceId() {
        return thirdpartyServiceId;
    }

    public void setThirdpartyServiceId(BigInteger thirdpartyServiceId) {
        this.thirdpartyServiceId = thirdpartyServiceId;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getRequireApproval() {
        return requireApproval;
    }

    public void setRequireApproval(String requireApproval) {
        this.requireApproval = requireApproval;
    }

    public BigInteger getSpCommAcId() {
        return spCommAcId;
    }

    public void setSpCommAcId(BigInteger spCommAcId) {
        this.spCommAcId = spCommAcId;
    }

    public BigInteger getIsAmntChargeable() {
        return isAmntChargeable;
    }

    public void setIsAmntChargeable(BigInteger isAmntChargeable) {
        this.isAmntChargeable = isAmntChargeable;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpServices)) {
            return false;
        }
        SpServices other = (SpServices) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpServices[ id=" + id + " ]";
    }
    
}
