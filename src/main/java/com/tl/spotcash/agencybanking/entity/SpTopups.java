/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TOPUPS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTopups.findAll", query = "SELECT s FROM SpTopups s"),
    @NamedQuery(name = "SpTopups.findById", query = "SELECT s FROM SpTopups s WHERE s.id = :id"),
    @NamedQuery(name = "SpTopups.findByClientId", query = "SELECT s FROM SpTopups s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpTopups.findByActualTrxDate", query = "SELECT s FROM SpTopups s WHERE s.actualTrxDate = :actualTrxDate"),
    @NamedQuery(name = "SpTopups.findByPaymentMode", query = "SELECT s FROM SpTopups s WHERE s.paymentMode = :paymentMode"),
    @NamedQuery(name = "SpTopups.findByReference", query = "SELECT s FROM SpTopups s WHERE s.reference = :reference"),
    @NamedQuery(name = "SpTopups.findByClientBankName", query = "SELECT s FROM SpTopups s WHERE s.clientBankName = :clientBankName"),
    @NamedQuery(name = "SpTopups.findByClientBankBranch", query = "SELECT s FROM SpTopups s WHERE s.clientBankBranch = :clientBankBranch"),
    @NamedQuery(name = "SpTopups.findBySpotcashBankId", query = "SELECT s FROM SpTopups s WHERE s.spotcashBankId = :spotcashBankId"),
    @NamedQuery(name = "SpTopups.findByTransactionAmount", query = "SELECT s FROM SpTopups s WHERE s.transactionAmount = :transactionAmount"),
    @NamedQuery(name = "SpTopups.findByNotes", query = "SELECT s FROM SpTopups s WHERE s.notes = :notes"),
    @NamedQuery(name = "SpTopups.findByInitiatedBy", query = "SELECT s FROM SpTopups s WHERE s.initiatedBy = :initiatedBy"),
    @NamedQuery(name = "SpTopups.findByConfirmedBy", query = "SELECT s FROM SpTopups s WHERE s.confirmedBy = :confirmedBy"),
    @NamedQuery(name = "SpTopups.findByTransactionTime", query = "SELECT s FROM SpTopups s WHERE s.transactionTime = :transactionTime"),
    @NamedQuery(name = "SpTopups.findByTransactionDepslip", query = "SELECT s FROM SpTopups s WHERE s.transactionDepslip = :transactionDepslip"),
    @NamedQuery(name = "SpTopups.findByStatus", query = "SELECT s FROM SpTopups s WHERE s.status = :status"),
    @NamedQuery(name = "SpTopups.findByConfirmStatus", query = "SELECT s FROM SpTopups s WHERE s.confirmStatus = :confirmStatus"),
    @NamedQuery(name = "SpTopups.findByConfirmNotes", query = "SELECT s FROM SpTopups s WHERE s.confirmNotes = :confirmNotes"),
    @NamedQuery(name = "SpTopups.findByConfirmSeenSlip", query = "SELECT s FROM SpTopups s WHERE s.confirmSeenSlip = :confirmSeenSlip"),
    @NamedQuery(name = "SpTopups.findByConfirmTime", query = "SELECT s FROM SpTopups s WHERE s.confirmTime = :confirmTime"),
    @NamedQuery(name = "SpTopups.findByAccountTypeId", query = "SELECT s FROM SpTopups s WHERE s.accountTypeId = :accountTypeId"),
    @NamedQuery(name = "SpTopups.findByFloatType", query = "SELECT s FROM SpTopups s WHERE s.floatType = :floatType"),
    @NamedQuery(name = "SpTopups.findByConfirmMode", query = "SELECT s FROM SpTopups s WHERE s.confirmMode = :confirmMode"),
    @NamedQuery(name = "SpTopups.findBySyncStatus", query = "SELECT s FROM SpTopups s WHERE s.syncStatus = :syncStatus"),
    @NamedQuery(name = "SpTopups.findByTrxId", query = "SELECT s FROM SpTopups s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpTopups.findByClientAccNo", query = "SELECT s FROM SpTopups s WHERE s.clientAccNo = :clientAccNo"),
    @NamedQuery(name = "SpTopups.findByClientAccName", query = "SELECT s FROM SpTopups s WHERE s.clientAccName = :clientAccName"),
    @NamedQuery(name = "SpTopups.findByAgentId", query = "SELECT s FROM SpTopups s WHERE s.agentId = :agentId")})
public class SpTopups implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "ACTUAL_TRX_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date actualTrxDate;
    @Size(max = 255)
    @Column(name = "PAYMENT_MODE")
    private String paymentMode;
    @Size(max = 255)
    @Column(name = "REFERENCE")
    private String reference;
    @Size(max = 255)
    @Column(name = "CLIENT_BANK_NAME")
    private String clientBankName;
    @Size(max = 255)
    @Column(name = "CLIENT_BANK_BRANCH")
    private String clientBankBranch;
    @Column(name = "SPOTCASH_BANK_ID")
    private BigInteger spotcashBankId;
    @Column(name = "TRANSACTION_AMOUNT")
    private BigDecimal transactionAmount;
    @Size(max = 255)
    @Column(name = "NOTES")
    private String notes;
    @Column(name = "INITIATED_BY")
    private BigInteger initiatedBy;
    @Column(name = "CONFIRMED_BY")
    private BigInteger confirmedBy;
    @Column(name = "TRANSACTION_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date transactionTime;
    @Size(max = 255)
    @Column(name = "TRANSACTION_DEPSLIP")
    private String transactionDepslip;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "CONFIRM_STATUS")
    private BigInteger confirmStatus;
    @Size(max = 255)
    @Column(name = "CONFIRM_NOTES")
    private String confirmNotes;
    @Column(name = "CONFIRM_SEEN_SLIP")
    private BigInteger confirmSeenSlip;
    @Column(name = "CONFIRM_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date confirmTime;
    @Column(name = "ACCOUNT_TYPE_ID")
    private BigInteger accountTypeId;
    @Column(name = "FLOAT_TYPE")
    private BigInteger floatType;
    @Column(name = "CONFIRM_MODE")
    private BigInteger confirmMode;
    @Column(name = "SYNC_STATUS")
    private BigInteger syncStatus;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Size(max = 255)
    @Column(name = "CLIENT_ACC_NO")
    private String clientAccNo;
    @Size(max = 255)
    @Column(name = "CLIENT_ACC_NAME")
    private String clientAccName;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;

    public SpTopups() {
    }

    public SpTopups(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public Date getActualTrxDate() {
        return actualTrxDate;
    }

    public void setActualTrxDate(Date actualTrxDate) {
        this.actualTrxDate = actualTrxDate;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getClientBankName() {
        return clientBankName;
    }

    public void setClientBankName(String clientBankName) {
        this.clientBankName = clientBankName;
    }

    public String getClientBankBranch() {
        return clientBankBranch;
    }

    public void setClientBankBranch(String clientBankBranch) {
        this.clientBankBranch = clientBankBranch;
    }

    public BigInteger getSpotcashBankId() {
        return spotcashBankId;
    }

    public void setSpotcashBankId(BigInteger spotcashBankId) {
        this.spotcashBankId = spotcashBankId;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigInteger getInitiatedBy() {
        return initiatedBy;
    }

    public void setInitiatedBy(BigInteger initiatedBy) {
        this.initiatedBy = initiatedBy;
    }

    public BigInteger getConfirmedBy() {
        return confirmedBy;
    }

    public void setConfirmedBy(BigInteger confirmedBy) {
        this.confirmedBy = confirmedBy;
    }

    public Date getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(Date transactionTime) {
        this.transactionTime = transactionTime;
    }

    public String getTransactionDepslip() {
        return transactionDepslip;
    }

    public void setTransactionDepslip(String transactionDepslip) {
        this.transactionDepslip = transactionDepslip;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public BigInteger getConfirmStatus() {
        return confirmStatus;
    }

    public void setConfirmStatus(BigInteger confirmStatus) {
        this.confirmStatus = confirmStatus;
    }

    public String getConfirmNotes() {
        return confirmNotes;
    }

    public void setConfirmNotes(String confirmNotes) {
        this.confirmNotes = confirmNotes;
    }

    public BigInteger getConfirmSeenSlip() {
        return confirmSeenSlip;
    }

    public void setConfirmSeenSlip(BigInteger confirmSeenSlip) {
        this.confirmSeenSlip = confirmSeenSlip;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public BigInteger getAccountTypeId() {
        return accountTypeId;
    }

    public void setAccountTypeId(BigInteger accountTypeId) {
        this.accountTypeId = accountTypeId;
    }

    public BigInteger getFloatType() {
        return floatType;
    }

    public void setFloatType(BigInteger floatType) {
        this.floatType = floatType;
    }

    public BigInteger getConfirmMode() {
        return confirmMode;
    }

    public void setConfirmMode(BigInteger confirmMode) {
        this.confirmMode = confirmMode;
    }

    public BigInteger getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(BigInteger syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getClientAccNo() {
        return clientAccNo;
    }

    public void setClientAccNo(String clientAccNo) {
        this.clientAccNo = clientAccNo;
    }

    public String getClientAccName() {
        return clientAccName;
    }

    public void setClientAccName(String clientAccName) {
        this.clientAccName = clientAccName;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTopups)) {
            return false;
        }
        SpTopups other = (SpTopups) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTopups[ id=" + id + " ]";
    }
    
}
