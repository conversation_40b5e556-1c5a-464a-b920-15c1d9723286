package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Entity
@Table(name="SP_LOCATION_LOG")
public class SpLocationLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigDecimal id;

    @Column(name = "TRX_ID")
    private String trxId;

    @Column(name = "TIMESTAMP")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestTime;

    @Column(name = "CLIENT_ID")
    private BigInteger clientId;

    @Column(name = "LOCATION")
    private String location;

    @Column(name = "AGENT_ID")
    private BigDecimal agentId;

    @Column(name = "STORE_ID")
    private BigInteger agentStoreId;

    @Column(name = "STORE_USER_ID")
    private BigInteger storeUserId;

    @Column(name = "IP_ADDRESS")
    private String ipAddress;

    @Column(name = "TRANSACTION_TYPE")
    private String transactionType;

    public SpLocationLog(){

    }

    public SpLocationLog(BigDecimal id, String trxId, Date requestTime, BigInteger clientId, String location, BigDecimal agentId, BigInteger agentStoreId, BigInteger storeUserId, String ipAddress, String transactionType) {
        this.id = id;
        this.trxId = trxId;
        this.requestTime = requestTime;
        this.clientId = clientId;
        this.location = location;
        this.agentId = agentId;
        this.agentStoreId = agentStoreId;
        this.storeUserId = storeUserId;
        this.ipAddress = ipAddress;
        this.transactionType = transactionType;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getAgentId() {
        return agentId;
    }

    public void setAgentId(BigDecimal agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public BigInteger getStoreUserId() {
        return storeUserId;
    }

    public void setStoreUserId(BigInteger storeUserId) {
        this.storeUserId = storeUserId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    @Override
    public String toString() {
        return "SpLocationLog{" +
                "id=" + id +
                ", trxId='" + trxId + '\'' +
                ", requestTime=" + requestTime +
                ", clientId=" + clientId +
                ", location='" + location + '\'' +
                ", agentId=" + agentId +
                ", agentStoreId=" + agentStoreId +
                ", storeUserId=" + storeUserId +
                ", ipAddress='" + ipAddress + '\'' +
                '}';
    }
}
