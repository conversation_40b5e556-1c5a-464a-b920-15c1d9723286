package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

@Entity
@Table(name = "SP_CLIENTS_APP_CONFIGURATIONS")
public class SpClientsAppConfigurations {
    private BigInteger id;
    private BigInteger clientId;
    private String applicationName;
    private String organizationSlogan;
    private String createdBy;
    private Timestamp dateUpdated;
    private String updatedBy;
    private String primaryBgColor;
    private String secondaryBgColor;
    private String fontColor;
    private String organizationLogo;
    private String appLogo;
    private BigInteger activeStatus;
    private String intrash;
    private String smsUrl;
    private String otpAuthentication;
    private BigInteger validateFinancials;
    private String utilityPayOption;
    private BigInteger asyncEloan;
    private BigInteger appIdleTimeout;
    private Timestamp dateCreated;
    private BigInteger maxPinRetries;
    private BigInteger genericDeposits;
    private BigInteger useMemberChannels;
    private Integer upfrontLoanFee;
    private BigDecimal loanInterest;
    private boolean mpesaWithdrawalToOther;
    private BigInteger notifyFriend;
    private BigInteger combinedAccFromCbs;
    private BigInteger newMinistatement;
    private BigInteger validateType;
    private BigInteger automaticDepositReversal;
    private String reversalResponseCodes;
    private BigInteger automaticFinancialReversal;
    private String financialReversalRespCodes;
    private BigInteger versionOld;
    private BigInteger versionNew;
    private BigInteger forceUpdate;
    private String dashboardTabs;
    private String organisationEmail;
    private String playStoreLink;
    private String dashboardTiles;
    private BigInteger listDestinationAccount;
    private BigInteger groupSourceAccount;
    private BigInteger balanceToFetchAll;
    private String appName;
    private BigInteger appPublished;
    private String notPublishedMsg;
    private String iatType;
    private BigInteger promptStatementEmail;
    private BigInteger showFlyers;
    private BigInteger buyAirtimeToOwn;
    private BigInteger useDefaultRepaymentPeriod;
    private BigInteger defaultRepaymentPeriod;
    private Integer maxOtpRetries;
    private String useMsgTemplate;
    private BigInteger useCbsResponse;
    private BigInteger defaultAccount;
    private BigInteger switchAccountStatus;
    private BigInteger useMainSavingsAccount;
    private BigInteger verifyMemberNumber;
    private String otpDestination;
    private BigInteger useSaccoAccountPaymentOption;

    @Id
    @Column(name = "ID")
    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    @Basic
    @Column(name = "CLIENT_ID")
    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    @Basic
    @Column(name = "APPLICATION_NAME")
    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    @Basic
    @Column(name = "ORGANIZATION_SLOGAN")
    public String getOrganizationSlogan() {
        return organizationSlogan;
    }

    public void setOrganizationSlogan(String organizationSlogan) {
        this.organizationSlogan = organizationSlogan;
    }

    @Basic
    @Column(name = "CREATED_BY")
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Basic
    @Column(name = "DATE_UPDATED")
    public Timestamp getDateUpdated() {
        return dateUpdated;
    }

    public void setDateUpdated(Timestamp dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

    @Basic
    @Column(name = "UPDATED_BY")
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Basic
    @Column(name = "PRIMARY_BG_COLOR")
    public String getPrimaryBgColor() {
        return primaryBgColor;
    }

    public void setPrimaryBgColor(String primaryBgColor) {
        this.primaryBgColor = primaryBgColor;
    }

    @Basic
    @Column(name = "SECONDARY_BG_COLOR")
    public String getSecondaryBgColor() {
        return secondaryBgColor;
    }

    public void setSecondaryBgColor(String secondaryBgColor) {
        this.secondaryBgColor = secondaryBgColor;
    }

    @Basic
    @Column(name = "FONT_COLOR")
    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    @Basic
    @Column(name = "ORGANIZATION_LOGO")
    public String getOrganizationLogo() {
        return organizationLogo;
    }

    public void setOrganizationLogo(String organizationLogo) {
        this.organizationLogo = organizationLogo;
    }

    @Basic
    @Column(name = "APP_LOGO")
    public String getAppLogo() {
        return appLogo;
    }

    public void setAppLogo(String appLogo) {
        this.appLogo = appLogo;
    }

    @Basic
    @Column(name = "ACTIVE_STATUS")
    public BigInteger getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(BigInteger activeStatus) {
        this.activeStatus = activeStatus;
    }

    @Basic
    @Column(name = "INTRASH")
    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    @Basic
    @Column(name = "SMS_URL")
    public String getSmsUrl() {
        return smsUrl;
    }

    public void setSmsUrl(String smsUrl) {
        this.smsUrl = smsUrl;
    }

    @Basic
    @Column(name = "OTP_AUTHENTICATION")
    public String getOtpAuthentication() {
        return otpAuthentication;
    }

    public void setOtpAuthentication(String otpAuthentication) {
        this.otpAuthentication = otpAuthentication;
    }

    @Basic
    @Column(name = "VALIDATE_FINANCIALS")
    public BigInteger getValidateFinancials() {
        return validateFinancials;
    }

    public void setValidateFinancials(BigInteger validateFinancials) {
        this.validateFinancials = validateFinancials;
    }

    @Basic
    @Column(name = "UTILITY_PAY_OPTION")
    public String getUtilityPayOption() {
        return utilityPayOption;
    }

    public void setUtilityPayOption(String utilityPayOption) {
        this.utilityPayOption = utilityPayOption;
    }

    @Basic
    @Column(name = "ASYNC_ELOAN")
    public BigInteger getAsyncEloan() {
        return asyncEloan;
    }

    public void setAsyncEloan(BigInteger asyncEloan) {
        this.asyncEloan = asyncEloan;
    }

    @Basic
    @Column(name = "APP_IDLE_TIMEOUT")
    public BigInteger getAppIdleTimeout() {
        return appIdleTimeout;
    }

    public void setAppIdleTimeout(BigInteger appIdleTimeout) {
        this.appIdleTimeout = appIdleTimeout;
    }

    @Basic
    @Column(name = "DATE_CREATED")
    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    @Basic
    @Column(name = "MAX_PIN_RETRIES")
    public BigInteger getMaxPinRetries() {
        return maxPinRetries;
    }

    public void setMaxPinRetries(BigInteger maxPinRetries) {
        this.maxPinRetries = maxPinRetries;
    }

    @Basic
    @Column(name = "GENERIC_DEPOSITS")
    public BigInteger getGenericDeposits() {
        return genericDeposits;
    }

    public void setGenericDeposits(BigInteger genericDeposits) {
        this.genericDeposits = genericDeposits;
    }

    @Basic
    @Column(name = "USE_MEMBER_CHANNELS")
    public BigInteger getUseMemberChannels() {
        return useMemberChannels;
    }

    public void setUseMemberChannels(BigInteger useMemberChannels) {
        this.useMemberChannels = useMemberChannels;
    }

    @Basic
    @Column(name = "UPFRONT_LOAN_FEE")
    public Integer getUpfrontLoanFee() {
        return upfrontLoanFee;
    }

    public void setUpfrontLoanFee(Integer upfrontLoanFee) {
        this.upfrontLoanFee = upfrontLoanFee;
    }

    @Basic
    @Column(name = "LOAN_INTEREST")
    public BigDecimal getLoanInterest() {
        return loanInterest;
    }

    public void setLoanInterest(BigDecimal loanInterest) {
        this.loanInterest = loanInterest;
    }

    @Basic
    @Column(name = "MPESA_WITHDRAWAL_TO_OTHER")
    public boolean isMpesaWithdrawalToOther() {
        return mpesaWithdrawalToOther;
    }

    public void setMpesaWithdrawalToOther(boolean mpesaWithdrawalToOther) {
        this.mpesaWithdrawalToOther = mpesaWithdrawalToOther;
    }

    @Basic
    @Column(name = "NOTIFY_FRIEND")
    public BigInteger getNotifyFriend() {
        return notifyFriend;
    }

    public void setNotifyFriend(BigInteger notifyFriend) {
        this.notifyFriend = notifyFriend;
    }

    @Basic
    @Column(name = "COMBINED_ACC_FROM_CBS")
    public BigInteger getCombinedAccFromCbs() {
        return combinedAccFromCbs;
    }

    public void setCombinedAccFromCbs(BigInteger combinedAccFromCbs) {
        this.combinedAccFromCbs = combinedAccFromCbs;
    }

    @Basic
    @Column(name = "NEW_MINISTATEMENT")
    public BigInteger getNewMinistatement() {
        return newMinistatement;
    }

    public void setNewMinistatement(BigInteger newMinistatement) {
        this.newMinistatement = newMinistatement;
    }

    @Basic
    @Column(name = "VALIDATE_TYPE")
    public BigInteger getValidateType() {
        return validateType;
    }

    public void setValidateType(BigInteger validateType) {
        this.validateType = validateType;
    }

    @Basic
    @Column(name = "AUTOMATIC_DEPOSIT_REVERSAL")
    public BigInteger getAutomaticDepositReversal() {
        return automaticDepositReversal;
    }

    public void setAutomaticDepositReversal(BigInteger automaticDepositReversal) {
        this.automaticDepositReversal = automaticDepositReversal;
    }

    @Basic
    @Column(name = "REVERSAL_RESPONSE_CODES")
    public String getReversalResponseCodes() {
        return reversalResponseCodes;
    }

    public void setReversalResponseCodes(String reversalResponseCodes) {
        this.reversalResponseCodes = reversalResponseCodes;
    }

    @Basic
    @Column(name = "AUTOMATIC_FINANCIAL_REVERSAL")
    public BigInteger getAutomaticFinancialReversal() {
        return automaticFinancialReversal;
    }

    public void setAutomaticFinancialReversal(BigInteger automaticFinancialReversal) {
        this.automaticFinancialReversal = automaticFinancialReversal;
    }

    @Basic
    @Column(name = "FINANCIAL_REVERSAL_RESP_CODES")
    public String getFinancialReversalRespCodes() {
        return financialReversalRespCodes;
    }

    public void setFinancialReversalRespCodes(String financialReversalRespCodes) {
        this.financialReversalRespCodes = financialReversalRespCodes;
    }

    @Basic
    @Column(name = "VERSION_OLD")
    public BigInteger getVersionOld() {
        return versionOld;
    }

    public void setVersionOld(BigInteger versionOld) {
        this.versionOld = versionOld;
    }

    @Basic
    @Column(name = "VERSION_NEW")
    public BigInteger getVersionNew() {
        return versionNew;
    }

    public void setVersionNew(BigInteger versionNew) {
        this.versionNew = versionNew;
    }

    @Basic
    @Column(name = "FORCE_UPDATE")
    public BigInteger getForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(BigInteger forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    @Basic
    @Column(name = "DASHBOARD_TABS")
    public String getDashboardTabs() {
        return dashboardTabs;
    }

    public void setDashboardTabs(String dashboardTabs) {
        this.dashboardTabs = dashboardTabs;
    }

    @Basic
    @Column(name = "ORGANISATION_EMAIL")
    public String getOrganisationEmail() {
        return organisationEmail;
    }

    public void setOrganisationEmail(String organisationEmail) {
        this.organisationEmail = organisationEmail;
    }

    @Basic
    @Column(name = "PLAY_STORE_LINK")
    public String getPlayStoreLink() {
        return playStoreLink;
    }

    public void setPlayStoreLink(String playStoreLink) {
        this.playStoreLink = playStoreLink;
    }

    @Basic
    @Column(name = "DASHBOARD_TILES")
    public String getDashboardTiles() {
        return dashboardTiles;
    }

    public void setDashboardTiles(String dashboardTiles) {
        this.dashboardTiles = dashboardTiles;
    }

    @Basic
    @Column(name = "LIST_DESTINATION_ACCOUNT")
    public BigInteger getListDestinationAccount() {
        return listDestinationAccount;
    }

    public void setListDestinationAccount(BigInteger listDestinationAccount) {
        this.listDestinationAccount = listDestinationAccount;
    }

    @Basic
    @Column(name = "GROUP_SOURCE_ACCOUNT")
    public BigInteger getGroupSourceAccount() {
        return groupSourceAccount;
    }

    public void setGroupSourceAccount(BigInteger groupSourceAccount) {
        this.groupSourceAccount = groupSourceAccount;
    }

    @Basic
    @Column(name = "BALANCE_TO_FETCH_ALL")
    public BigInteger getBalanceToFetchAll() {
        return balanceToFetchAll;
    }

    public void setBalanceToFetchAll(BigInteger balanceToFetchAll) {
        this.balanceToFetchAll = balanceToFetchAll;
    }

    @Basic
    @Column(name = "APP_NAME")
    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    @Basic
    @Column(name = "APP_PUBLISHED")
    public BigInteger getAppPublished() {
        return appPublished;
    }

    public void setAppPublished(BigInteger appPublished) {
        this.appPublished = appPublished;
    }

    @Basic
    @Column(name = "NOT_PUBLISHED_MSG")
    public String getNotPublishedMsg() {
        return notPublishedMsg;
    }

    public void setNotPublishedMsg(String notPublishedMsg) {
        this.notPublishedMsg = notPublishedMsg;
    }

    @Basic
    @Column(name = "IAT_TYPE")
    public String getIatType() {
        return iatType;
    }

    public void setIatType(String iatType) {
        this.iatType = iatType;
    }

    @Basic
    @Column(name = "PROMPT_STATEMENT_EMAIL")
    public BigInteger getPromptStatementEmail() {
        return promptStatementEmail;
    }

    public void setPromptStatementEmail(BigInteger promptStatementEmail) {
        this.promptStatementEmail = promptStatementEmail;
    }

    @Basic
    @Column(name = "SHOW_FLYERS")
    public BigInteger getShowFlyers() {
        return showFlyers;
    }

    public void setShowFlyers(BigInteger showFlyers) {
        this.showFlyers = showFlyers;
    }

    @Basic
    @Column(name = "BUY_AIRTIME_TO_OWN")
    public BigInteger getBuyAirtimeToOwn() {
        return buyAirtimeToOwn;
    }

    public void setBuyAirtimeToOwn(BigInteger buyAirtimeToOwn) {
        this.buyAirtimeToOwn = buyAirtimeToOwn;
    }

    @Basic
    @Column(name = "USE_DEFAULT_REPAYMENT_PERIOD")
    public BigInteger getUseDefaultRepaymentPeriod() {
        return useDefaultRepaymentPeriod;
    }

    public void setUseDefaultRepaymentPeriod(BigInteger useDefaultRepaymentPeriod) {
        this.useDefaultRepaymentPeriod = useDefaultRepaymentPeriod;
    }

    @Basic
    @Column(name = "DEFAULT_REPAYMENT_PERIOD")
    public BigInteger getDefaultRepaymentPeriod() {
        return defaultRepaymentPeriod;
    }

    public void setDefaultRepaymentPeriod(BigInteger defaultRepaymentPeriod) {
        this.defaultRepaymentPeriod = defaultRepaymentPeriod;
    }

    @Basic
    @Column(name = "MAX_OTP_RETRIES")
    public Integer getMaxOtpRetries() {
        return maxOtpRetries;
    }

    public void setMaxOtpRetries(Integer maxOtpRetries) {
        this.maxOtpRetries = maxOtpRetries;
    }

    @Basic
    @Column(name = "USE_MSG_TEMPLATE")
    public String getUseMsgTemplate() {
        return useMsgTemplate;
    }

    public void setUseMsgTemplate(String useMsgTemplate) {
        this.useMsgTemplate = useMsgTemplate;
    }

    @Basic
    @Column(name = "USE_CBS_RESPONSE")
    public BigInteger getUseCbsResponse() {
        return useCbsResponse;
    }

    public void setUseCbsResponse(BigInteger useCbsResponse) {
        this.useCbsResponse = useCbsResponse;
    }

    @Basic
    @Column(name = "DEFAULT_ACCOUNT")
    public BigInteger getDefaultAccount() {
        return defaultAccount;
    }

    public void setDefaultAccount(BigInteger defaultAccount) {
        this.defaultAccount = defaultAccount;
    }

    @Basic
    @Column(name = "SWITCH_ACCOUNT_STATUS")
    public BigInteger getSwitchAccountStatus() {
        return switchAccountStatus;
    }

    public void setSwitchAccountStatus(BigInteger switchAccountStatus) {
        this.switchAccountStatus = switchAccountStatus;
    }

    @Basic
    @Column(name = "USE_MAIN_SAVINGS_ACCOUNT")
    public BigInteger getUseMainSavingsAccount() {
        return useMainSavingsAccount;
    }

    public void setUseMainSavingsAccount(BigInteger useMainSavingsAccount) {
        this.useMainSavingsAccount = useMainSavingsAccount;
    }

    @Basic
    @Column(name = "VERIFY_MEMBER_NUMBER")
    public BigInteger getVerifyMemberNumber() {
        return verifyMemberNumber;
    }

    public void setVerifyMemberNumber(BigInteger verifyMemberNumber) {
        this.verifyMemberNumber = verifyMemberNumber;
    }

    @Basic
    @Column(name = "OTP_DESTINATION")
    public String getOtpDestination() {
        return otpDestination;
    }

    public void setOtpDestination(String otpDestination) {
        this.otpDestination = otpDestination;
    }

    @Basic
    @Column(name = "USE_SACCO_ACCOUNT_PAYMENT_OPTION")
    public BigInteger getUseSaccoAccountPaymentOption() {
        return useSaccoAccountPaymentOption;
    }

    public void setUseSaccoAccountPaymentOption(BigInteger useSaccoAccountPaymentOption) {
        this.useSaccoAccountPaymentOption = useSaccoAccountPaymentOption;
    }

}
