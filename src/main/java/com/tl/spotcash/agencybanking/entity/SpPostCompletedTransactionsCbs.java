package com.tl.spotcash.agencybanking.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "SP_POST_COMPLETED_TRANSACTIONS_CBS")
@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "SpPostCompletedTransactionsCbs.findAll", query = "select s from SpPostCompletedTransactionsCbs s")
})
public class SpPostCompletedTransactionsCbs implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "PAYMENT_REFERENCE")
    private String paymentReference;
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "EVENT_ID")
    private String eventId;
    @Column(name = "NOTIFY_CBS")
    private String notifyCbs;
}
