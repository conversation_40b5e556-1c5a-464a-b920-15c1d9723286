/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ALERTS_MAP")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAlertsMap.findAll", query = "SELECT s FROM SpAlertsMap s"),
    @NamedQuery(name = "SpAlertsMap.findById", query = "SELECT s FROM SpAlertsMap s WHERE s.id = :id"),
    @NamedQuery(name = "SpAlertsMap.findByAlertId", query = "SELECT s FROM SpAlertsMap s WHERE s.alertId = :alertId"),
    @NamedQuery(name = "SpAlertsMap.findByUserId", query = "SELECT s FROM SpAlertsMap s WHERE s.userId = :userId"),
    @NamedQuery(name = "SpAlertsMap.findBySmsEmail", query = "SELECT s FROM SpAlertsMap s WHERE s.smsEmail = :smsEmail")})
public class SpAlertsMap implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "ALERT_ID")
    private BigInteger alertId;
    @Column(name = "USER_ID")
    private BigInteger userId;
    @Size(max = 255)
    @Column(name = "SMS_EMAIL")
    private String smsEmail;

    public SpAlertsMap() {
    }

    public SpAlertsMap(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getAlertId() {
        return alertId;
    }

    public void setAlertId(BigInteger alertId) {
        this.alertId = alertId;
    }

    public BigInteger getUserId() {
        return userId;
    }

    public void setUserId(BigInteger userId) {
        this.userId = userId;
    }

    public String getSmsEmail() {
        return smsEmail;
    }

    public void setSmsEmail(String smsEmail) {
        this.smsEmail = smsEmail;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAlertsMap)) {
            return false;
        }
        SpAlertsMap other = (SpAlertsMap) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAlertsMap[ id=" + id + " ]";
    }
    
}
