package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AGENCY_PENDING_TRANSACTIONS")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "SpAgencyPendingTransactions.findAll", query = "SELECT s FROM SpAgencyPendingTransactions s"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findById", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.id = :id"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByTrxId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.trxId = :trxId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByOriginalTxnId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.originalTxnId = :originalTxnId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByClientId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.clientId = :clientId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByServiceId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.serviceId = :serviceId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByMsisdn", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.msisdn = :msisdn"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAmount", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.amount = :amount"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findBySpotcashCommission", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.spotcashCommission = :spotcashCommission"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByThirdpartyCharge", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.thirdpartyCharge = :thirdpartyCharge"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAmntColOne", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.amntColOne = :amntColOne"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAmntColTwo", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.amntColTwo = :amntColTwo"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByTrxStatus", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.trxStatus = :trxStatus"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByRespCode", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.respCode = :respCode"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByDescription", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.description = :description"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByRefId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.refId = :refId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByCustomerName", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.customerName = :customerName"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByThirdpartyInfo", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.thirdpartyInfo = :thirdpartyInfo"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByRequestTime", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.requestTime = :requestTime"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByRequestRespTime", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.requestRespTime = :requestRespTime"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByTimeInitiated", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.timeInitiated = :timeInitiated"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByTimeCompleted", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.timeCompleted = :timeCompleted"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAccesschannelId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.accesschannelId = :accesschannelId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByThirdpartySid", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.thirdpartySid = :thirdpartySid"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByBridgeIp", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.bridgeIp = :bridgeIp"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByOtherFields", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.otherFields = :otherFields"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByFailCount", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.failCount = :failCount"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByIntermediateStatus", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.intermediateStatus = :intermediateStatus"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByTransactionHash", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.transactionHash = :transactionHash"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByProcessorFailCount", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.processorFailCount = :processorFailCount"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findBySyncStatus", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.syncStatus = :syncStatus"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByConversationId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.conversationId = :conversationId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAgentId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.agentId = :agentId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByAgentStoreId", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.agentStoreId = :agentStoreId"),
        @NamedQuery(name = "SpAgencyPendingTransactions.findByCustBal", query = "SELECT s FROM SpAgencyPendingTransactions s WHERE s.custBal = :custBal")})
public class SpAgencyPendingTransactions implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;
    @Size(max = 100)
    @Column(name = "TRX_ID")
    private String trxId;
    @Size(max = 20)
    @Column(name = "ORIGINAL_TXN_ID")
    private String originalTxnId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;
    @Size(max = 20)
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "SPOTCASH_COMMISSION")
    private BigDecimal spotcashCommission;
    @Column(name = "THIRDPARTY_CHARGE")
    private BigDecimal thirdpartyCharge;
    @Column(name = "AMNT_COL_ONE")
    private BigDecimal amntColOne;
    @Column(name = "AMNT_COL_TWO")
    private BigDecimal amntColTwo;
    @Column(name = "TRX_STATUS")
    private BigInteger trxStatus;
    @Size(max = 255)
    @Column(name = "RESP_CODE")
    private String respCode;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 50)
    @Column(name = "REF_ID")
    private String refId;
    @Size(max = 100)
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    @Size(max = 100)
    @Column(name = "THIRDPARTY_INFO")
    private String thirdpartyInfo;
    @Column(name = "REQUEST_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestTime;
    @Column(name = "REQUEST_RESP_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestRespTime;
    @Column(name = "TIME_INITIATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeInitiated;
    @Column(name = "TIME_COMPLETED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeCompleted;
    @Column(name = "ACCESSCHANNEL_ID")
    private BigInteger accesschannelId;
    @Column(name = "THIRDPARTY_SID")
    private BigInteger thirdpartySid;
    @Size(max = 20)
    @Column(name = "BRIDGE_IP")
    private String bridgeIp;
    @Size(max = 1024)
    @Column(name = "OTHER_FIELDS")
    private String otherFields;
    @Column(name = "FAIL_COUNT")
    private BigInteger failCount;
    @Column(name = "INTERMEDIATE_STATUS")
    private BigInteger intermediateStatus;
    @Size(max = 100)
    @Column(name = "TRANSACTION_HASH")
    private String transactionHash;
    @Column(name = "PROCESSOR_FAIL_COUNT")
    private Integer processorFailCount;
    @Column(name = "SYNC_STATUS")
    private BigInteger syncStatus;
    @Size(max = 255)
    @Column(name = "CONVERSATION_ID")
    private String conversationId;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;
    @Column(name = "CUST_BAL")
    private BigDecimal custBal;

    public SpAgencyPendingTransactions() {
    }

    public SpAgencyPendingTransactions(BigInteger id) {
        this.id = id;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getOriginalTxnId() {
        return originalTxnId;
    }

    public void setOriginalTxnId(String originalTxnId) {
        this.originalTxnId = originalTxnId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSpotcashCommission() {
        return spotcashCommission;
    }

    public void setSpotcashCommission(BigDecimal spotcashCommission) {
        this.spotcashCommission = spotcashCommission;
    }

    public BigDecimal getThirdpartyCharge() {
        return thirdpartyCharge;
    }

    public void setThirdpartyCharge(BigDecimal thirdpartyCharge) {
        this.thirdpartyCharge = thirdpartyCharge;
    }

    public BigDecimal getAmntColOne() {
        return amntColOne;
    }

    public void setAmntColOne(BigDecimal amntColOne) {
        this.amntColOne = amntColOne;
    }

    public BigDecimal getAmntColTwo() {
        return amntColTwo;
    }

    public void setAmntColTwo(BigDecimal amntColTwo) {
        this.amntColTwo = amntColTwo;
    }

    public BigInteger getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(BigInteger trxStatus) {
        this.trxStatus = trxStatus;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getThirdpartyInfo() {
        return thirdpartyInfo;
    }

    public void setThirdpartyInfo(String thirdpartyInfo) {
        this.thirdpartyInfo = thirdpartyInfo;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    public Date getRequestRespTime() {
        return requestRespTime;
    }

    public void setRequestRespTime(Date requestRespTime) {
        this.requestRespTime = requestRespTime;
    }

    public Date getTimeInitiated() {
        return timeInitiated;
    }

    public void setTimeInitiated(Date timeInitiated) {
        this.timeInitiated = timeInitiated;
    }

    public Date getTimeCompleted() {
        return timeCompleted;
    }

    public void setTimeCompleted(Date timeCompleted) {
        this.timeCompleted = timeCompleted;
    }

    public BigInteger getAccesschannelId() {
        return accesschannelId;
    }

    public void setAccesschannelId(BigInteger accesschannelId) {
        this.accesschannelId = accesschannelId;
    }

    public BigInteger getThirdpartySid() {
        return thirdpartySid;
    }

    public void setThirdpartySid(BigInteger thirdpartySid) {
        this.thirdpartySid = thirdpartySid;
    }

    public String getBridgeIp() {
        return bridgeIp;
    }

    public void setBridgeIp(String bridgeIp) {
        this.bridgeIp = bridgeIp;
    }

    public String getOtherFields() {
        return otherFields;
    }

    public void setOtherFields(String otherFields) {
        this.otherFields = otherFields;
    }

    public BigInteger getFailCount() {
        return failCount;
    }

    public void setFailCount(BigInteger failCount) {
        this.failCount = failCount;
    }

    public BigInteger getIntermediateStatus() {
        return intermediateStatus;
    }

    public void setIntermediateStatus(BigInteger intermediateStatus) {
        this.intermediateStatus = intermediateStatus;
    }

    public String getTransactionHash() {
        return transactionHash;
    }

    public void setTransactionHash(String transactionHash) {
        this.transactionHash = transactionHash;
    }

    public Integer getProcessorFailCount() {
        return processorFailCount;
    }

    public void setProcessorFailCount(Integer processorFailCount) {
        this.processorFailCount = processorFailCount;
    }

    public BigInteger getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(BigInteger syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public BigDecimal getCustBal() {
        return custBal;
    }

    public void setCustBal(BigDecimal custBal) {
        this.custBal = custBal;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAgencyPendingTransactions)) {
            return false;
        }
        SpAgencyPendingTransactions other = (SpAgencyPendingTransactions) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAgencyPendingTransactions[ id=" + id + " ]";
    }
}
