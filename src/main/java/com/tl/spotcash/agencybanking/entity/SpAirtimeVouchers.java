/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AIRTIME_VOUCHERS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAirtimeVouchers.findAll", query = "SELECT s FROM SpAirtimeVouchers s"),
    @NamedQuery(name = "SpAirtimeVouchers.findById", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.id = :id"),
    @NamedQuery(name = "SpAirtimeVouchers.findByAmount", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpAirtimeVouchers.findBySerialNumber", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.serialNumber = :serialNumber"),
    @NamedQuery(name = "SpAirtimeVouchers.findByPin", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.pin = :pin"),
    @NamedQuery(name = "SpAirtimeVouchers.findByInvoiceNumber", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.invoiceNumber = :invoiceNumber"),
    @NamedQuery(name = "SpAirtimeVouchers.findByExpiryDate", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.expiryDate = :expiryDate"),
    @NamedQuery(name = "SpAirtimeVouchers.findByValidityPeriod", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.validityPeriod = :validityPeriod"),
    @NamedQuery(name = "SpAirtimeVouchers.findByTrxStatus", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.trxStatus = :trxStatus"),
    @NamedQuery(name = "SpAirtimeVouchers.findByAvailedOn", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.availedOn = :availedOn"),
    @NamedQuery(name = "SpAirtimeVouchers.findByPurchaseOn", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.purchaseOn = :purchaseOn"),
    @NamedQuery(name = "SpAirtimeVouchers.findByClientId", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAirtimeVouchers.findByAgentId", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpAirtimeVouchers.findByAgentStoreId", query = "SELECT s FROM SpAirtimeVouchers s WHERE s.agentStoreId = :agentStoreId")})
public class SpAirtimeVouchers implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "AMOUNT")
    private BigInteger amount;
    @Size(max = 100)
    @Column(name = "SERIAL_NUMBER")
    private String serialNumber;
    @Size(max = 100)
    @Column(name = "PIN")
    private String pin;
    @Size(max = 100)
    @Column(name = "INVOICE_NUMBER")
    private String invoiceNumber;
    @Size(max = 50)
    @Column(name = "EXPIRY_DATE")
    private String expiryDate;
    @Column(name = "VALIDITY_PERIOD")
    private BigInteger validityPeriod;
    @Column(name = "TRX_STATUS")
    private BigInteger trxStatus;
    @Column(name = "AVAILED_ON")
    @Temporal(TemporalType.TIMESTAMP)
    private Date availedOn;
    @Column(name = "PURCHASE_ON")
    @Temporal(TemporalType.TIMESTAMP)
    private Date purchaseOn;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;

    public SpAirtimeVouchers() {
    }

    public SpAirtimeVouchers(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getAmount() {
        return amount;
    }

    public void setAmount(BigInteger amount) {
        this.amount = amount;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public BigInteger getValidityPeriod() {
        return validityPeriod;
    }

    public void setValidityPeriod(BigInteger validityPeriod) {
        this.validityPeriod = validityPeriod;
    }

    public BigInteger getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(BigInteger trxStatus) {
        this.trxStatus = trxStatus;
    }

    public Date getAvailedOn() {
        return availedOn;
    }

    public void setAvailedOn(Date availedOn) {
        this.availedOn = availedOn;
    }

    public Date getPurchaseOn() {
        return purchaseOn;
    }

    public void setPurchaseOn(Date purchaseOn) {
        this.purchaseOn = purchaseOn;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAirtimeVouchers)) {
            return false;
        }
        SpAirtimeVouchers other = (SpAirtimeVouchers) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAirtimeVouchers[ id=" + id + " ]";
    }
    
}
