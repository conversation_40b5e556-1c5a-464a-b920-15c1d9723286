package com.tl.spotcash.agencybanking.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Date;

@Entity
@Table(name = "SP_AGENCY_ENCRYPTION_KEY")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Data
public class SpAgencyEncryptionKey {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigInteger id;

    @NotNull
    @Column(name = "DEVICE_ID")
    private String deviceId;

    @NotNull
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    @NotNull
    @Column(name = "ENCRYPTION_KEY")
    private String encryptionKey;

    @NotNull
    @Column(name = "IP_ADDRESS")
    private String ipAddress;

    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DATE_CREATED")
    private Date dateCreated;

}
