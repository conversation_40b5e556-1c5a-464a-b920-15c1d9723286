package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

@Entity
@Table(name = "SP_TOKENKEY")
public class SpTokenKey implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigDecimal id;

    @NotNull
    @Column(name = "STORE_USER_ID", unique = true)
    private BigInteger storeUserId;

    @NotNull
    @Column(name = "IS_VALID")
    private BigInteger validToken;

    @Size(max = 2000)
    @Column(name = "TOKEN")
    private String jwtToken;

    @Size(max = 255)
    @Column(name = "DATAKEY")
    private String dataKey;

    @Column(name = "REQUEST_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestTime;

    public SpTokenKey() {
    }

    public SpTokenKey(BigInteger storeUserId, String jwtToken, String dataKey, Date requestTime, BigInteger validToken) {
        this.storeUserId = storeUserId;
        this.jwtToken = jwtToken;
        this.dataKey = dataKey;
        this.requestTime = requestTime;
        this.validToken = validToken;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getValidToken() {
        return validToken;
    }

    public void setValidToken(BigInteger validToken) {
        this.validToken = validToken;
    }

    public BigInteger getStoreUserId() {
        return storeUserId;
    }

    public void setStoreUserId(BigInteger storeUserId) {
        this.storeUserId = storeUserId;
    }

    public String getJwtToken() {
        return jwtToken;
    }

    public void setJwtToken(String jwtToken) {
        this.jwtToken = jwtToken;
    }

    public String getDataKey() {
        return dataKey;
    }

    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    @Override
    public String toString() {
        return "SpTokenKey{" +
                "id=" + id +
                ", storeUserId=" + storeUserId +
                ", jwtToken='" + jwtToken + '\'' +
                ", dataKey='" + dataKey + '\'' +
                ", requestTime=" + requestTime +
                ", validToken=" +validToken +
                '}';
    }
}
