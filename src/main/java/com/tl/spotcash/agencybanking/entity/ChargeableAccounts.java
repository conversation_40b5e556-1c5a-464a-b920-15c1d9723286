package com.tl.spotcash.agencybanking.entity;

/**
 *
 * <AUTHOR>
 */

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class ChargeableAccounts {

    String TRANSACTIONFIELD;
    String DRPARTY;
    String CRPARTY;
    BigDecimal SPCOMMACCID;
    BigDecimal ISAMOUNTCHARGEABLE;//isAmountChargeable;
    Integer SERVICEID;//serviceId;
    Integer CLIENTID;//clientId;
    Integer AGENTSTOREID;//agentStoreId;
    String TRXPARTY;//trxParty;
    String FIELD;

    public String getTRANSACTIONFIELD() {
        return TRANSACTIONFIELD;
    }

    public void setTRANSACTIONFIELD(String TRANSACTIONFIELD) {
        this.TRANSACTIONFIELD = TRANSACTIONFIELD;
    }

    public String getDRPARTY() {
        return DRPARTY;
    }

    public void setDRPARTY(String DRPARTY) {
        this.DRPARTY = DRPARTY;
    }

    public String getCRPARTY() {
        return CRPARTY;
    }

    public void setCRPARTY(String CRPARTY) {
        this.CRPARTY = CRPARTY;
    }

    public BigDecimal getSPCOMMACCID() {
        return SPCOMMACCID;
    }

    public void setSPCOMMACCID(BigDecimal SPCOMMACCID) {
        this.SPCOMMACCID = SPCOMMACCID;
    }

    public BigDecimal getISAMOUNTCHARGEABLE() {
        return ISAMOUNTCHARGEABLE;
    }

    public void setISAMOUNTCHARGEABLE(BigDecimal ISAMOUNTCHARGEABLE) {
        this.ISAMOUNTCHARGEABLE = ISAMOUNTCHARGEABLE;
    }

    public Integer getSERVICEID() {
        return SERVICEID;
    }

    public void setSERVICEID(Integer SERVICEID) {
        this.SERVICEID = SERVICEID;
    }

    public Integer getCLIENTID() {
        return CLIENTID;
    }

    public void setCLIENTID(Integer CLIENTID) {
        this.CLIENTID = CLIENTID;
    }

    public Integer getAGENTSTOREID() {
        return AGENTSTOREID;
    }

    public void setAGENTSTOREID(Integer AGENTSTOREID) {
        this.AGENTSTOREID = AGENTSTOREID;
    }

    public String getTRXPARTY() {
        return TRXPARTY;
    }

    public void setTRXPARTY(String TRXPARTY) {
        this.TRXPARTY = TRXPARTY;
    }

    public String getFIELD() {
        return FIELD;
    }

    public void setFIELD(String FIELD) {
        this.FIELD = FIELD;
    }
}
