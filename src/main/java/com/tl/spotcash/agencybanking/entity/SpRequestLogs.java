/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_REQUEST_LOGS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpRequestLogs.findAll", query = "SELECT s FROM SpRequestLogs s"),
    @NamedQuery(name = "SpRequestLogs.findById", query = "SELECT s FROM SpRequestLogs s WHERE s.id = :id"),
    @NamedQuery(name = "SpRequestLogs.findByMsisdn", query = "SELECT s FROM SpRequestLogs s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpRequestLogs.findBySessionId", query = "SELECT s FROM SpRequestLogs s WHERE s.sessionId = :sessionId"),
    @NamedQuery(name = "SpRequestLogs.findByRequestParams", query = "SELECT s FROM SpRequestLogs s WHERE s.requestParams = :requestParams"),
    @NamedQuery(name = "SpRequestLogs.findByRespCode", query = "SELECT s FROM SpRequestLogs s WHERE s.respCode = :respCode"),
    @NamedQuery(name = "SpRequestLogs.findByDescription", query = "SELECT s FROM SpRequestLogs s WHERE s.description = :description"),
    @NamedQuery(name = "SpRequestLogs.findByDateCreated", query = "SELECT s FROM SpRequestLogs s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpRequestLogs.findByAccessChannelId", query = "SELECT s FROM SpRequestLogs s WHERE s.accessChannelId = :accessChannelId"),
    @NamedQuery(name = "SpRequestLogs.findByClientId", query = "SELECT s FROM SpRequestLogs s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpRequestLogs.findByServiceId", query = "SELECT s FROM SpRequestLogs s WHERE s.serviceId = :serviceId"),
    @NamedQuery(name = "SpRequestLogs.findByAmount", query = "SELECT s FROM SpRequestLogs s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpRequestLogs.findByPinNumber", query = "SELECT s FROM SpRequestLogs s WHERE s.pinNumber = :pinNumber"),
    @NamedQuery(name = "SpRequestLogs.findByLoanNumber", query = "SELECT s FROM SpRequestLogs s WHERE s.loanNumber = :loanNumber"),
    @NamedQuery(name = "SpRequestLogs.findByAccessChannel", query = "SELECT s FROM SpRequestLogs s WHERE s.accessChannel = :accessChannel")})
public class SpRequestLogs implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Size(max = 255)
    @Column(name = "SESSION_ID")
    private String sessionId;
    @Size(max = 255)
    @Column(name = "REQUEST_PARAMS")
    private String requestParams;
    @Size(max = 255)
    @Column(name = "RESP_CODE")
    private String respCode;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "ACCESS_CHANNEL_ID")
    private BigInteger accessChannelId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "PIN_NUMBER")
    private BigInteger pinNumber;
    @Size(max = 255)
    @Column(name = "LOAN_NUMBER")
    private String loanNumber;
    @Size(max = 255)
    @Column(name = "ACCESS_CHANNEL")
    private String accessChannel;

    public SpRequestLogs() {
    }

    public SpRequestLogs(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigInteger getAccessChannelId() {
        return accessChannelId;
    }

    public void setAccessChannelId(BigInteger accessChannelId) {
        this.accessChannelId = accessChannelId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigInteger getPinNumber() {
        return pinNumber;
    }

    public void setPinNumber(BigInteger pinNumber) {
        this.pinNumber = pinNumber;
    }

    public String getLoanNumber() {
        return loanNumber;
    }

    public void setLoanNumber(String loanNumber) {
        this.loanNumber = loanNumber;
    }

    public String getAccessChannel() {
        return accessChannel;
    }

    public void setAccessChannel(String accessChannel) {
        this.accessChannel = accessChannel;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpRequestLogs)) {
            return false;
        }
        SpRequestLogs other = (SpRequestLogs) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpRequestLogs[ id=" + id + " ]";
    }
    
}
