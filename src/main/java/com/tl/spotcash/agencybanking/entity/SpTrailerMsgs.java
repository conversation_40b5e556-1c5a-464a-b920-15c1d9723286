/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TRAILER_MSGS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTrailerMsgs.findAll", query = "SELECT s FROM SpTrailerMsgs s"),
    @NamedQuery(name = "SpTrailerMsgs.findById", query = "SELECT s FROM SpTrailerMsgs s WHERE s.id = :id"),
    @NamedQuery(name = "SpTrailerMsgs.findByTitle", query = "SELECT s FROM SpTrailerMsgs s WHERE s.title = :title"),
    @NamedQuery(name = "SpTrailerMsgs.findByMessage", query = "SELECT s FROM SpTrailerMsgs s WHERE s.message = :message"),
    @NamedQuery(name = "SpTrailerMsgs.findByStatus", query = "SELECT s FROM SpTrailerMsgs s WHERE s.status = :status"),
    @NamedQuery(name = "SpTrailerMsgs.findByDateCreated", query = "SELECT s FROM SpTrailerMsgs s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpTrailerMsgs.findByIntrash", query = "SELECT s FROM SpTrailerMsgs s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpTrailerMsgs.findByMsgType", query = "SELECT s FROM SpTrailerMsgs s WHERE s.msgType = :msgType")})
public class SpTrailerMsgs implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "MESSAGE")
    private String message;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Size(max = 255)
    @Column(name = "MSG_TYPE")
    private String msgType;

    public SpTrailerMsgs() {
    }

    public SpTrailerMsgs(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTrailerMsgs)) {
            return false;
        }
        SpTrailerMsgs other = (SpTrailerMsgs) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTrailerMsgs[ id=" + id + " ]";
    }
    
}
