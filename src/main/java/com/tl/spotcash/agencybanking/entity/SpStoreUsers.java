/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_STORE_USERS")
@XmlRootElement
@NamedQueries({
	@NamedQuery(name = "SpStoreUsers.findAll", query = "SELECT s FROM SpStoreUsers s"),
	@NamedQuery(name = "SpStoreUsers.resetPin", query = "UPDATE SpStoreUsers SET pin = :newPin WHERE contactMsisdn  = :msisdn"),
	@NamedQuery(name = "SpStoreUsers.findByContactName", query = "SELECT s FROM SpStoreUsers s WHERE s.contactName = :contactName"),
	@NamedQuery(name = "SpStoreUsers.findByContactMsisdn", query = "SELECT s FROM SpStoreUsers s WHERE s.contactMsisdn = :contactMsisdn"),
	@NamedQuery(name = "SpStoreUsers.findByDateCreated", query = "SELECT s FROM SpStoreUsers s WHERE s.dateCreated = :dateCreated"),
	@NamedQuery(name = "SpStoreUsers.findByIntrash", query = "SELECT s FROM SpStoreUsers s WHERE s.intrash = :intrash"),
	@NamedQuery(name = "SpStoreUsers.findByActiveStatus", query = "SELECT s FROM SpStoreUsers s WHERE s.activeStatus = :activeStatus"),
	@NamedQuery(name = "SpStoreUsers.findByPin", query = "SELECT s FROM SpStoreUsers s WHERE s.pin = :pin"),
	@NamedQuery(name = "SpStoreUsers.findByPassword", query = "SELECT s FROM SpStoreUsers s WHERE s.password = :password"),
    @NamedQuery(name = "SpStoreUsers.updatePin", query = "UPDATE SpStoreUsers SET pin = :newPin WHERE contactMsisdn  = :Msisdn")})
public class SpStoreUsers implements Serializable, UserDetails {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "STORE_USER_ID")
	private BigInteger storeUserId;
	@Column(name = "AGENT_STORE_ID")
	private BigInteger agentStoreId;
	@Size(max = 255)
	@Column(name = "CONTACT_NAME")
	private String contactName;
	@Size(max = 255)
	@Column(name = "CONTACT_MSISDN")
	private String contactMsisdn;
	@Column(name = "DATE_CREATED")
	@Temporal(TemporalType.TIMESTAMP)
	private Date dateCreated;
	@Size(max = 255)
	@Column(name = "INTRASH")
	private String intrash;
	@Column(name = "ACTIVE_STATUS")
	private BigInteger activeStatus;
	@Size(max = 255)
	@Column(name = "PIN")
	private String pin;
	@Size(max = 255)
	@Column(name = "PASSWORD")
	private String password;
	@Size(max = 255)
	@Column(name = "CLIENT_ID")
	private BigInteger clientId;

	@Column(name = "AGENT_TYPE")
	private BigInteger agent_type;

	@Column(name = "PIN_RETRIES")
	private Integer pinRetries;

	@Column(name = "DEACTIVATION_COUNT")
	private int deactivationCount;

	/*@Column(name = "ACCOUNT_NUMBER")
	@Getter @Setter
	private String accountNumber;

	@Column(name = "COMMISSION_ACCOUNT_NUMBER")
	@Getter @Setter
	private String commissionAccountNumber;*/

	public SpStoreUsers() {
	}

	public SpStoreUsers(BigInteger storeUserId) {
		this.storeUserId = storeUserId;
	}

	public BigInteger getStoreUserId() {
		return storeUserId;
	}

	public void setStoreUserId(BigInteger storeUserId) {
		this.storeUserId = storeUserId;
	}

	public BigInteger getAgentStoreId() {
		return agentStoreId;
	}

	public void setAgentStoreId(BigInteger agentStoreId) {
		this.agentStoreId = agentStoreId;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMsisdn() {
		return contactMsisdn;
	}

	public void setContactMsisdn(String contactMsisdn) {
		this.contactMsisdn = contactMsisdn;
	}

	public Date getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public String getIntrash() {
		return intrash;
	}

	public void setIntrash(String intrash) {
		this.intrash = intrash;
	}

	public BigInteger getActiveStatus() {
		return activeStatus;
	}

	public void setActiveStatus(BigInteger activeStatus) {
		this.activeStatus = activeStatus;
	}

	public String getPin() {
		return pin;
	}

	public void setPin(String pin) {
		this.pin = pin;
	}

	// Note: Use this getter to get the password value
	public String getPasswordValue() {
		return password;
	}

	// This getter is used by spring security to get the password,
	// which in this case is the Pin of the store_user/Agent.
	@Override
	public String getPassword(){ return pin; }

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		return null;
	}
	@Override
	public String getUsername() {
		return contactMsisdn;
	}

	@Override
	public boolean isAccountNonExpired() {
		return intrash.trim().equalsIgnoreCase("NO");
	}

	@Override
	public boolean isAccountNonLocked() {
		return true;
	}

	@Override
	public boolean isCredentialsNonExpired() {
		return true;
	}

	@Override
	public boolean isEnabled() {
		return activeStatus == BigInteger.ONE;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public BigInteger getClientId() {
		return clientId;
	}

	public void setClientId(BigInteger clientId) {
		this.clientId = clientId;
	}

	public BigInteger getAgent_type() {
		return agent_type;
	}

	public void setAgent_type(BigInteger agent_type) {
		this.agent_type = agent_type;
	}

	public Integer getPinRetries() {return pinRetries;}

	public void setPinRetries(Integer pinRetries) {this.pinRetries = pinRetries;}

	public int getDeactivationCount() {return deactivationCount;}

	public void setDeactivationCount(int deactivationCount) {this.deactivationCount = deactivationCount;}

	@Override
	public int hashCode() {
		int hash = 0;
		hash += (agentStoreId != null ? agentStoreId.hashCode() : 0);
		return hash;
	}

	@Override
	public boolean equals(Object object) {
		// TODO: Warning - this method won't work in the case the id fields are not set
		if (!(object instanceof SpStoreUsers)) {
			return false;
		}
		SpStoreUsers other = (SpStoreUsers) object;
		if ((this.agentStoreId == null && other.agentStoreId != null) || (this.agentStoreId != null && !this.agentStoreId.equals(other.agentStoreId))) {
			return false;
		}
		return true;
	}

	@Override
	public String toString() {
		return "SpStoreUsers{" + "agentStoreId=" + agentStoreId + ", contactName=" + contactName + ", contactMsisdn=" + contactMsisdn + ", dateCreated=" + dateCreated + ", intrash=" + intrash + ", activeStatus=" + activeStatus + ", pin=" + pin + ", password=" + password + '}';
	}

}