package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TRANS_RESPONSE_TABLE")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTransResponseTable.findAll", query = "SELECT s FROM SpTransResponseTable s"),
    @NamedQuery(name = "SpTransResponseTable.findById", query = "SELECT s FROM SpTransResponseTable s WHERE s.id = :id"),
    @NamedQuery(name = "SpTransResponseTable.findByTrxId", query = "SELECT s FROM SpTransResponseTable s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpTransResponseTable.findByMessage", query = "SELECT s FROM SpTransResponseTable s WHERE s.message = :message")})
public class SpTransResponseTable implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SEQ_SP_TRANS_RESPONSE_TABLE")
    @SequenceGenerator(name = "SEQ_SP_TRANS_RESPONSE_TABLE", sequenceName = "SEQ_SP_TRANS_RESPONSE_TABLE", allocationSize = 1)
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Size(max = 3000)
    @Column(name = "MESSAGE")
    private String message;

    public SpTransResponseTable() {
    }

    public SpTransResponseTable(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTransResponseTable)) {
            return false;
        }
        SpTransResponseTable other = (SpTransResponseTable) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTransResponseTable[ id=" + id + " ]";
    }

}
