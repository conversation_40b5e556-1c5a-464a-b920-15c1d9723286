/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_SETTINGS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpSettings.findAll", query = "SELECT s FROM SpSettings s"),
    @NamedQuery(name = "SpSettings.findById", query = "SELECT s FROM SpSettings s WHERE s.id = :id"),
    @NamedQuery(name = "SpSettings.findByLastEodTime", query = "SELECT s FROM SpSettings s WHERE s.lastEodTime = :lastEodTime"),
    @NamedQuery(name = "SpSettings.findByEodHour", query = "SELECT s FROM SpSettings s WHERE s.eodHour = :eodHour"),
    @NamedQuery(name = "SpSettings.findByEodInProgress", query = "SELECT s FROM SpSettings s WHERE s.eodInProgress = :eodInProgress"),
    @NamedQuery(name = "SpSettings.findByLastArchiveTime", query = "SELECT s FROM SpSettings s WHERE s.lastArchiveTime = :lastArchiveTime"),
    @NamedQuery(name = "SpSettings.findByArchiveInProgress", query = "SELECT s FROM SpSettings s WHERE s.archiveInProgress = :archiveInProgress"),
    @NamedQuery(name = "SpSettings.findByLastRegularUpdate", query = "SELECT s FROM SpSettings s WHERE s.lastRegularUpdate = :lastRegularUpdate")})
public class SpSettings implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "LAST_EOD_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastEodTime;
    @Column(name = "EOD_HOUR")
    private BigInteger eodHour;
    @Column(name = "EOD_IN_PROGRESS")
    private BigInteger eodInProgress;
    @Column(name = "LAST_ARCHIVE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastArchiveTime;
    @Column(name = "ARCHIVE_IN_PROGRESS")
    private BigInteger archiveInProgress;
    @Column(name = "LAST_REGULAR_UPDATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastRegularUpdate;

    public SpSettings() {
    }

    public SpSettings(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Date getLastEodTime() {
        return lastEodTime;
    }

    public void setLastEodTime(Date lastEodTime) {
        this.lastEodTime = lastEodTime;
    }

    public BigInteger getEodHour() {
        return eodHour;
    }

    public void setEodHour(BigInteger eodHour) {
        this.eodHour = eodHour;
    }

    public BigInteger getEodInProgress() {
        return eodInProgress;
    }

    public void setEodInProgress(BigInteger eodInProgress) {
        this.eodInProgress = eodInProgress;
    }

    public Date getLastArchiveTime() {
        return lastArchiveTime;
    }

    public void setLastArchiveTime(Date lastArchiveTime) {
        this.lastArchiveTime = lastArchiveTime;
    }

    public BigInteger getArchiveInProgress() {
        return archiveInProgress;
    }

    public void setArchiveInProgress(BigInteger archiveInProgress) {
        this.archiveInProgress = archiveInProgress;
    }

    public Date getLastRegularUpdate() {
        return lastRegularUpdate;
    }

    public void setLastRegularUpdate(Date lastRegularUpdate) {
        this.lastRegularUpdate = lastRegularUpdate;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpSettings)) {
            return false;
        }
        SpSettings other = (SpSettings) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpSettings[ id=" + id + " ]";
    }
    
}
