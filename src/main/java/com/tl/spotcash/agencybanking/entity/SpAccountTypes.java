/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ACCOUNT_TYPES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAccountTypes.findAll", query = "SELECT s FROM SpAccountTypes s"),
    @NamedQuery(name = "SpAccountTypes.findById", query = "SELECT s FROM SpAccountTypes s WHERE s.id = :id"),
    @NamedQuery(name = "SpAccountTypes.findByTitle", query = "SELECT s FROM SpAccountTypes s WHERE s.title = :title"),
    @NamedQuery(name = "SpAccountTypes.findByDescription", query = "SELECT s FROM SpAccountTypes s WHERE s.description = :description"),
    @NamedQuery(name = "SpAccountTypes.findByIntrash", query = "SELECT s FROM SpAccountTypes s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpAccountTypes.findByDateCreated", query = "SELECT s FROM SpAccountTypes s WHERE s.dateCreated = :dateCreated")})
public class SpAccountTypes implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;

    public SpAccountTypes() {
    }

    public SpAccountTypes(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAccountTypes)) {
            return false;
        }
        SpAccountTypes other = (SpAccountTypes) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAccountTypes[ id=" + id + " ]";
    }
    
}
