/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_SERVICE_SUBSCRIPTIONS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpServiceSubscriptions.findAll", query = "SELECT s FROM SpServiceSubscriptions s"),
    @NamedQuery(name = "SpServiceSubscriptions.findById", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.id = :id"),
    @NamedQuery(name = "SpServiceSubscriptions.findByClientId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByServiceId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.serviceId = :serviceId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByStatus", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.status = :status"),
    @NamedQuery(name = "SpServiceSubscriptions.findByMessage", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.message = :message"),
    @NamedQuery(name = "SpServiceSubscriptions.findByMinAmount", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.minAmount = :minAmount"),
    @NamedQuery(name = "SpServiceSubscriptions.findByMaxAmount", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.maxAmount = :maxAmount"),
    @NamedQuery(name = "SpServiceSubscriptions.findByMaxDaily", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.maxDaily = :maxDaily"),
    @NamedQuery(name = "SpServiceSubscriptions.findByTariffId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.tariffId = :tariffId"),
    @NamedQuery(name = "SpServiceSubscriptions.findBySpotcashAccountId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.spotcashAccountId = :spotcashAccountId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByTimeoutInmins", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.timeoutInmins = :timeoutInmins"),
    @NamedQuery(name = "SpServiceSubscriptions.findByDateCreated", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpServiceSubscriptions.findByServiceAlias", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.serviceAlias = :serviceAlias"),
    @NamedQuery(name = "SpServiceSubscriptions.findByAgentId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByAmountAccId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.amountAccId = :amountAccId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByCommAccId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.commAccId = :commAccId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByChargeAccId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.chargeAccId = :chargeAccId"),
    @NamedQuery(name = "SpServiceSubscriptions.findByAgentStoreId", query = "SELECT s FROM SpServiceSubscriptions s WHERE s.agentStoreId = :agentStoreId")})
public class SpServiceSubscriptions implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Size(max = 255)
    @Column(name = "MESSAGE")
    private String message;
    @Column(name = "MIN_AMOUNT")
    private BigDecimal minAmount;
    @Column(name = "MAX_AMOUNT")
    private BigDecimal maxAmount;
    @Column(name = "MAX_DAILY")
    private BigInteger maxDaily;
    @Size(max = 255)
    @Column(name = "TARIFF_ID")
    private String tariffId;
    @Size(max = 255)
    @Column(name = "SPOTCASH_ACCOUNT_ID")
    private String spotcashAccountId;
    @Column(name = "TIMEOUT_INMINS")
    private BigInteger timeoutInmins;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "SERVICE_ALIAS")
    private String serviceAlias;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AMOUNT_ACC_ID")
    private BigInteger amountAccId;
    @Column(name = "COMM_ACC_ID")
    private BigInteger commAccId;
    @Column(name = "CHARGE_ACC_ID")
    private BigInteger chargeAccId;
    @Size(max = 255)
    @Column(name = "AGENT_STORE_ID")
    private String agentStoreId;
    @Size(max = 1000)
    @Column(name = "AGENCY_MESSAGES")
    private String agencyMessages;

    public SpServiceSubscriptions() {
    }

    public SpServiceSubscriptions(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public BigInteger getMaxDaily() {
        return maxDaily;
    }

    public void setMaxDaily(BigInteger maxDaily) {
        this.maxDaily = maxDaily;
    }

    public String getTariffId() {
        return tariffId;
    }

    public void setTariffId(String tariffId) {
        this.tariffId = tariffId;
    }

    public String getSpotcashAccountId() {
        return spotcashAccountId;
    }

    public void setSpotcashAccountId(String spotcashAccountId) {
        this.spotcashAccountId = spotcashAccountId;
    }

    public BigInteger getTimeoutInmins() {
        return timeoutInmins;
    }

    public void setTimeoutInmins(BigInteger timeoutInmins) {
        this.timeoutInmins = timeoutInmins;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getServiceAlias() {
        return serviceAlias;
    }

    public void setServiceAlias(String serviceAlias) {
        this.serviceAlias = serviceAlias;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAmountAccId() {
        return amountAccId;
    }

    public void setAmountAccId(BigInteger amountAccId) {
        this.amountAccId = amountAccId;
    }

    public BigInteger getCommAccId() {
        return commAccId;
    }

    public void setCommAccId(BigInteger commAccId) {
        this.commAccId = commAccId;
    }

    public BigInteger getChargeAccId() {
        return chargeAccId;
    }

    public void setChargeAccId(BigInteger chargeAccId) {
        this.chargeAccId = chargeAccId;
    }

    public String getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(String agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public String getAgencyMessages() {
        return agencyMessages;
    }

    public void setAgencyMessages(String agencyMessages) {
        this.agencyMessages = agencyMessages;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpServiceSubscriptions)) {
            return false;
        }
        SpServiceSubscriptions other = (SpServiceSubscriptions) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpServiceSubscriptions[ id=" + id + " ]";
    }
    
}
