/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_REQUESTS_TABLE")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpRequestsTable.findAll", query = "SELECT s FROM SpRequestsTable s"),
    @NamedQuery(name = "SpRequestsTable.findById", query = "SELECT s FROM SpRequestsTable s WHERE s.id = :id"),
    @NamedQuery(name = "SpRequestsTable.findByTrxId", query = "SELECT s FROM SpRequestsTable s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpRequestsTable.findByClientId", query = "SELECT s FROM SpRequestsTable s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpRequestsTable.findByServiceId", query = "SELECT s FROM SpRequestsTable s WHERE s.serviceId = :serviceId"),
    @NamedQuery(name = "SpRequestsTable.findByMsisdn", query = "SELECT s FROM SpRequestsTable s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpRequestsTable.findByAmount", query = "SELECT s FROM SpRequestsTable s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpRequestsTable.findBySpotcashCommission", query = "SELECT s FROM SpRequestsTable s WHERE s.spotcashCommission = :spotcashCommission"),
    @NamedQuery(name = "SpRequestsTable.findByThirdpartyCharge", query = "SELECT s FROM SpRequestsTable s WHERE s.thirdpartyCharge = :thirdpartyCharge"),
    @NamedQuery(name = "SpRequestsTable.findByTrxStatus", query = "SELECT s FROM SpRequestsTable s WHERE s.trxStatus = :trxStatus"),
    @NamedQuery(name = "SpRequestsTable.findByRespCode", query = "SELECT s FROM SpRequestsTable s WHERE s.respCode = :respCode"),
    @NamedQuery(name = "SpRequestsTable.findByDescription", query = "SELECT s FROM SpRequestsTable s WHERE s.description = :description"),
    @NamedQuery(name = "SpRequestsTable.findByRefId", query = "SELECT s FROM SpRequestsTable s WHERE s.refId = :refId"),
    @NamedQuery(name = "SpRequestsTable.findByCustomerName", query = "SELECT s FROM SpRequestsTable s WHERE s.customerName = :customerName"),
    @NamedQuery(name = "SpRequestsTable.findByRequestTime", query = "SELECT s FROM SpRequestsTable s WHERE s.requestTime = :requestTime"),
    @NamedQuery(name = "SpRequestsTable.findByTimeCompleted", query = "SELECT s FROM SpRequestsTable s WHERE s.timeCompleted = :timeCompleted")})
public class SpRequestsTable implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "SERVICE_ID")
    private String serviceId;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "SPOTCASH_COMMISSION")
    private BigInteger spotcashCommission;
    @Column(name = "THIRDPARTY_CHARGE")
    private BigInteger thirdpartyCharge;
    @Column(name = "TRX_STATUS")
    private BigInteger trxStatus;
    @Size(max = 255)
    @Column(name = "RESP_CODE")
    private String respCode;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "REF_ID")
    private String refId;
    @Size(max = 255)
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    @Size(max = 255)
    @Column(name = "REQUEST_TIME")
    private String requestTime;
    @Column(name = "TIME_COMPLETED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeCompleted;

    public SpRequestsTable() {
    }

    public SpRequestsTable(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigInteger getSpotcashCommission() {
        return spotcashCommission;
    }

    public void setSpotcashCommission(BigInteger spotcashCommission) {
        this.spotcashCommission = spotcashCommission;
    }

    public BigInteger getThirdpartyCharge() {
        return thirdpartyCharge;
    }

    public void setThirdpartyCharge(BigInteger thirdpartyCharge) {
        this.thirdpartyCharge = thirdpartyCharge;
    }

    public BigInteger getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(BigInteger trxStatus) {
        this.trxStatus = trxStatus;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    public Date getTimeCompleted() {
        return timeCompleted;
    }

    public void setTimeCompleted(Date timeCompleted) {
        this.timeCompleted = timeCompleted;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpRequestsTable)) {
            return false;
        }
        SpRequestsTable other = (SpRequestsTable) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpRequestsTable[ id=" + id + " ]";
    }
    
}
