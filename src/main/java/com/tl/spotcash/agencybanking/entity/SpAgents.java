/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @co-author mwendwakelvin
 */
@Entity
@Table(name = "SP_AGENTS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAgents.findAll", query = "SELECT s FROM SpAgents s"),
    @NamedQuery(name = "SpAgents.findByAgentId", query = "SELECT s FROM SpAgents s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpAgents.findByClientId", query = "SELECT s FROM SpAgents s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAgents.findByAgentName", query = "SELECT s FROM SpAgents s WHERE s.agentName = :agentName"),
    @NamedQuery(name = "SpAgents.findByPassKey", query = "SELECT s FROM SpAgents s WHERE s.passKey = :passKey"),
    @NamedQuery(name = "SpAgents.findByActiveStatus", query = "SELECT s FROM SpAgents s WHERE s.activeStatus = :activeStatus"),
    @NamedQuery(name = "SpAgents.findByInTrash", query = "SELECT s FROM SpAgents s WHERE s.inTrash = :inTrash"),
    @NamedQuery(name = "SpAgents.findByDateCreated", query = "SELECT s FROM SpAgents s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpAgents.findByBackgroundColour", query = "SELECT s FROM SpAgents s WHERE s.backgroundColour = :backgroundColour"),
    @NamedQuery(name = "SpAgents.findByClientLogo", query = "SELECT s FROM SpAgents s WHERE s.clientLogo = :clientLogo"),
    @NamedQuery(name = "SpAgents.findByTagline", query = "SELECT s FROM SpAgents s WHERE s.tagline = :tagline"),
    @NamedQuery(name = "SpAgents.findByReceiptLogo", query = "SELECT s FROM SpAgents s WHERE s.receiptLogo = :receiptLogo")})
public class SpAgents implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AGENT_ID")
    private BigDecimal agentId;
    @Size(max = 255)
    @Column(name = "AGENT_NAME")
    private String agentName;
    @Size(max = 255)
    @Column(name = "PASSKEY")
    private String passKey;
    @Column(name = "ACTIVE_STATUS")
    private BigInteger activeStatus;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String inTrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "BACKGROUND_COLOUR")
    private String backgroundColour;
    @Lob
    @Column(name = "CLIENT_LOGO")
    private String clientLogo;
    @Lob
    @Column(name = "RECEIPT_LOGO")
    private String receiptLogo;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "TAGLINE")
    private String tagline;
    @Size(max = 255)
    @Column(name = "FONT_COLOUR")
    private String fontColour;
    @Column(name = "PRINT_ENQUIRY")
    private BigInteger printEnquiry;
    @Column(name = "CBS_AUTH")
    private BigInteger cbsAuth;
    @Column(name = "ACCOUNT_DISPLAY")
    private BigInteger accountDisplay;
    @Column(name = "PRINT_AGENT_ID")
    private BigInteger printAgentId;
    @Column(name = "USE_NUMBER_KEYBOARD")
    private BigInteger useNumberKeyboard;
    @Column(name = "DISPLAY_ZREPORT")
    private BigInteger displayZReport;
    @Column(name = "AGENT_BALANCE")
    private BigInteger agentBalance;
    @Column(name = "SMS_URL")
    private String smsUrl;
    @Column(name = "ACCOUNT_VERIFICATION")
    private BigInteger accountVerification;
    @Column(name = "MEMBER_IMAGE")
    private BigInteger memberImage;
    @Column(name = "PRINT_ACCOUNT_NO")
    private BigInteger printAccountNo;
    @Column(name = "LOCK_ACCOUNT")
    private BigInteger lockAccount;
    @Column(name = "LOCK_COUNT")
    private BigInteger lockCount;
    @Column(name = "AUTHENTICATE_AGENT_TRANSACTION")
    private BigInteger authenticateAgentTransaction;
    @Column(name = "NM_ACCOUNT")
    private BigInteger displayNm_Trx;
    @Column(name = "NEW_PRINT_DESIGN")
    private BigInteger newPrintDesign;
    @Column(name = "ALLOW_ACCOUNT_NAME")
    private BigInteger allow_account_name;
    @Column(name = "FORCED_UPDATE")
    private BigInteger forcedUpdate;
    @Size(max = 250)
    @Column(name = "VERSION_OLD")
    private String versionOld;
    @Size(max = 250)
    @Column(name = "VERSION_NEW")
    private String versionNew;
    @Column(name = "PRINT_ACCOUNT_NAME")
    private BigInteger printAccountname;
    @Column(name = "WITHDRAWAL_SMS")
    private BigInteger enable_withdrawals_sms;
    @Column(name = "MEM_REG_REQ_ID_IMAGE")
    private BigInteger memRegReqIdImage;
    @Column(name = "MEM_REG_REQ_PASSPORT_IMAGE")
    private BigInteger memRegReqPassportImage;
    @Column(name = "MEM_REG_REQ_SIGNATURE_IMAGE")
    private BigInteger memRegReqSignatureImage;

    @Column(name = "MEM_REG_REQ_REG_FORM_IMAGE")
    private BigInteger memRegReqRegFormImage;

    @Column(name = "MEM_REG_REQ_NEW_BIO_DATA")
    private BigInteger memRegReqNewBioData;

    @Column(name = "RECEIPT_CONFIG")
    private String  receiptConfig;

    @Column(name = "RECEIPT_LABELS")
    private String  receiptLabels;

    @Column(name = "INACTIVE_TIMEOUT")
    private BigInteger inactiveTimeout;

    @Column(name = "AGENCY_SERVICES")
    private String  agencyServices;

    @Column(name = "PRINT_AGENT_RECEIPT")
    private BigInteger printAgentReceipt;

    @Lob
    @Column(name = "AGENT_RECEIPT_CONFIG")
    private String agentReceiptConfig;

    @Column(name = "STORE_BLACKLIST_COUNT")
    private BigInteger storeBlacklistCount;

    @Column(name = "STORE_BLACKLIST_TIME")
    private Double storeBlacklistTime;

    @Column(name = "OTP_RETRIES")
    private int otpRetries;

    @Column(name = "DEVICE_CAPTURE")
    private BigInteger deviceCapture;

    @Column(name = "WORKING_HOURS")
    private BigInteger workingHours;

    @Column(name = "SIM_BINDING")
    private BigInteger simBinding;

    @Column(name = "IMSI_INTERVAL")
    private int imsiInterval;

    @Column(name = "FENCE")
    private BigInteger fence;

    @Column(name = "DEACTIVATION_COUNT")
    private int deactivationCount;

    @Column(name = "CUSTOMER_PIN_RETRIES")
    private Integer customerPinRetries;
    @Column(name = "ACCOUNT_OVERDRAFT")
    private BigInteger accountOverdraft;
    @Column(name = "AGENT_TRANSACTION_SMS")
    private BigInteger agentTransactionSms;

    @Column(name = "OTP_LOGIN")
    private BigInteger otpLogin;

    @Getter
    @Setter
    @Transient
    private boolean mobileWebservice;

    public BigInteger getFence() {
        return fence;
    }

    public void setFence(BigInteger fence) {
        this.fence = fence;
    }

    public int getImsiInterval() {
        return imsiInterval;
    }

    public void setImsiInterval(int imsiInterval) {
        this.imsiInterval = imsiInterval;
    }

    public BigInteger getSimBinding() {
        return simBinding;
    }

    public void setSimBinding(BigInteger simBinding) {
        this.simBinding = simBinding;
    }
    @Column(name = "SENDER_ID")
    private String senderId;

    public BigInteger getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigInteger workingHours) {
        this.workingHours = workingHours;
    }

    public SpAgents() {
    }

    public BigInteger getDeviceCapture() {
        return deviceCapture;
    }

    public void setDeviceCapture(BigInteger deviceCapture) {
        this.deviceCapture = deviceCapture;
    }

    public BigInteger getInactiveTimeout() {
        return inactiveTimeout;
    }

    public void setInactiveTimeout(BigInteger inactiveTimeout) {
        this.inactiveTimeout = inactiveTimeout;
    }

    public String getReceiptLabels() {
        return receiptLabels;
    }

    public void setReceiptLabels(String receiptLabels) {
        this.receiptLabels = receiptLabels;
    }

    public SpAgents(BigDecimal agentId) {
        this.agentId = agentId;
    }

    public BigDecimal getAgentId() {
        return agentId;
    }

    public void setAgentId(BigDecimal agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getPassKey() {
        return passKey;
    }

    public void setPasskey(String passKey) {
        this.passKey = passKey;
    }

    public BigInteger getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(BigInteger activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getInTrash() {
        return inTrash;
    }

    public void setIntrash(String inTrash) {
        this.inTrash = inTrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getBackgroundColour() {
        return backgroundColour;
    }

    public void setBackgroundColour(String backgroundColour) {
        this.backgroundColour = backgroundColour;
    }

    public String getClientLogo() {
        return clientLogo;
    }

    public void setClientLogo(String clientLogo) {
        this.clientLogo = clientLogo;
    }

    public String getReceiptLogo() {
        return receiptLogo;
    }

    public void setReceiptLogo(String receiptLogo) {
        this.receiptLogo = receiptLogo;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getTagline() {
        return tagline;
    }

    public void setTagline(String tagline) {
        this.tagline = tagline;
    }

    public String getFontColour() {
        return fontColour;
    }

    public void setFontColour(String fontColour) {
        this.fontColour = fontColour;
    }

    public BigInteger getPrintEnquiry() {
        return printEnquiry;
    }

    public void setPrintEnquiry(BigInteger printEnquiry) {
        this.printEnquiry = printEnquiry;
    }

    public BigInteger getCbsAuth() {
        return cbsAuth;
    }

    public void setCbsAuth(BigInteger cbsAuth) {
        this.cbsAuth = cbsAuth;
    }

    public BigInteger getAccountDisplay() {
        return accountDisplay;
    }

    public void setAccountDisplay(BigInteger accountDisplay) {
        this.accountDisplay = accountDisplay;
    }

    public BigInteger getPrintAgentId() {
        return printAgentId;
    }

    public void setPrintAgentId(BigInteger printAgentId) {
        this.printAgentId = printAgentId;
    }

    public BigInteger getUseNumberKeyboard() {
        return useNumberKeyboard;
    }

    public void setUseNumberKeyboard(BigInteger useNumberKeyboard) {
        this.useNumberKeyboard = useNumberKeyboard;
    }

    public BigInteger getDisplayZReport() {
        return displayZReport;
    }

    public void setDisplayZReport(BigInteger displayZReport) {
        this.displayZReport = displayZReport;
    }

    public BigInteger getAgentBalance() {
        return agentBalance;
    }

    public void setAgentBalance(BigInteger agentBalance) {
        this.agentBalance = agentBalance;
    }

    public String getSmsUrl() {
        return smsUrl;
    }

    public void setSmsUrl(String smsUrl) {
        this.smsUrl = smsUrl;
    }

    public BigInteger getAccountVerification() {
        return accountVerification;
    }

    public void setAccountVerification(BigInteger accountVerification) {
        this.accountVerification = accountVerification;
    }

    public BigInteger getMemberImage() {
        return memberImage;
    }

    public void setMemberImage(BigInteger memberImage) {
        this.memberImage = memberImage;
    }

    public BigInteger getPrintAccountNo() {
        return printAccountNo;
    }

    public void setprintAccountNo(BigInteger printAccountNo) {
        this.printAccountNo = printAccountNo;
    }

    public BigInteger getLockAccount() {
        return lockAccount;
    }

    public void setLockAccount(BigInteger lockAccount) {
        this.lockAccount = lockAccount;
    }

    public BigInteger getLockCount() {
        return lockCount;
    }

    public void setLockCount(BigInteger lockCount) {
        this.lockCount = lockCount;
    }
    public BigInteger getAuthenticateAgentTransaction() {
        return authenticateAgentTransaction;
    }

    public void setAuthenticateAgentTransaction(BigInteger authenticateAgentTransaction) {
        this.authenticateAgentTransaction = authenticateAgentTransaction;
    }

    public BigInteger getDisplayNm_Trx() {
        return displayNm_Trx;
    }

    public void setDisplayNm_Trx(BigInteger displayNm_Trx) {
        this.displayNm_Trx = displayNm_Trx;
    }

    public BigInteger getNewPrintDesign() {
        return newPrintDesign;
    }

    public void setNewPrintDesign(BigInteger newPrintDesign) {
        this.newPrintDesign = newPrintDesign;
    }

    public BigInteger getAllow_account_name() {
        return allow_account_name;
    }

    public void setAllow_account_name(BigInteger allow_account_name) {
        this.allow_account_name = allow_account_name;
    }

    public BigInteger getForcedUpdate() {
        return forcedUpdate;
    }

    public void setForcedUpdate(BigInteger forcedUpdate) {
        this.forcedUpdate = forcedUpdate;
    }

    public String getVersionOld() {
        return versionOld;
    }

    public void setVersionOld(String versionOld) {
        this.versionOld = versionOld;
    }

    public String getVersionNew() {
        return versionNew;
    }

    public void setVersionNew(String versionNew) {
        this.versionNew = versionNew;
    }

    public BigInteger getPrintAccountname() { return printAccountname; }

    public void setPrintAccountname(BigInteger printAccountname) { this.printAccountname = printAccountname; }

    public BigInteger getEnable_withdrawals_sms() { return enable_withdrawals_sms; }

    public void setEnable_withdrawals_sms(BigInteger enable_withdrawals_sms) { this.enable_withdrawals_sms = enable_withdrawals_sms; }

    public BigInteger getMemRegReqIdImage() {
        return memRegReqIdImage;
    }

    public void setMemRegReqIdImage(BigInteger memRegReqIdImage) {
        this.memRegReqIdImage = memRegReqIdImage;
    }

    public BigInteger getMemRegReqPassportImage() {
        return memRegReqPassportImage;
    }

    public void setMemRegReqPassportImage(BigInteger memRegReqPassportImage) {
        this.memRegReqPassportImage = memRegReqPassportImage;
    }

    public BigInteger getMemRegReqSignatureImage() {
        return memRegReqSignatureImage;
    }

    public void setMemRegReqSignatureImage(BigInteger memRegReqSignatureImage) {
        this.memRegReqSignatureImage = memRegReqSignatureImage;
    }

    public BigInteger getMemRegReqRegFormImage() {
        return memRegReqRegFormImage;
    }

    public void setMemRegReqRegFormImage(BigInteger memRegReqRegFormImage) {
        this.memRegReqRegFormImage = memRegReqRegFormImage;
    }

    public String getReceiptConfig() {
        return receiptConfig;
    }

    public void setReceiptConfig(String receiptConfig) {
        this.receiptConfig = receiptConfig;
    }

    public BigInteger getMemRegReqNewBioData() {
        return memRegReqNewBioData;
    }

    public void setMemRegReqNewBioData(BigInteger memRegReqNewBioData) {
        this.memRegReqNewBioData = memRegReqNewBioData;
    }

    public String getAgencyServices() {
        return agencyServices;
    }

    public void setAgencyServices(String agencyServices) {
        this.agencyServices = agencyServices;
    }

    public BigInteger getStoreBlacklistCount() {return storeBlacklistCount;}

    public void setStoreBlacklistCount(BigInteger storeBlacklistCount) {this.storeBlacklistCount = storeBlacklistCount;}

    public Double getStoreBlacklistTime() {return storeBlacklistTime;}

    public void setStoreBlacklistTime(Double storeBlacklistTime) {this.storeBlacklistTime = storeBlacklistTime;}

    public int getOtpRetries() {
        return otpRetries;
    }

    public void setOtpRetries(int otpRetries) {
        this.otpRetries = otpRetries;
    }

    public BigInteger getPrintAgentReceipt() {
        return printAgentReceipt;
    }

    public void setPrintAgentReceipt(BigInteger printAgentReceipt) {
        this.printAgentReceipt = printAgentReceipt;
    }

    public String getAgentReceiptConfig() {
        return agentReceiptConfig;
    }

    public void setAgentReceiptConfig(String agentReceiptConfig) {
        this.agentReceiptConfig = agentReceiptConfig;
    }

    public String getSenderId() {return senderId;}

    public void setSenderId(String senderId) {this.senderId = senderId;}

    public int getDeactivationCount() {return deactivationCount;}

    public void setDeactivationCount(int deactivationCount) {this.deactivationCount = deactivationCount;}

    public Integer getCustomerPinRetries() { return customerPinRetries; }

    public void setCustomerPinRetries(Integer customerPinRetries) { this.customerPinRetries = customerPinRetries; }
    public BigInteger getAccountOverdraft() { return accountOverdraft; }
    public void setAccountOverdraft(BigInteger accountOverdraft) { this.accountOverdraft = accountOverdraft; }
    public BigInteger getAgentTransactionSms() {
        return agentTransactionSms;
    }
    public void setAgentTransactionSms(BigInteger agentTransactionSms) {
        this.agentTransactionSms = agentTransactionSms;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (agentId != null ? agentId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAgents)) {
            return false;
        }
        SpAgents other = (SpAgents) object;
        if ((this.agentId == null && other.agentId != null) || (this.agentId != null && !this.agentId.equals(other.agentId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "spotcash.agencybanking.entity.SpAgents[ agentId=" + agentId + " ]";
    }

    public BigInteger getOtpLogin() { return otpLogin; }

    public void setOtpLogin(BigInteger otpLogin) { this.otpLogin = otpLogin; }
}
