package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import java.math.BigInteger;

@Entity
@Table(name = "SP_MSG_TEMPLATES")
public class SpMsgTemplates {
    private BigInteger id;
    private BigInteger clientId;
    private String templateName;
    private String templateMessage;
    private BigInteger status;

    @Id
    @Basic
    @Column(name = "ID")
    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    @Basic
    @Column(name = "CLIENT_ID")
    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    @Basic
    @Column(name = "TEMPLATE_NAME")
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    @Basic
    @Column(name = "TEMPLATE_MESSAGE")
    public String getTemplateMessage() {
        return templateMessage;
    }

    public void setTemplateMessage(String templateMessage) {
        this.templateMessage = templateMessage;
    }

    @Basic
    @Column(name = "STATUS")
    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

}
