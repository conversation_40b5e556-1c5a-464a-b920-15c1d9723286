/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_MPESA_RECORDS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpMpesaRecords.findAll", query = "SELECT s FROM SpMpesaRecords s"),
    @NamedQuery(name = "SpMpesaRecords.findById", query = "SELECT s FROM SpMpesaRecords s WHERE s.id = :id"),
    @NamedQuery(name = "SpMpesaRecords.findByReceipt", query = "SELECT s FROM SpMpesaRecords s WHERE s.receipt = :receipt"),
    @NamedQuery(name = "SpMpesaRecords.findByDateTime", query = "SELECT s FROM SpMpesaRecords s WHERE s.dateTime = :dateTime"),
    @NamedQuery(name = "SpMpesaRecords.findByDetails", query = "SELECT s FROM SpMpesaRecords s WHERE s.details = :details"),
    @NamedQuery(name = "SpMpesaRecords.findByStatus", query = "SELECT s FROM SpMpesaRecords s WHERE s.status = :status"),
    @NamedQuery(name = "SpMpesaRecords.findByWithdrawn", query = "SELECT s FROM SpMpesaRecords s WHERE s.withdrawn = :withdrawn"),
    @NamedQuery(name = "SpMpesaRecords.findByPaidId", query = "SELECT s FROM SpMpesaRecords s WHERE s.paidId = :paidId"),
    @NamedQuery(name = "SpMpesaRecords.findByBalance", query = "SELECT s FROM SpMpesaRecords s WHERE s.balance = :balance"),
    @NamedQuery(name = "SpMpesaRecords.findByTransactionType", query = "SELECT s FROM SpMpesaRecords s WHERE s.transactionType = :transactionType"),
    @NamedQuery(name = "SpMpesaRecords.findByPhoneNo", query = "SELECT s FROM SpMpesaRecords s WHERE s.phoneNo = :phoneNo"),
    @NamedQuery(name = "SpMpesaRecords.findByOtherInfo", query = "SELECT s FROM SpMpesaRecords s WHERE s.otherInfo = :otherInfo"),
    @NamedQuery(name = "SpMpesaRecords.findByBalanceConfirmed", query = "SELECT s FROM SpMpesaRecords s WHERE s.balanceConfirmed = :balanceConfirmed"),
    @NamedQuery(name = "SpMpesaRecords.findByTransactionPartyDetails", query = "SELECT s FROM SpMpesaRecords s WHERE s.transactionPartyDetails = :transactionPartyDetails"),
    @NamedQuery(name = "SpMpesaRecords.findByBusinessNumber", query = "SELECT s FROM SpMpesaRecords s WHERE s.businessNumber = :businessNumber"),
    @NamedQuery(name = "SpMpesaRecords.findByClientId", query = "SELECT s FROM SpMpesaRecords s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpMpesaRecords.findByOriginalPhoneNo", query = "SELECT s FROM SpMpesaRecords s WHERE s.originalPhoneNo = :originalPhoneNo"),
    @NamedQuery(name = "SpMpesaRecords.findByEditReason", query = "SELECT s FROM SpMpesaRecords s WHERE s.editReason = :editReason"),
    @NamedQuery(name = "SpMpesaRecords.findByComments", query = "SELECT s FROM SpMpesaRecords s WHERE s.comments = :comments"),
    @NamedQuery(name = "SpMpesaRecords.findByEdittedBy", query = "SELECT s FROM SpMpesaRecords s WHERE s.edittedBy = :edittedBy"),
    @NamedQuery(name = "SpMpesaRecords.findByTransactionId", query = "SELECT s FROM SpMpesaRecords s WHERE s.transactionId = :transactionId")})
public class SpMpesaRecords implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "RECEIPT")
    private String receipt;
    @Column(name = "DATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTime;
    @Size(max = 255)
    @Column(name = "DETAILS")
    private String details;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Size(max = 255)
    @Column(name = "WITHDRAWN")
    private String withdrawn;
    @Size(max = 255)
    @Column(name = "PAID_ID")
    private String paidId;
    @Size(max = 255)
    @Column(name = "BALANCE")
    private String balance;
    @Size(max = 255)
    @Column(name = "TRANSACTION_TYPE")
    private String transactionType;
    @Size(max = 255)
    @Column(name = "PHONE_NO")
    private String phoneNo;
    @Size(max = 255)
    @Column(name = "OTHER_INFO")
    private String otherInfo;
    @Size(max = 255)
    @Column(name = "BALANCE_CONFIRMED")
    private String balanceConfirmed;
    @Size(max = 255)
    @Column(name = "TRANSACTION_PARTY_DETAILS")
    private String transactionPartyDetails;
    @Size(max = 255)
    @Column(name = "BUSINESS_NUMBER")
    private String businessNumber;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "ORIGINAL_PHONE_NO")
    private String originalPhoneNo;
    @Size(max = 255)
    @Column(name = "EDIT_REASON")
    private String editReason;
    @Size(max = 255)
    @Column(name = "COMMENTS")
    private String comments;
    @Column(name = "EDITTED_BY")
    private BigInteger edittedBy;
    @Size(max = 255)
    @Column(name = "TRANSACTION_ID")
    private String transactionId;

    public SpMpesaRecords() {
    }

    public SpMpesaRecords(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWithdrawn() {
        return withdrawn;
    }

    public void setWithdrawn(String withdrawn) {
        this.withdrawn = withdrawn;
    }

    public String getPaidId() {
        return paidId;
    }

    public void setPaidId(String paidId) {
        this.paidId = paidId;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(String otherInfo) {
        this.otherInfo = otherInfo;
    }

    public String getBalanceConfirmed() {
        return balanceConfirmed;
    }

    public void setBalanceConfirmed(String balanceConfirmed) {
        this.balanceConfirmed = balanceConfirmed;
    }

    public String getTransactionPartyDetails() {
        return transactionPartyDetails;
    }

    public void setTransactionPartyDetails(String transactionPartyDetails) {
        this.transactionPartyDetails = transactionPartyDetails;
    }

    public String getBusinessNumber() {
        return businessNumber;
    }

    public void setBusinessNumber(String businessNumber) {
        this.businessNumber = businessNumber;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getOriginalPhoneNo() {
        return originalPhoneNo;
    }

    public void setOriginalPhoneNo(String originalPhoneNo) {
        this.originalPhoneNo = originalPhoneNo;
    }

    public String getEditReason() {
        return editReason;
    }

    public void setEditReason(String editReason) {
        this.editReason = editReason;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigInteger getEdittedBy() {
        return edittedBy;
    }

    public void setEdittedBy(BigInteger edittedBy) {
        this.edittedBy = edittedBy;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpMpesaRecords)) {
            return false;
        }
        SpMpesaRecords other = (SpMpesaRecords) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpMpesaRecords[ id=" + id + " ]";
    }
    
}
