/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_RECON")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpRecon.findAll", query = "SELECT s FROM SpRecon s"),
    @NamedQuery(name = "SpRecon.findById", query = "SELECT s FROM SpRecon s WHERE s.id = :id"),
    @NamedQuery(name = "SpRecon.findByReceipt", query = "SELECT s FROM SpRecon s WHERE s.receipt = :receipt"),
    @NamedQuery(name = "SpRecon.findByName", query = "SELECT s FROM SpRecon s WHERE s.name = :name"),
    @NamedQuery(name = "SpRecon.findByMsisdn", query = "SELECT s FROM SpRecon s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpRecon.findByAmount", query = "SELECT s FROM SpRecon s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpRecon.findByDtNew", query = "SELECT s FROM SpRecon s WHERE s.dtNew = :dtNew"),
    @NamedQuery(name = "SpRecon.findByTrxId", query = "SELECT s FROM SpRecon s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpRecon.findByClientId", query = "SELECT s FROM SpRecon s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpRecon.findByDt", query = "SELECT s FROM SpRecon s WHERE s.dt = :dt")})
public class SpRecon implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "RECEIPT")
    private String receipt;
    @Size(max = 255)
    @Column(name = "NAME")
    private String name;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "DT_NEW")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dtNew;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "DT")
    private String dt;

    public SpRecon() {
    }

    public SpRecon(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getReceipt() {
        return receipt;
    }

    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getDtNew() {
        return dtNew;
    }

    public void setDtNew(Date dtNew) {
        this.dtNew = dtNew;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpRecon)) {
            return false;
        }
        SpRecon other = (SpRecon) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpRecon[ id=" + id + " ]";
    }
    
}
