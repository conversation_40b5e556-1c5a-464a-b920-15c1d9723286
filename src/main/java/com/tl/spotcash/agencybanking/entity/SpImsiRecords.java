package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigInteger;
import java.util.Date;
import java.time.LocalDate;

@Entity
@Table(name = "SP_IMSI_RECORDS")
public class SpImsiRecords {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "SP_IMSI_RECORDS_SEQ")
    @SequenceGenerator(name = "SP_IMSI_RECORDS_SEQ", sequenceName = "SP_IMSI_RECORDS_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;

    @Size(max = 20)
    @Column(name = "MSISDN", unique=true)
    private String msisdn;

    @Column(name = "CLIENT_ID")
    private BigInteger clientId;

    @Column(name = "LAST_SWAP_TIME")
    private LocalDate lastSwapTime;
    @Column(name = "LAST_IMSI_CHECK")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastImsiCheck;

    public SpImsiRecords(){

    }

    public SpImsiRecords(BigInteger id, String msisdn, BigInteger clientId, LocalDate lastSwapTime, Date lastImsiCheck) {
        this.id = id;
        this.msisdn = msisdn;
        this.clientId = clientId;
        this.lastSwapTime = lastSwapTime;
        this.lastImsiCheck = lastImsiCheck;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public LocalDate getLastSwapTime() {
        return lastSwapTime;
    }

    public void setLastSwapTime(LocalDate lastSwapTime) {
        this.lastSwapTime = lastSwapTime;
    }

    public Date getLastImsiCheck() {
        return lastImsiCheck;
    }

    public void setLastImsiCheck(Date lastImsiCheck) {
        this.lastImsiCheck = lastImsiCheck;
    }

    @Override
    public String toString() {
        return "SpImsiRecords{" +
                "id=" + id +
                ", msisdn='" + msisdn + '\'' +
                ", clientId=" + clientId +
                ", lastSwapTime=" + lastSwapTime +
                ", lastImsiCheck=" + lastImsiCheck +
                '}';
    }
}
