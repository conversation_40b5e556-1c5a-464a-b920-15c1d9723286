package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_VPNCLIENTS_CONFIGS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpVpnclientsConfigs.findAll", query = "SELECT s FROM SpVpnclientsConfigs s"),
    @NamedQuery(name = "SpVpnclientsConfigs.findById", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.id = :id"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByClientId", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByCbsEndpointType", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.cbsEndpointType = :cbsEndpointType"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByHostUrl", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.hostUrl = :hostUrl"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByHostPort", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.hostPort = :hostPort"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByInTrash", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.inTrash = :inTrash"),
    @NamedQuery(name = "SpVpnclientsConfigs.findByResponses", query = "SELECT s FROM SpVpnclientsConfigs s WHERE s.responses = :responses")})
public class SpVpnclientsConfigs implements Serializable {

    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 20)
    @Column(name = "HAS_LENGTH")
    private String hasLength;

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigDecimal id;
    @Basic(optional = false)
    @NotNull
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 20)
    @Column(name = "CBS_ENDPOINT_TYPE")
    private String cbsEndpointType;
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 20)
    @Column(name = "HOST_URL")
    private String hostUrl;
    @Size(max = 20)
    @Column(name = "HOST_PORT")
    private String hostPort;
    @Basic(optional = false)
    @NotNull
    @Lob
    @Column(name = "API_PARAMS")
    private String apiParams;
    @Size(max = 1000)
    @Column(name = "RESPONSES")
    private String responses;
    @Size(max = 4000)
    @Column(name = "ISO_PARSE_PARAMS")
    private String isoParseParams;
    @Lob
    @Column(name = "RESPONSE_MAPPINGS")
    private String responseMappings;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String inTrash;
    @Column(name = "USE_MOBILE_BANKING")
    private Integer useMobileBanking;
    @Column(name = "USE_SPOTPAY")
    private Integer useSpotpay;
    @Column(name = "SPOTPAY_ORG_ID")
    private String spotpayOrgId;

    @Basic(optional = false)
    @NotNull
    @Column(name = "BRIDGE_CONFIGS")
    private BigInteger bridgeConfigs;

    public SpVpnclientsConfigs() {
    }

    public SpVpnclientsConfigs(BigDecimal id) {
        this.id = id;
    }

    public SpVpnclientsConfigs(BigDecimal id, BigInteger clientId, String cbsEndpointType, String hostUrl, String apiParams) {
        this.id = id;
        this.clientId = clientId;
        this.cbsEndpointType = cbsEndpointType;
        this.hostUrl = hostUrl;
        this.apiParams = apiParams;
    }

    public BigInteger getBridgeConfigs() {
        return bridgeConfigs;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getCbsEndpointType() {
        return cbsEndpointType;
    }

    public void setCbsEndpointType(String cbsEndpointType) {
        this.cbsEndpointType = cbsEndpointType;
    }

    public String getHostUrl() {
        return hostUrl;
    }

    public void setHostUrl(String hostUrl) {
        this.hostUrl = hostUrl;
    }

    public String getHostPort() {
        return hostPort;
    }

    public void setHostPort(String hostPort) {
        this.hostPort = hostPort;
    }

    public String getApiParams() {
        return apiParams;
    }

    public void setApiParams(String apiParams) {
        this.apiParams = apiParams;
    }

    public String getResponses() {
        return responses;
    }

    public void setResponses(String responses) {
        this.responses = responses;
    }

    public String getResponseMappings() {
        return responseMappings;
    }

    public void setResponseMappings(String responseMappings) {
        this.responseMappings = responseMappings;
    }

    public String getIsoParseParams() {
        return isoParseParams;
    }

    public void setIsoParseParams(String isoParseParams) {
        this.isoParseParams = isoParseParams;
    }

    public String getInTrash() {
        return inTrash;
    }

    public void setIntrash(String inTrash) {
        this.inTrash = inTrash;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpVpnclientsConfigs)) {
            return false;
        }
        SpVpnclientsConfigs other = (SpVpnclientsConfigs) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "SpVpnclientsConfigs{" +
                "hasLength='" + hasLength + '\'' +
                ", id=" + id +
                ", clientId=" + clientId +
                ", cbsEndpointType='" + cbsEndpointType + '\'' +
                ", hostUrl='" + hostUrl + '\'' +
                ", hostPort='" + hostPort + '\'' +
                ", apiParams='" + apiParams + '\'' +
                ", responses='" + responses + '\'' +
                ", isoParseParams='" + isoParseParams + '\'' +
                ", responseMappings='" + responseMappings + '\'' +
                '}';
    }

    public String getHasLength() {
        return hasLength;
    }

    public void setHasLength(String hasLength) {
        this.hasLength = hasLength;
    }

    public Integer getUseMobileBanking() {
        return useMobileBanking;
    }

    public void setUseMobileBanking(Integer useMobileBanking) {
        this.useMobileBanking = useMobileBanking;
    }

    public Integer getUseSpotpay() { return useSpotpay; }

    public void setUseSpotpay(Integer useSpotpay) { this.useSpotpay = useSpotpay; }

    public String getSpotpayOrgId() { return spotpayOrgId; }

    public void setSpotpayOrgId(String spotpayOrgId) { this.spotpayOrgId = spotpayOrgId; }
}
