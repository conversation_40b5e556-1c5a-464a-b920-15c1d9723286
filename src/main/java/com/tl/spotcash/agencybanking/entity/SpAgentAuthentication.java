/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "SP_AGENT_AUTHENTICATION")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAgentAuthentication.findAll", query = "SELECT s FROM SpAgentAuthentication s"),
    @NamedQuery(name = "SpAgentAuthentication.findByAgentId", query = "SELECT s FROM SpAgentAuthentication s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpAgentAuthentication.findByAuthType", query = "SELECT s FROM SpAgentAuthentication s WHERE s.authType = :authType")})
public class SpAgentAuthentication implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Size(max = 255)
    @Column(name = "AUTH_TYPE")
    private String authType;

    public SpAgentAuthentication() {
    }

    public SpAgentAuthentication(BigInteger id) {
        this.id = id;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAgentAuthentication)) {
            return false;
        }
        SpAgentAuthentication other = (SpAgentAuthentication) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "spotcash.agencybanking.entity.SpAgentAuthentication[ id=" + id + " ]";
    }
    
}
