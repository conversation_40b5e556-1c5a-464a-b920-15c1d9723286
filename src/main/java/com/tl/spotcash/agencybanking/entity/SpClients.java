/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_CLIENTS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpClients.findAll", query = "SELECT s FROM SpClients s"),
    @NamedQuery(name = "SpClients.findById", query = "SELECT s FROM SpClients s WHERE s.id = :id"),
    @NamedQuery(name = "SpClients.findByClientId", query = "SELECT s FROM SpClients s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpClients.findByClientName", query = "SELECT s FROM SpClients s WHERE s.clientName = :clientName"),
    @NamedQuery(name = "SpClients.findByShortCode", query = "SELECT s FROM SpClients s WHERE s.shortCode = :shortCode"),
    @NamedQuery(name = "SpClients.findByBulksmsId", query = "SELECT s FROM SpClients s WHERE s.bulksmsId = :bulksmsId"),
    @NamedQuery(name = "SpClients.findByPasskey", query = "SELECT s FROM SpClients s WHERE s.passkey = :passkey"),
    @NamedQuery(name = "SpClients.findByMsisdn", query = "SELECT s FROM SpClients s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpClients.findByEmail", query = "SELECT s FROM SpClients s WHERE s.email = :email"),
    @NamedQuery(name = "SpClients.findByBridgeStatus", query = "SELECT s FROM SpClients s WHERE s.bridgeStatus = :bridgeStatus"),
    @NamedQuery(name = "SpClients.findByActiveStatus", query = "SELECT s FROM SpClients s WHERE s.activeStatus = :activeStatus"),
    @NamedQuery(name = "SpClients.findByIntrash", query = "SELECT s FROM SpClients s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpClients.findByDateCreated", query = "SELECT s FROM SpClients s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpClients.findBySendAlert", query = "SELECT s FROM SpClients s WHERE s.sendAlert = :sendAlert"),
    @NamedQuery(name = "SpClients.findByBusinessCatId", query = "SELECT s FROM SpClients s WHERE s.businessCatId = :businessCatId"),
    @NamedQuery(name = "SpClients.findByBusinessRegionId", query = "SELECT s FROM SpClients s WHERE s.businessRegionId = :businessRegionId"),
    @NamedQuery(name = "SpClients.findByWebsite", query = "SELECT s FROM SpClients s WHERE s.website = :website"),
    @NamedQuery(name = "SpClients.findByContactPerson", query = "SELECT s FROM SpClients s WHERE s.contactPerson = :contactPerson"),
    @NamedQuery(name = "SpClients.findByLowerLimit", query = "SELECT s FROM SpClients s WHERE s.lowerLimit = :lowerLimit"),
    @NamedQuery(name = "SpClients.findByUpperLimit", query = "SELECT s FROM SpClients s WHERE s.upperLimit = :upperLimit"),
    @NamedQuery(name = "SpClients.findBySmsAlert", query = "SELECT s FROM SpClients s WHERE s.smsAlert = :smsAlert"),
    @NamedQuery(name = "SpClients.findByEmailAlert", query = "SELECT s FROM SpClients s WHERE s.emailAlert = :emailAlert"),
    @NamedQuery(name = "SpClients.findBySmsAlertNo", query = "SELECT s FROM SpClients s WHERE s.smsAlertNo = :smsAlertNo"),
    @NamedQuery(name = "SpClients.findByEmailAlertAddr", query = "SELECT s FROM SpClients s WHERE s.emailAlertAddr = :emailAlertAddr"),
    @NamedQuery(name = "SpClients.findBySendingNumber", query = "SELECT s FROM SpClients s WHERE s.sendingNumber = :sendingNumber"),
    @NamedQuery(name = "SpClients.findByExpireAfter", query = "SELECT s FROM SpClients s WHERE s.expireAfter = :expireAfter"),
    @NamedQuery(name = "SpClients.findByIpAddress", query = "SELECT s FROM SpClients s WHERE s.ipAddress = :ipAddress"),
    @NamedQuery(name = "SpClients.findByUseIp", query = "SELECT s FROM SpClients s WHERE s.useIp = :useIp"),
    @NamedQuery(name = "SpClients.findByPostalAddr", query = "SELECT s FROM SpClients s WHERE s.postalAddr = :postalAddr"),
    @NamedQuery(name = "SpClients.findByBankName", query = "SELECT s FROM SpClients s WHERE s.bankName = :bankName"),
    @NamedQuery(name = "SpClients.findByAccountNumber", query = "SELECT s FROM SpClients s WHERE s.accountNumber = :accountNumber"),
    @NamedQuery(name = "SpClients.findByBankBranch", query = "SELECT s FROM SpClients s WHERE s.bankBranch = :bankBranch"),
    @NamedQuery(name = "SpClients.findByPaybill", query = "SELECT s FROM SpClients s WHERE s.paybill = :paybill"),
    @NamedQuery(name = "SpClients.findByBulksmsPasskey", query = "SELECT s FROM SpClients s WHERE s.bulksmsPasskey = :bulksmsPasskey"),
    @NamedQuery(name = "SpClients.findByAccountName", query = "SELECT s FROM SpClients s WHERE s.accountName = :accountName"),
    @NamedQuery(name = "SpClients.findByClientType", query = "SELECT s FROM SpClients s WHERE s.clientType = :clientType")})
public class SpClients implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "CLIENT_NAME")
    private String clientName;
    @Size(max = 10)
    @Column(name = "SHORT_CODE")
    private String shortCode;
    @Size(max = 10)
    @Column(name = "BULKSMS_ID")
    private String bulksmsId;
    @Size(max = 50)
    @Column(name = "PASSKEY")
    private String passkey;
    @Size(max = 25)
    @Column(name = "MSISDN")
    private String msisdn;
    // @Pattern(regexp="[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?", message="Invalid email")//if the field contains email address consider using this annotation to enforce field validation
    @Size(max = 100)
    @Column(name = "EMAIL")
    private String email;
    @Size(max = 30)
    @Column(name = "BRIDGE_STATUS")
    private String bridgeStatus;
    @Column(name = "ACTIVE_STATUS")
    private BigInteger activeStatus;
    @Size(max = 5)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "SEND_ALERT")
    private BigInteger sendAlert;
    @Column(name = "BUSINESS_CAT_ID")
    private BigInteger businessCatId;
    @Column(name = "BUSINESS_REGION_ID")
    private BigInteger businessRegionId;
    @Size(max = 255)
    @Column(name = "WEBSITE")
    private String website;
    @Size(max = 100)
    @Column(name = "CONTACT_PERSON")
    private String contactPerson;
    @Column(name = "LOWER_LIMIT")
    private BigInteger lowerLimit;
    @Column(name = "UPPER_LIMIT")
    private BigInteger upperLimit;
    @Column(name = "SMS_ALERT",nullable = true, columnDefinition = "char default 0")
    private Character smsAlert;
    @Column(nullable = true, name = "EMAIL_ALERT", columnDefinition = "char default 0")
    private Character emailAlert;
    @Size(max = 25)
    @Column(name = "SMS_ALERT_NO",nullable = true)
    private String smsAlertNo;
    @Size(max = 100)
    @Column(name = "EMAIL_ALERT_ADDR")
    private String emailAlertAddr;
    @Size(max = 30)
    @Column(name = "SENDING_NUMBER")
    private String sendingNumber;
    @Column(name = "EXPIRE_AFTER")
    private BigInteger expireAfter;
    @Size(max = 100)
    @Column(name = "IP_ADDRESS")
    private String ipAddress;
    @Column(name = "USE_IP",nullable = true, columnDefinition = "char default 0")
    private Character useIp;
    @Size(max = 50)
    @Column(name = "POSTAL_ADDR")
    private String postalAddr;
    @Size(max = 100)
    @Column(name = "BANK_NAME")
    private String bankName;
    @Size(max = 100)
    @Column(name = "ACCOUNT_NUMBER")
    private String accountNumber;
    @Size(max = 100)
    @Column(name = "BANK_BRANCH")
    private String bankBranch;
    @Size(max = 50)
    @Column(name = "PAYBILL")
    private String paybill;
    @Size(max = 50)
    @Column(name = "BULKSMS_PASSKEY")
    private String bulksmsPasskey;
    @Size(max = 255)
    @Column(name = "ACCOUNT_NAME")
    private String accountName;
    @Size(max = 255)
    @Column(name = "CLIENT_TYPE")
    private String clientType;
    @Column(name = "LAST_ALERT_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastAlertTime;
    @Column(name = "USE_CLIENT_PAYBILL")
    private Short useClientPaybill;
    @Column(name="USE_XML_TEMPLATE")
    private BigInteger useXmlTemplate;

    public SpClients() {
    }

    public SpClients(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public String getBulksmsId() {
        return bulksmsId;
    }

    public void setBulksmsId(String bulksmsId) {
        this.bulksmsId = bulksmsId;
    }

    public String getPasskey() {
        return passkey;
    }

    public void setPasskey(String passkey) {
        this.passkey = passkey;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBridgeStatus() {
        return bridgeStatus;
    }

    public void setBridgeStatus(String bridgeStatus) {
        this.bridgeStatus = bridgeStatus;
    }

    public BigInteger getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(BigInteger activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigInteger getSendAlert() {
        return sendAlert;
    }

    public void setSendAlert(BigInteger sendAlert) {
        this.sendAlert = sendAlert;
    }

    public BigInteger getBusinessCatId() {
        return businessCatId;
    }

    public void setBusinessCatId(BigInteger businessCatId) {
        this.businessCatId = businessCatId;
    }

    public BigInteger getBusinessRegionId() {
        return businessRegionId;
    }

    public void setBusinessRegionId(BigInteger businessRegionId) {
        this.businessRegionId = businessRegionId;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public BigInteger getLowerLimit() {
        return lowerLimit;
    }

    public void setLowerLimit(BigInteger lowerLimit) {
        this.lowerLimit = lowerLimit;
    }

    public BigInteger getUpperLimit() {
        return upperLimit;
    }

    public void setUpperLimit(BigInteger upperLimit) {
        this.upperLimit = upperLimit;
    }

    public Character getSmsAlert() {
        return smsAlert;
    }

    public void setSmsAlert(Character smsAlert) {
        this.smsAlert = smsAlert;
    }

    public Character getEmailAlert() {
        return emailAlert;
    }

    public void setEmailAlert(Character emailAlert) {
        this.emailAlert = emailAlert;
    }

    public String getSmsAlertNo() {
        return smsAlertNo;
    }

    public void setSmsAlertNo(String smsAlertNo) {
        this.smsAlertNo = smsAlertNo;
    }

    public String getEmailAlertAddr() {
        return emailAlertAddr;
    }

    public void setEmailAlertAddr(String emailAlertAddr) {
        this.emailAlertAddr = emailAlertAddr;
    }

    public String getSendingNumber() {
        return sendingNumber;
    }

    public void setSendingNumber(String sendingNumber) {
        this.sendingNumber = sendingNumber;
    }

    public BigInteger getExpireAfter() {
        return expireAfter;
    }

    public void setExpireAfter(BigInteger expireAfter) {
        this.expireAfter = expireAfter;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Character getUseIp() {
        return useIp;
    }

    public void setUseIp(Character useIp) {
        this.useIp = useIp;
    }

    public String getPostalAddr() {
        return postalAddr;
    }

    public void setPostalAddr(String postalAddr) {
        this.postalAddr = postalAddr;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getPaybill() {
        return paybill;
    }

    public void setPaybill(String paybill) {
        this.paybill = paybill;
    }

    public String getBulksmsPasskey() {
        return bulksmsPasskey;
    }

    public void setBulksmsPasskey(String bulksmsPasskey) {
        this.bulksmsPasskey = bulksmsPasskey;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getClientType() {
        return clientType;
    }

    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    public Date getLastAlertTime() {
        return lastAlertTime;
    }

    public void setLastAlertTime(Date lastAlertTime) {
        this.lastAlertTime = lastAlertTime;
    }

    public Short getUseClientPaybill() {
        return useClientPaybill;
    }

    public void setUseClientPaybill(Short useClientPaybill) {
        this.useClientPaybill = useClientPaybill;
    }

    public BigInteger getUseXmlTemplate() {return useXmlTemplate;}

    public void setUseXmlTemplate(BigInteger useXmlTemplate) {this.useXmlTemplate = useXmlTemplate;}

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpClients)) {
            return false;
        }
        SpClients other = (SpClients) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpClients[ id=" + id + " client_id = "+clientId+ "]";
    }

}