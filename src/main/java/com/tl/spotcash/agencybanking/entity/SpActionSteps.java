/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ACTION_STEPS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpActionSteps.findAll", query = "SELECT s FROM SpActionSteps s"),
    @NamedQuery(name = "SpActionSteps.findById", query = "SELECT s FROM SpActionSteps s WHERE s.id = :id"),
    @NamedQuery(name = "SpActionSteps.findByServiceId", query = "SELECT s FROM SpActionSteps s WHERE s.serviceId = :serviceId"),
    @NamedQuery(name = "SpActionSteps.findByField", query = "SELECT s FROM SpActionSteps s WHERE s.field = :field"),
    @NamedQuery(name = "SpActionSteps.findByDrCrIndicator", query = "SELECT s FROM SpActionSteps s WHERE s.drCrIndicator = :drCrIndicator"),
    @NamedQuery(name = "SpActionSteps.findByAccId", query = "SELECT s FROM SpActionSteps s WHERE s.accId = :accId"),
    @NamedQuery(name = "SpActionSteps.findByDateCreated", query = "SELECT s FROM SpActionSteps s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpActionSteps.findByDrParty", query = "SELECT s FROM SpActionSteps s WHERE s.drParty = :drParty"),
    @NamedQuery(name = "SpActionSteps.findByCrParty", query = "SELECT s FROM SpActionSteps s WHERE s.crParty = :crParty")})
public class SpActionSteps implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;
    @Size(max = 255)
    @Column(name = "FIELD")
    private String field;
    @Size(max = 255)
    @Column(name = "DR_CR_INDICATOR")
    private String drCrIndicator;
    @Size(max = 255)
    @Column(name = "ACC_ID")
    private String accId;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "DR_PARTY")
    private String drParty;
    @Size(max = 255)
    @Column(name = "CR_PARTY")
    private String crParty;

    public SpActionSteps() {
    }

    public SpActionSteps(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getDrCrIndicator() {
        return drCrIndicator;
    }

    public void setDrCrIndicator(String drCrIndicator) {
        this.drCrIndicator = drCrIndicator;
    }

    public String getAccId() {
        return accId;
    }

    public void setAccId(String accId) {
        this.accId = accId;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getDrParty() {
        return drParty;
    }

    public void setDrParty(String drParty) {
        this.drParty = drParty;
    }

    public String getCrParty() {
        return crParty;
    }

    public void setCrParty(String crParty) {
        this.crParty = crParty;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpActionSteps)) {
            return false;
        }
        SpActionSteps other = (SpActionSteps) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpActionSteps[ id=" + id + " ]";
    }
    
}
