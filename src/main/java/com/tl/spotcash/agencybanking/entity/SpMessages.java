/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_MESSAGES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpMessages.findAll", query = "SELECT s FROM SpMessages s"),
    @NamedQuery(name = "SpMessages.findById", query = "SELECT s FROM SpMessages s WHERE s.id = :id"),
    @NamedQuery(name = "SpMessages.findByMessageType", query = "SELECT s FROM SpMessages s WHERE s.messageType = :messageType"),
    @NamedQuery(name = "SpMessages.findByTrxId", query = "SELECT s FROM SpMessages s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpMessages.findByClientId", query = "SELECT s FROM SpMessages s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpMessages.findByServiceId", query = "SELECT s FROM SpMessages s WHERE s.serviceId = :serviceId"),
    @NamedQuery(name = "SpMessages.findByMsisdn", query = "SELECT s FROM SpMessages s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpMessages.findByMessage", query = "SELECT s FROM SpMessages s WHERE s.message = :message"),
    @NamedQuery(name = "SpMessages.findByStatus", query = "SELECT s FROM SpMessages s WHERE s.status = :status"),
    @NamedQuery(name = "SpMessages.findByDateTime", query = "SELECT s FROM SpMessages s WHERE s.dateTime = :dateTime"),
    @NamedQuery(name = "SpMessages.findBySmsEmail", query = "SELECT s FROM SpMessages s WHERE s.smsEmail = :smsEmail"),
    @NamedQuery(name = "SpMessages.findByEmail", query = "SELECT s FROM SpMessages s WHERE s.email = :email"),
    @NamedQuery(name = "SpMessages.findBySender", query = "SELECT s FROM SpMessages s WHERE s.sender = :sender"),
    @NamedQuery(name = "SpMessages.findByMailMsg", query = "SELECT s FROM SpMessages s WHERE s.mailMsg = :mailMsg"),
    @NamedQuery(name = "SpMessages.findByAgentId", query = "SELECT s FROM SpMessages s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpMessages.findByAgentStoreId", query = "SELECT s FROM SpMessages s WHERE s.agentStoreId = :agentStoreId")})
public class SpMessages implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "MESSAGE_TYPE")
    private String messageType;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Size(max = 255)
    @Column(name = "MESSAGE")
    private String message;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "DATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTime;
    @Size(max = 255)
    @Column(name = "SMS_EMAIL")
    private String smsEmail;
    // @Pattern(regexp="[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?", message="Invalid email")//if the field contains email address consider using this annotation to enforce field validation
    @Size(max = 255)
    @Column(name = "EMAIL")
    private String email;
    @Size(max = 255)
    @Column(name = "SENDER")
    private String sender;
    @Size(max = 255)
    @Column(name = "MAIL_MSG")
    private String mailMsg;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;

    public SpMessages() {
    }

    public SpMessages(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getSmsEmail() {
        return smsEmail;
    }

    public void setSmsEmail(String smsEmail) {
        this.smsEmail = smsEmail;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getMailMsg() {
        return mailMsg;
    }

    public void setMailMsg(String mailMsg) {
        this.mailMsg = mailMsg;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpMessages)) {
            return false;
        }
        SpMessages other = (SpMessages) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpMessages[ id=" + id + " ]";
    }
    
}
