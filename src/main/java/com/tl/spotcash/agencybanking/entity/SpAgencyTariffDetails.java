/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AGENCY_TARIFF_DETAILS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAgencyTariffDetails.findAll", query = "SELECT s FROM SpAgencyTariffDetails s"),
    @NamedQuery(name = "SpAgencyTariffDetails.findById", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.id = :id"),
    @NamedQuery(name = "SpAgencyTariffDetails.findByAgencyTariffId", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.agencyTariffId = :agencyTariffId"),
    @NamedQuery(name = "SpAgencyTariffDetails.findByAmount", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpAgencyTariffDetails.findBySpotcashComission", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.spotcashComission = :spotcashComission"),
    @NamedQuery(name = "SpAgencyTariffDetails.findByServiceCharge", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.serviceCharge = :serviceCharge"),
    @NamedQuery(name = "SpAgencyTariffDetails.findByDateCreated", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpAgencyTariffDetails.findByIntrash", query = "SELECT s FROM SpAgencyTariffDetails s WHERE s.intrash = :intrash")})
public class SpAgencyTariffDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "AGENCY_TARIFF_ID")
    private BigInteger agencyTariffId;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "SPOTCASH_COMISSION")
    private BigDecimal spotcashComission;
    @Column(name = "SERVICE_CHARGE")
    private BigDecimal serviceCharge;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 20)
    @Column(name = "INTRASH")
    private String intrash;

    public SpAgencyTariffDetails() {
    }

    public SpAgencyTariffDetails(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getAgencyTariffId() {
        return agencyTariffId;
    }

    public void setAgencyTariffId(BigInteger agencyTariffId) {
        this.agencyTariffId = agencyTariffId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSpotcashComission() {
        return spotcashComission;
    }

    public void setSpotcashComission(BigDecimal spotcashComission) {
        this.spotcashComission = spotcashComission;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAgencyTariffDetails)) {
            return false;
        }
        SpAgencyTariffDetails other = (SpAgencyTariffDetails) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAgencyTariffDetails[ id=" + id + " ]";
    }
    
}
