package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_AGENCY_OTP_LOG")
public class SpAgencyOtpLog {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SP_AGENCY_OTP_LOG_SEQ")
    @SequenceGenerator(name = "SP_AGENCY_OTP_LOG_SEQ", sequenceName = "SP_AGENCY_OTP_LOG_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;
    @Basic
    @Column(name = "MSISDN")
    private String msisdn;
    @Basic
    @Column(name = "NATIONAL_ID")
    private String nationalId;
    @Basic
    @Column(name = "OTP")
    private String otp;
    @Basic
    @Column(name = "TIME_CREATED")
    private Timestamp timeCreated;
    @Basic
    @Column(name = "RETRY_COUNT")
    private Integer retryCount;
    @Basic
    @Column(name = "CLIENT_ID")
    private Long clientId;
    @Basic
    @Column(name = "STATUS")
    private BigInteger status;

    @Basic
    @Column(name = "STORE_USER_ID")
    private Long storeUserId;

    @Basic
    @Column(name = "TIME_UPDATED")
    private Timestamp timeUpdated;

    @Basic
    @Column(name = "STORE_USER_RETRY_COUNT")
    private Integer storeUserRetryCount;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public Timestamp getTimeCreated() {
        return timeCreated;
    }

    public void setTimeCreated(Timestamp timeCreated) {
        this.timeCreated = timeCreated;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getNationalId() {
        return nationalId;
    }

    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public Long getStoreUserId() {return storeUserId;}

    public void setStoreUserId(Long storeUserId) {this.storeUserId = storeUserId;}

    public Timestamp getTimeUpdated() {
        return timeUpdated;
    }

    public void setTimeUpdated(Timestamp timeUpdated) {
        this.timeUpdated = timeUpdated;
    }

    public Integer getStoreUserRetryCount() {return storeUserRetryCount;}

    public void setStoreUserRetryCount(Integer storeUserRetryCount) {this.storeUserRetryCount = storeUserRetryCount;}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SpAgencyOtpLog that = (SpAgencyOtpLog) o;
        return Objects.equals(id, that.id) && Objects.equals(msisdn, that.msisdn) && Objects.equals(otp, that.otp) && Objects.equals(timeCreated, that.timeCreated) && Objects.equals(retryCount, that.retryCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, msisdn, otp, timeCreated, retryCount);
    }
}
