/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_PERMISSIONS_MAP")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpPermissionsMap.findAll", query = "SELECT s FROM SpPermissionsMap s"),
    @NamedQuery(name = "SpPermissionsMap.findById", query = "SELECT s FROM SpPermissionsMap s WHERE s.id = :id"),
    @NamedQuery(name = "SpPermissionsMap.findByRoleId", query = "SELECT s FROM SpPermissionsMap s WHERE s.roleId = :roleId"),
    @NamedQuery(name = "SpPermissionsMap.findByPermissionId", query = "SELECT s FROM SpPermissionsMap s WHERE s.permissionId = :permissionId"),
    @NamedQuery(name = "SpPermissionsMap.findByCreationtime", query = "SELECT s FROM SpPermissionsMap s WHERE s.creationtime = :creationtime")})
public class SpPermissionsMap implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "ROLE_ID")
    private BigInteger roleId;
    @Column(name = "PERMISSION_ID")
    private BigInteger permissionId;
    @Size(max = 255)
    @Column(name = "CREATIONTIME")
    private String creationtime;

    public SpPermissionsMap() {
    }

    public SpPermissionsMap(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getRoleId() {
        return roleId;
    }

    public void setRoleId(BigInteger roleId) {
        this.roleId = roleId;
    }

    public BigInteger getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(BigInteger permissionId) {
        this.permissionId = permissionId;
    }

    public String getCreationtime() {
        return creationtime;
    }

    public void setCreationtime(String creationtime) {
        this.creationtime = creationtime;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpPermissionsMap)) {
            return false;
        }
        SpPermissionsMap other = (SpPermissionsMap) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpPermissionsMap[ id=" + id + " ]";
    }
    
}
