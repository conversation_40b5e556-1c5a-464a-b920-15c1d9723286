/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_CUSTOMERS")
@XmlRootElement
@NamedQueries({

    @NamedQuery(name = "SpCustomers.findAll", query = "SELECT s FROM SpCustomers s"),
    @NamedQuery(name = "SpCustomers.findById", query = "SELECT s FROM SpCustomers s WHERE s.id = :id"),
    @NamedQuery(name = "SpCustomers.findByClientId", query = "SELECT s FROM SpCustomers s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpCustomers.findByCustomerName", query = "SELECT s FROM SpCustomers s WHERE s.customerName = :customerName"),
    @NamedQuery(name = "SpCustomers.findBySubscriptionStatus", query = "SELECT s FROM SpCustomers s WHERE s.subscriptionStatus = :subscriptionStatus"),
    @NamedQuery(name = "SpCustomers.findByMsisdn", query = "SELECT s FROM SpCustomers s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpCustomers.findByPinNo", query = "SELECT s FROM SpCustomers s WHERE s.pinNo = :pinNo"),
    @NamedQuery(name = "SpCustomers.findByDateCreated", query = "SELECT s FROM SpCustomers s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpCustomers.findByIntrash", query = "SELECT s FROM SpCustomers s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpCustomers.findByPinStatus", query = "SELECT s FROM SpCustomers s WHERE s.pinStatus = :pinStatus"),
    @NamedQuery(name = "SpCustomers.findByRegProcessStatus", query = "SELECT s FROM SpCustomers s WHERE s.regProcessStatus = :regProcessStatus"),
    @NamedQuery(name = "SpCustomers.findBySpId", query = "SELECT s FROM SpCustomers s WHERE s.spId = :spId"),
    @NamedQuery(name = "SpCustomers.findByNationalId", query = "SELECT s FROM SpCustomers s WHERE s.nationalId = :nationalId"),
    @NamedQuery(name = "SpCustomers.findByPotrait", query = "SELECT s FROM SpCustomers s WHERE s.potrait = :potrait"),
    @NamedQuery(name = "SpCustomers.findByAgencyBnkStatus", query = "SELECT s FROM SpCustomers s WHERE s.agencyBnkStatus = :agencyBnkStatus"),
    @NamedQuery(name = "SpCustomers.findBySpCustId", query = "SELECT s FROM SpCustomers s WHERE s.spCustId = :spCustId"),
    @NamedQuery(name = "SpCustomers.findByAccountNumber", query = "SELECT s FROM SpCustomers s WHERE s.accountNumber = :accountNumber")})
public class SpCustomers implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    @Column(name = "SUBSCRIPTION_STATUS")
    private BigInteger subscriptionStatus;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Size(max = 255)
    @Column(name = "PIN_NO")
    private String pinNo;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Size(max = 255)
    @Column(name = "PIN_STATUS")
    private String pinStatus;
    @Column(name = "REG_PROCESS_STATUS")
    private BigInteger regProcessStatus;
    @Column(name = "SP_ID")
    private BigInteger spId;
    @Size(max = 255)
    @Column(name = "NATIONAL_ID")
    private String nationalId;
    @Lob
    @Column(name = "POTRAIT")
    private String potrait;
    @Column(name = "AGENCY_BNK_STATUS")
    private BigInteger agencyBnkStatus;
    @Size(max = 255)
    @Column(name = "SP_CUST_ID")
    private String spCustId;
    @Size(max = 50)
    @Column(name = "ACCOUNT_NUMBER")
    private String accountNumber;
    @Lob
    @Column(name = "FINGERPRINT")
    private byte[] fingerprint;
    @Column(name = "PIN_RETRIES")
    private Integer pinRetries;
    @Column(name = "OTP_RETRIES")
    private Integer otpRetries;
    @Column(name = "NEW_ENCRYPT")
    private BigInteger newEncrypt;
    @Size(max = 255)
    @Column(name = "NEW_PIN_NO")
    private String newPinNo;

    @Column(name = "WRONG_PIN_ENTRIES")
    private Integer wrongPinEntries;

    @Getter
    @Setter
    @Transient
    private boolean mobileWebservice;

    public SpCustomers() {
    }

    public SpCustomers(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public BigInteger getSubscriptionStatus() {
        return subscriptionStatus;
    }

    public void setSubscriptionStatus(BigInteger subscriptionStatus) {
        this.subscriptionStatus = subscriptionStatus;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getPinNo() {
        return pinNo;
    }

    public void setPinNo(String pinNo) {
        this.pinNo = pinNo;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public String getPinStatus() {
        return pinStatus;
    }

    public void setPinStatus(String pinStatus) {
        this.pinStatus = pinStatus;
    }

    public BigInteger getRegProcessStatus() {
        return regProcessStatus;
    }

    public void setRegProcessStatus(BigInteger regProcessStatus) {
        this.regProcessStatus = regProcessStatus;
    }

    public BigInteger getSpId() {
        return spId;
    }

    public void setSpId(BigInteger spId) {
        this.spId = spId;
    }

    public String getNationalId() {
        return nationalId;
    }

    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }

    public String getPotrait() {
        return potrait;
    }

    public void setPotrait(String potrait) {
        this.potrait = potrait;
    }

    public BigInteger getAgencyBnkStatus() {
        return agencyBnkStatus;
    }

    public void setAgencyBnkStatus(BigInteger agencyBnkStatus) {
        this.agencyBnkStatus = agencyBnkStatus;
    }

    public String getSpCustId() {
        return spCustId;
    }

    public void setSpCustId(String spCustId) {
        this.spCustId = spCustId;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public byte[] getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(byte[] fingerprint) {
        this.fingerprint = fingerprint;
    }

    public Integer getPinRetries() {
        return pinRetries;
    }

    public void setPinRetries(Integer pinRetries) {
        this.pinRetries = pinRetries;
    }

    public Integer getOtpRetries() {
        return otpRetries;
    }

    public void setOtpRetries(Integer otpRetries) {
        this.otpRetries = otpRetries;
    }

    public BigInteger getNewEncrypt() {
        return newEncrypt;
    }

    public String getNewPinNo() {
        return newPinNo;
    }

    public void setNewPinNo(String newPinNo) {
        this.newPinNo = newPinNo;
    }

    public void setNewEncrypt(BigInteger newEncrypt) {
        this.newEncrypt = newEncrypt;
    }

    public Integer getWrongPinEntries() { return wrongPinEntries; }

    public void setWrongPinEntries(Integer wrongPinEntries) { this.wrongPinEntries = wrongPinEntries; }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpCustomers)) {
            return false;
        }
        SpCustomers other = (SpCustomers) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "SpCustomers{" +
                "id=" + id +
                ", clientId=" + clientId +
                ", customerName='" + customerName + '\'' +
                ", subscriptionStatus=" + subscriptionStatus +
                ", msisdn='" + msisdn + '\'' +
                ", pinNo='" + pinNo + '\'' +
                ", dateCreated=" + dateCreated +
                ", intrash='" + intrash + '\'' +
                ", pinStatus='" + pinStatus + '\'' +
                ", regProcessStatus=" + regProcessStatus +
                ", spId=" + spId +
                ", nationalId='" + nationalId + '\'' +
                ", potrait='" + potrait + '\'' +
                ", agencyBnkStatus=" + agencyBnkStatus +
                ", spCustId='" + spCustId + '\'' +
                ", accountNumber='" + accountNumber + '\'' +
                ", fingerprint='" + fingerprint + '\'' +
                '}';
    }
}
