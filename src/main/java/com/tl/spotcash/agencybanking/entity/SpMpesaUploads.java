/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_MPESA_UPLOADS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpMpesaUploads.findAll", query = "SELECT s FROM SpMpesaUploads s"),
    @NamedQuery(name = "SpMpesaUploads.findById", query = "SELECT s FROM SpMpesaUploads s WHERE s.id = :id"),
    @NamedQuery(name = "SpMpesaUploads.findByDateTime", query = "SELECT s FROM SpMpesaUploads s WHERE s.dateTime = :dateTime"),
    @NamedQuery(name = "SpMpesaUploads.findByNotes", query = "SELECT s FROM SpMpesaUploads s WHERE s.notes = :notes"),
    @NamedQuery(name = "SpMpesaUploads.findByStatus", query = "SELECT s FROM SpMpesaUploads s WHERE s.status = :status"),
    @NamedQuery(name = "SpMpesaUploads.findByUploadedBy", query = "SELECT s FROM SpMpesaUploads s WHERE s.uploadedBy = :uploadedBy"),
    @NamedQuery(name = "SpMpesaUploads.findByDecKey", query = "SELECT s FROM SpMpesaUploads s WHERE s.decKey = :decKey"),
    @NamedQuery(name = "SpMpesaUploads.findByCsvFile", query = "SELECT s FROM SpMpesaUploads s WHERE s.csvFile = :csvFile")})
public class SpMpesaUploads implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "DATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTime;
    @Size(max = 255)
    @Column(name = "NOTES")
    private String notes;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Column(name = "UPLOADED_BY")
    private BigInteger uploadedBy;
    @Size(max = 255)
    @Column(name = "DEC_KEY")
    private String decKey;
    @Size(max = 255)
    @Column(name = "CSV_FILE")
    private String csvFile;

    public SpMpesaUploads() {
    }

    public SpMpesaUploads(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigInteger getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(BigInteger uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getDecKey() {
        return decKey;
    }

    public void setDecKey(String decKey) {
        this.decKey = decKey;
    }

    public String getCsvFile() {
        return csvFile;
    }

    public void setCsvFile(String csvFile) {
        this.csvFile = csvFile;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpMpesaUploads)) {
            return false;
        }
        SpMpesaUploads other = (SpMpesaUploads) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpMpesaUploads[ id=" + id + " ]";
    }
    
}
