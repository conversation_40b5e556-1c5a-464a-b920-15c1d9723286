/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_GLOBALPARAMS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpGlobalparams.findAll", query = "SELECT s FROM SpGlobalparams s"),
    @NamedQuery(name = "SpGlobalparams.findById", query = "SELECT s FROM SpGlobalparams s WHERE s.id = :id"),
    @NamedQuery(name = "SpGlobalparams.findByParameter", query = "SELECT s FROM SpGlobalparams s WHERE s.parameter = :parameter"),
    @NamedQuery(name = "SpGlobalparams.findByValue", query = "SELECT s FROM SpGlobalparams s WHERE s.value = :value"),
    @NamedQuery(name = "SpGlobalparams.findByDescription", query = "SELECT s FROM SpGlobalparams s WHERE s.description = :description"),
    @NamedQuery(name = "SpGlobalparams.findByDateCreated", query = "SELECT s FROM SpGlobalparams s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpGlobalparams.findByPosition", query = "SELECT s FROM SpGlobalparams s WHERE s.position = :position")})
public class SpGlobalparams implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "PARAMETER")
    private String parameter;
    @Size(max = 255)
    @Column(name = "VALUE")
    private String value;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "POSITION")
    private BigInteger position;

    public SpGlobalparams() {
    }

    public SpGlobalparams(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getParameter() {
        return parameter;
    }

    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigInteger getPosition() {
        return position;
    }

    public void setPosition(BigInteger position) {
        this.position = position;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpGlobalparams)) {
            return false;
        }
        SpGlobalparams other = (SpGlobalparams) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpGlobalparams[ id=" + id + " ]";
    }
    
}
