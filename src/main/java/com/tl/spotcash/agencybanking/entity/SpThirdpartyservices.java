/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_THIRDPARTYSERVICES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpThirdpartyservices.findAll", query = "SELECT s FROM SpThirdpartyservices s"),
    @NamedQuery(name = "SpThirdpartyservices.findById", query = "SELECT s FROM SpThirdpartyservices s WHERE s.id = :id"),
    @NamedQuery(name = "SpThirdpartyservices.findByTitle", query = "SELECT s FROM SpThirdpartyservices s WHERE s.title = :title"),
    @NamedQuery(name = "SpThirdpartyservices.findByStatus", query = "SELECT s FROM SpThirdpartyservices s WHERE s.status = :status"),
    @NamedQuery(name = "SpThirdpartyservices.findByAccessPoint", query = "SELECT s FROM SpThirdpartyservices s WHERE s.accessPoint = :accessPoint"),
    @NamedQuery(name = "SpThirdpartyservices.findByAccessPort", query = "SELECT s FROM SpThirdpartyservices s WHERE s.accessPort = :accessPort"),
    @NamedQuery(name = "SpThirdpartyservices.findByUsername1", query = "SELECT s FROM SpThirdpartyservices s WHERE s.username1 = :username1"),
    @NamedQuery(name = "SpThirdpartyservices.findByUsername2", query = "SELECT s FROM SpThirdpartyservices s WHERE s.username2 = :username2"),
    @NamedQuery(name = "SpThirdpartyservices.findByPassword1", query = "SELECT s FROM SpThirdpartyservices s WHERE s.password1 = :password1"),
    @NamedQuery(name = "SpThirdpartyservices.findByPassword2", query = "SELECT s FROM SpThirdpartyservices s WHERE s.password2 = :password2"),
    @NamedQuery(name = "SpThirdpartyservices.findByOtherDetails", query = "SELECT s FROM SpThirdpartyservices s WHERE s.otherDetails = :otherDetails"),
    @NamedQuery(name = "SpThirdpartyservices.findByServiceStatusUptime", query = "SELECT s FROM SpThirdpartyservices s WHERE s.serviceStatusUptime = :serviceStatusUptime"),
    @NamedQuery(name = "SpThirdpartyservices.findByDateCreated", query = "SELECT s FROM SpThirdpartyservices s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpThirdpartyservices.findByMnoId", query = "SELECT s FROM SpThirdpartyservices s WHERE s.mnoId = :mnoId"),
    @NamedQuery(name = "SpThirdpartyservices.findByUtilityBal", query = "SELECT s FROM SpThirdpartyservices s WHERE s.utilityBal = :utilityBal"),
    @NamedQuery(name = "SpThirdpartyservices.findByIntrash", query = "SELECT s FROM SpThirdpartyservices s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpThirdpartyservices.findByNumberOfThreads", query = "SELECT s FROM SpThirdpartyservices s WHERE s.numberOfThreads = :numberOfThreads"),
    @NamedQuery(name = "SpThirdpartyservices.findByTimeoutMs", query = "SELECT s FROM SpThirdpartyservices s WHERE s.timeoutMs = :timeoutMs"),
    @NamedQuery(name = "SpThirdpartyservices.findByRefreshRateMs", query = "SELECT s FROM SpThirdpartyservices s WHERE s.refreshRateMs = :refreshRateMs"),
    @NamedQuery(name = "SpThirdpartyservices.findByHost", query = "SELECT s FROM SpThirdpartyservices s WHERE s.host = :host"),
    @NamedQuery(name = "SpThirdpartyservices.findByBindingPort", query = "SELECT s FROM SpThirdpartyservices s WHERE s.bindingPort = :bindingPort"),
    @NamedQuery(name = "SpThirdpartyservices.findByHost2", query = "SELECT s FROM SpThirdpartyservices s WHERE s.host2 = :host2"),
    @NamedQuery(name = "SpThirdpartyservices.findByBindingPort2", query = "SELECT s FROM SpThirdpartyservices s WHERE s.bindingPort2 = :bindingPort2"),
    @NamedQuery(name = "SpThirdpartyservices.findByProcessingSpeedMs", query = "SELECT s FROM SpThirdpartyservices s WHERE s.processingSpeedMs = :processingSpeedMs"),
    @NamedQuery(name = "SpThirdpartyservices.findByDowntimeAlert", query = "SELECT s FROM SpThirdpartyservices s WHERE s.downtimeAlert = :downtimeAlert"),
    @NamedQuery(name = "SpThirdpartyservices.findByMmfBal", query = "SELECT s FROM SpThirdpartyservices s WHERE s.mmfBal = :mmfBal")})
public class SpThirdpartyservices implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Size(max = 255)
    @Column(name = "ACCESS_POINT")
    private String accessPoint;
    @Size(max = 255)
    @Column(name = "ACCESS_PORT")
    private String accessPort;
    @Size(max = 255)
    @Column(name = "USERNAME1")
    private String username1;
    @Size(max = 255)
    @Column(name = "USERNAME2")
    private String username2;
    @Size(max = 255)
    @Column(name = "PASSWORD1")
    private String password1;
    @Size(max = 255)
    @Column(name = "PASSWORD2")
    private String password2;
    @Size(max = 255)
    @Column(name = "OTHER_DETAILS")
    private String otherDetails;
    @Column(name = "SERVICE_STATUS_UPTIME")
    private BigInteger serviceStatusUptime;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "MNO_ID")
    private BigInteger mnoId;
    @Column(name = "UTILITY_BAL")
    private BigInteger utilityBal;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "NUMBER_OF_THREADS")
    private BigInteger numberOfThreads;
    @Column(name = "TIMEOUT_MS")
    private BigInteger timeoutMs;
    @Column(name = "REFRESH_RATE_MS")
    private BigInteger refreshRateMs;
    @Size(max = 255)
    @Column(name = "HOST")
    private String host;
    @Column(name = "BINDING_PORT")
    private BigInteger bindingPort;
    @Size(max = 255)
    @Column(name = "HOST2")
    private String host2;
    @Column(name = "BINDING_PORT2")
    private BigInteger bindingPort2;
    @Column(name = "PROCESSING_SPEED_MS")
    private BigInteger processingSpeedMs;
    @Column(name = "DOWNTIME_ALERT")
    private BigInteger downtimeAlert;
    @Column(name = "MMF_BAL")
    private BigInteger mmfBal;

    public SpThirdpartyservices() {
    }

    public SpThirdpartyservices(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAccessPoint() {
        return accessPoint;
    }

    public void setAccessPoint(String accessPoint) {
        this.accessPoint = accessPoint;
    }

    public String getAccessPort() {
        return accessPort;
    }

    public void setAccessPort(String accessPort) {
        this.accessPort = accessPort;
    }

    public String getUsername1() {
        return username1;
    }

    public void setUsername1(String username1) {
        this.username1 = username1;
    }

    public String getUsername2() {
        return username2;
    }

    public void setUsername2(String username2) {
        this.username2 = username2;
    }

    public String getPassword1() {
        return password1;
    }

    public void setPassword1(String password1) {
        this.password1 = password1;
    }

    public String getPassword2() {
        return password2;
    }

    public void setPassword2(String password2) {
        this.password2 = password2;
    }

    public String getOtherDetails() {
        return otherDetails;
    }

    public void setOtherDetails(String otherDetails) {
        this.otherDetails = otherDetails;
    }

    public BigInteger getServiceStatusUptime() {
        return serviceStatusUptime;
    }

    public void setServiceStatusUptime(BigInteger serviceStatusUptime) {
        this.serviceStatusUptime = serviceStatusUptime;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigInteger getMnoId() {
        return mnoId;
    }

    public void setMnoId(BigInteger mnoId) {
        this.mnoId = mnoId;
    }

    public BigInteger getUtilityBal() {
        return utilityBal;
    }

    public void setUtilityBal(BigInteger utilityBal) {
        this.utilityBal = utilityBal;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public BigInteger getNumberOfThreads() {
        return numberOfThreads;
    }

    public void setNumberOfThreads(BigInteger numberOfThreads) {
        this.numberOfThreads = numberOfThreads;
    }

    public BigInteger getTimeoutMs() {
        return timeoutMs;
    }

    public void setTimeoutMs(BigInteger timeoutMs) {
        this.timeoutMs = timeoutMs;
    }

    public BigInteger getRefreshRateMs() {
        return refreshRateMs;
    }

    public void setRefreshRateMs(BigInteger refreshRateMs) {
        this.refreshRateMs = refreshRateMs;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public BigInteger getBindingPort() {
        return bindingPort;
    }

    public void setBindingPort(BigInteger bindingPort) {
        this.bindingPort = bindingPort;
    }

    public String getHost2() {
        return host2;
    }

    public void setHost2(String host2) {
        this.host2 = host2;
    }

    public BigInteger getBindingPort2() {
        return bindingPort2;
    }

    public void setBindingPort2(BigInteger bindingPort2) {
        this.bindingPort2 = bindingPort2;
    }

    public BigInteger getProcessingSpeedMs() {
        return processingSpeedMs;
    }

    public void setProcessingSpeedMs(BigInteger processingSpeedMs) {
        this.processingSpeedMs = processingSpeedMs;
    }

    public BigInteger getDowntimeAlert() {
        return downtimeAlert;
    }

    public void setDowntimeAlert(BigInteger downtimeAlert) {
        this.downtimeAlert = downtimeAlert;
    }

    public BigInteger getMmfBal() {
        return mmfBal;
    }

    public void setMmfBal(BigInteger mmfBal) {
        this.mmfBal = mmfBal;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpThirdpartyservices)) {
            return false;
        }
        SpThirdpartyservices other = (SpThirdpartyservices) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpThirdpartyservices[ id=" + id + " ]";
    }
    
}
