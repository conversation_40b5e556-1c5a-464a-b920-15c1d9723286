/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AUDIT_TRAIL")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAuditTrail.findAll", query = "SELECT s FROM SpAuditTrail s"),
    @NamedQuery(name = "SpAuditTrail.findById", query = "SELECT s FROM SpAuditTrail s WHERE s.id = :id"),
    @NamedQuery(name = "SpAuditTrail.findByType", query = "SELECT s FROM SpAuditTrail s WHERE s.type = :type"),
    @NamedQuery(name = "SpAuditTrail.findByUserId", query = "SELECT s FROM SpAuditTrail s WHERE s.userId = :userId"),
    @NamedQuery(name = "SpAuditTrail.findByDescription", query = "SELECT s FROM SpAuditTrail s WHERE s.description = :description"),
    @NamedQuery(name = "SpAuditTrail.findByHostIp", query = "SELECT s FROM SpAuditTrail s WHERE s.hostIp = :hostIp"),
    @NamedQuery(name = "SpAuditTrail.findByLogDate", query = "SELECT s FROM SpAuditTrail s WHERE s.logDate = :logDate"),
    @NamedQuery(name = "SpAuditTrail.findByTrxId", query = "SELECT s FROM SpAuditTrail s WHERE s.trxId = :trxId")})
public class SpAuditTrail implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TYPE")
    private String type;
    @Column(name = "USER_ID")
    private BigInteger userId;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "HOST_IP")
    private String hostIp;
    @Column(name = "LOG_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date logDate;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;

    public SpAuditTrail() {
    }

    public SpAuditTrail(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigInteger getUserId() {
        return userId;
    }

    public void setUserId(BigInteger userId) {
        this.userId = userId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHostIp() {
        return hostIp;
    }

    public void setHostIp(String hostIp) {
        this.hostIp = hostIp;
    }

    public Date getLogDate() {
        return logDate;
    }

    public void setLogDate(Date logDate) {
        this.logDate = logDate;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAuditTrail)) {
            return false;
        }
        SpAuditTrail other = (SpAuditTrail) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAuditTrail[ id=" + id + " ]";
    }
    
}
