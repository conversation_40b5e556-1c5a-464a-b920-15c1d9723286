package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_STORE_USER_BLACKLIST")
public class SpStoreUserBlacklist {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigInteger id;

    @Basic
    @Column(name = "STORE_USER_ID")
    private Long storeUserId;

    @Basic
    @Column(name = "DATE_CREATED")
    private Timestamp dateCreated;

    @Basic
    @Column(name = "IP_ADDRESS")
    private String ipAddress;

    public BigInteger getId() {return id;}

    public void setId(BigInteger id) {this.id = id;}

    public Long getStoreUserId() {return storeUserId;}

    public void setStoreUserId(Long storeUserId) {this.storeUserId = storeUserId;}

    public Timestamp getDateCreated() {return dateCreated;}

    public void setDateCreated(Timestamp date) {this.dateCreated = date;}

    public String getIpAddress() {return ipAddress;}

    public void setIpAddress(String ipAddress) {this.ipAddress = ipAddress;}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SpStoreUserBlacklist)) return false;
        SpStoreUserBlacklist that = (SpStoreUserBlacklist) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getStoreUserId(), that.getStoreUserId()) && Objects.equals(getDateCreated(), that.getDateCreated()) && Objects.equals(getIpAddress(), that.getIpAddress());
    }

    @Override
    public int hashCode() {return Objects.hash(getId(), getStoreUserId(), getDateCreated(), getIpAddress());}
}
