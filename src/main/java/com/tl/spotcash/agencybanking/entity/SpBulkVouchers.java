/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_BULK_VOUCHERS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpBulkVouchers.findAll", query = "SELECT s FROM SpBulkVouchers s"),
    @NamedQuery(name = "SpBulkVouchers.findById", query = "SELECT s FROM SpBulkVouchers s WHERE s.id = :id"),
    @NamedQuery(name = "SpBulkVouchers.findByDecKey", query = "SELECT s FROM SpBulkVouchers s WHERE s.decKey = :decKey"),
    @NamedQuery(name = "SpBulkVouchers.findByStatus", query = "SELECT s FROM SpBulkVouchers s WHERE s.status = :status"),
    @NamedQuery(name = "SpBulkVouchers.findByDescription", query = "SELECT s FROM SpBulkVouchers s WHERE s.description = :description"),
    @NamedQuery(name = "SpBulkVouchers.findByTimeUploaded", query = "SELECT s FROM SpBulkVouchers s WHERE s.timeUploaded = :timeUploaded"),
    @NamedQuery(name = "SpBulkVouchers.findByTimeProcessed", query = "SELECT s FROM SpBulkVouchers s WHERE s.timeProcessed = :timeProcessed"),
    @NamedQuery(name = "SpBulkVouchers.findByNotes", query = "SELECT s FROM SpBulkVouchers s WHERE s.notes = :notes"),
    @NamedQuery(name = "SpBulkVouchers.findByUploadedBy", query = "SELECT s FROM SpBulkVouchers s WHERE s.uploadedBy = :uploadedBy"),
    @NamedQuery(name = "SpBulkVouchers.findByFilename", query = "SELECT s FROM SpBulkVouchers s WHERE s.filename = :filename")})
public class SpBulkVouchers implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "DEC_KEY")
    private String decKey;
    @Lob
    @Column(name = "CSV_FILE")
    private String csvFile;
    @Column(name = "STATUS")
    private BigInteger status;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "TIME_UPLOADED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeUploaded;
    @Column(name = "TIME_PROCESSED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeProcessed;
    @Size(max = 255)
    @Column(name = "NOTES")
    private String notes;
    @Column(name = "UPLOADED_BY")
    private BigInteger uploadedBy;
    @Size(max = 255)
    @Column(name = "FILENAME")
    private String filename;

    public SpBulkVouchers() {
    }

    public SpBulkVouchers(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getDecKey() {
        return decKey;
    }

    public void setDecKey(String decKey) {
        this.decKey = decKey;
    }

    public String getCsvFile() {
        return csvFile;
    }

    public void setCsvFile(String csvFile) {
        this.csvFile = csvFile;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getTimeUploaded() {
        return timeUploaded;
    }

    public void setTimeUploaded(Date timeUploaded) {
        this.timeUploaded = timeUploaded;
    }

    public Date getTimeProcessed() {
        return timeProcessed;
    }

    public void setTimeProcessed(Date timeProcessed) {
        this.timeProcessed = timeProcessed;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public BigInteger getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(BigInteger uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpBulkVouchers)) {
            return false;
        }
        SpBulkVouchers other = (SpBulkVouchers) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpBulkVouchers[ id=" + id + " ]";
    }
    
}
