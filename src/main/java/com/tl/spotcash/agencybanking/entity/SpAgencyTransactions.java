/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AGENCY_TRANSACTIONS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAgencyTransactions.findAll", query = "SELECT s FROM SpAgencyTransactions s"),
    @NamedQuery(name = "SpAgencyTransactions.findById", query = "SELECT s FROM SpAgencyTransactions s WHERE s.id = :id"),
    @NamedQuery(name = "SpAgencyTransactions.findByTrxId", query = "SELECT s FROM SpAgencyTransactions s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpAgencyTransactions.findByClientId", query = "SELECT s FROM SpAgencyTransactions s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAgencyTransactions.findByMsisdn", query = "SELECT s FROM SpAgencyTransactions s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpAgencyTransactions.findByAmount", query = "SELECT s FROM SpAgencyTransactions s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpAgencyTransactions.findBySpotcashComission", query = "SELECT s FROM SpAgencyTransactions s WHERE s.spotcashComission = :spotcashComission"),
    @NamedQuery(name = "SpAgencyTransactions.findByTrxStatus", query = "SELECT s FROM SpAgencyTransactions s WHERE s.trxStatus = :trxStatus"),
    @NamedQuery(name = "SpAgencyTransactions.findByRespCode", query = "SELECT s FROM SpAgencyTransactions s WHERE s.respCode = :respCode"),
    @NamedQuery(name = "SpAgencyTransactions.findByDescription", query = "SELECT s FROM SpAgencyTransactions s WHERE s.description = :description"),
    @NamedQuery(name = "SpAgencyTransactions.findByCustomerName", query = "SELECT s FROM SpAgencyTransactions s WHERE s.customerName = :customerName"),
    @NamedQuery(name = "SpAgencyTransactions.findByTimeCompleted", query = "SELECT s FROM SpAgencyTransactions s WHERE s.timeCompleted = :timeCompleted"),
    @NamedQuery(name = "SpAgencyTransactions.findByAgentId", query = "SELECT s FROM SpAgencyTransactions s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpAgencyTransactions.findByAgentStoreId", query = "SELECT s FROM SpAgencyTransactions s WHERE s.agentStoreId = :agentStoreId"),
    @NamedQuery(name = "SpAgencyTransactions.findByServiceId", query = "SELECT s FROM SpAgencyTransactions s WHERE s.serviceId = :serviceId")})
public class SpAgencyTransactions implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "AGENCY_TRANSACTIONS_SEQ")
    @SequenceGenerator(name = "AGENCY_TRANSACTIONS_SEQ", sequenceName = "AGENCY_TRANSACTIONS_SEQ", allocationSize = 1)
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "TRANS_ID")
    private BigDecimal transId;
    @Size(max = 20)
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 20)
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "SPOTCASH_COMISSION")
    private BigDecimal spotcashComission;
    @Column(name = "THIRDPARTY_CHARGE")
    private BigDecimal thirdpartyCharge;
    @Column(name = "TRX_STATUS")
    private BigInteger trxStatus;
    @Column(name = "RESP_CODE")
    private String respCode;
    @Size(max = 200)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 50)
    @Column(name = "CUSTOMER_NAME")
    private String customerName;
    @Column(name = "REQUEST_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestTime;
    @Column(name = "TIME_COMPLETED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeCompleted;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;

    public SpAgencyTransactions() {
    }

    public SpAgencyTransactions(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getTransId() {
        return transId;
    }

    public void setTransId(BigDecimal transId) {
        this.transId = transId;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSpotcashComission() {
        return spotcashComission;
    }

    public void setSpotcashComission(BigDecimal spotcashComission) {
        this.spotcashComission = spotcashComission;
    }

    public BigDecimal getThirdpartyCharge() {
        return thirdpartyCharge;
    }

    public void setThirdpartyCharge(BigDecimal thirdpartyCharge) {
        this.thirdpartyCharge = thirdpartyCharge;
    }

    public BigInteger getTrxStatus() {
        return trxStatus;
    }

    public void setTrxStatus(BigInteger trxStatus) {
        this.trxStatus = trxStatus;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Date getTimeCompleted() {
        return timeCompleted;
    }

    public void setTimeCompleted(Date timeCompleted) {
        this.timeCompleted = timeCompleted;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAgencyTransactions)) {
            return false;
        }
        SpAgencyTransactions other = (SpAgencyTransactions) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAgencyTransactions[ id=" + id + " ]";
    }
    
}
