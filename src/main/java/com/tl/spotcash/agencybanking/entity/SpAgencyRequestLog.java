package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.time.LocalDateTime;

@Entity
@Table(name = "SP_AGENCY_REQUEST_LOG")
@NamedQueries({
        @NamedQuery(name = "SpAgencyRequestLog.findByIpAndTime", query = "SELECT s FROM SpAgencyRequestLog s WHERE s.sourceIp = :sourceIp and s.time > :time"),
})
public class SpAgencyRequestLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigInteger id;
    @Column(name = "SOURCE_IP")
    private String sourceIp;
    @Column(name = "URL")
    private String Url;
    @Column(name = "REQUEST_BODY")
    private String requestBody;
    @Column(name = "THREAD")
    private String thread;
    @Column(name = "TIME")
    private LocalDateTime time;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public String getUrl() {
        return Url;
    }

    public void setUrl(String url) {
        Url = url;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public LocalDateTime getTime() {
        return time;
    }

    public void setTime(LocalDateTime time) {
        this.time = time;
    }

    public String getThread() {
        return thread;
    }

    public void setThread(String thread) {
        this.thread = thread;
    }
}
