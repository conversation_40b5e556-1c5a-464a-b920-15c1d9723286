package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_AGENCY_BLACKLIST_ARCHIVE")
public class SpAgencyBlacklistArchive {
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SP_AGENCY_BLACKLIST_ARCHIVE_SEQ")
    @SequenceGenerator(name = "SP_AGENCY_BLACKLIST_ARCHIVE_SEQ", sequenceName = "SP_AGENCY_BLACKLIST_ARCHIVE_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Id
    @Column(name = "ID")
    private BigInteger id;
    @Basic
    @Column(name = "MSISDN")
    private String msisdn;
    @Basic
    @Column(name = "DATE_CREATED")
    private Timestamp dateCreated;
    @Basic
    @Column(name = "IP_ADDRESS")
    private String ipAddress;
    @Basic
    @Column(name = "CLIENT_ID")
    private Long clientId;

    @Basic
    @Column(name = "STORE_USER_ID")
    private Long storeUserId;

    public SpAgencyBlacklistArchive(String msisdn, Timestamp dateCreated, String ipAddress, Long clientId, Long storeUserId) {
        this.msisdn = msisdn;
        this.dateCreated = dateCreated;
        this.ipAddress = ipAddress;
        this.clientId = clientId;
        this.storeUserId = storeUserId;
    }

    public SpAgencyBlacklistArchive() {}

    public BigInteger getId() {return id;}

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getStoreUserId() {
        return storeUserId;
    }

    public void setStoreUserId(Long storeUserId) {
        this.storeUserId = storeUserId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SpAgencyBlacklistArchive)) return false;
        SpAgencyBlacklistArchive that = (SpAgencyBlacklistArchive) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getMsisdn(), that.getMsisdn())
                && Objects.equals(getDateCreated(), that.getDateCreated())
                && Objects.equals(getIpAddress(), that.getIpAddress())
                && Objects.equals(getClientId(), that.getClientId())
                && Objects.equals(getStoreUserId(), that.getStoreUserId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getMsisdn(), getDateCreated(), getIpAddress(), getClientId(), getStoreUserId());
    }
}
