/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TARIFF")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTariff.findAll", query = "SELECT s FROM SpTariff s"),
    @NamedQuery(name = "SpTariff.findById", query = "SELECT s FROM SpTariff s WHERE s.id = :id"),
    @NamedQuery(name = "SpTariff.findByTitle", query = "SELECT s FROM SpTariff s WHERE s.title = :title"),
    @NamedQuery(name = "SpTariff.findByType", query = "SELECT s FROM SpTariff s WHERE s.type = :type"),
    @NamedQuery(name = "SpTariff.findByIntrash", query = "SELECT s FROM SpTariff s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpTariff.findByDateCreated", query = "SELECT s FROM SpTariff s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpTariff.findByServiceId", query = "SELECT s FROM SpTariff s WHERE s.serviceId = :serviceId")})
public class SpTariff implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TITLE")
    private String title;
    @Size(max = 255)
    @Column(name = "TYPE")
    private String type;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "SERVICE_ID")
    private BigInteger serviceId;

    public SpTariff() {
    }

    public SpTariff(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigInteger getServiceId() {
        return serviceId;
    }

    public void setServiceId(BigInteger serviceId) {
        this.serviceId = serviceId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTariff)) {
            return false;
        }
        SpTariff other = (SpTariff) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTariff[ id=" + id + " ]";
    }
    
}
