/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ALERT_TYPES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAlertTypes.findAll", query = "SELECT s FROM SpAlertTypes s"),
    @NamedQuery(name = "SpAlertTypes.findById", query = "SELECT s FROM SpAlertTypes s WHERE s.id = :id"),
    @NamedQuery(name = "SpAlertTypes.findByAlertName", query = "SELECT s FROM SpAlertTypes s WHERE s.alertName = :alertName"),
    @NamedQuery(name = "SpAlertTypes.findByAlertType", query = "SELECT s FROM SpAlertTypes s WHERE s.alertType = :alertType"),
    @NamedQuery(name = "SpAlertTypes.findByIntrash", query = "SELECT s FROM SpAlertTypes s WHERE s.intrash = :intrash")})
public class SpAlertTypes implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "ALERT_NAME")
    private String alertName;
    @Size(max = 255)
    @Column(name = "ALERT_TYPE")
    private String alertType;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;

    public SpAlertTypes() {
    }

    public SpAlertTypes(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getAlertName() {
        return alertName;
    }

    public void setAlertName(String alertName) {
        this.alertName = alertName;
    }

    public String getAlertType() {
        return alertType;
    }

    public void setAlertType(String alertType) {
        this.alertType = alertType;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAlertTypes)) {
            return false;
        }
        SpAlertTypes other = (SpAlertTypes) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAlertTypes[ id=" + id + " ]";
    }
    
}
