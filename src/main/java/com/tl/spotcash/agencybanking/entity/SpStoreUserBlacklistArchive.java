package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_STORE_USER_BLACKLIST_ARCHIVE")
public class SpStoreUserBlacklistArchive {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SP_STORE_USER_BLACKLIST_ARCHIVE_SEQ")
    @SequenceGenerator(name = "SP_STORE_USER_BLACKLIST_ARCHIVE_SEQ", sequenceName = "SP_STORE_USER_BLACKLIST_ARCHIVE_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigInteger id;

    @Basic
    @Column(name = "STORE_USER_ID")
    private Long storeUserId;

    @Basic
    @Column(name = "DATE_CREATED")
    private Timestamp dateCreated;

    @Basic
    @Column(name = "IP_ADDRESS")
    private String ipAddress;

    @Basic
    @Column(name = "CLIENT_ID")
    private Long clientId;

    @Basic
    @Column(name = "MSISDN")
    private String msisdn;

    public SpStoreUserBlacklistArchive(Long storeUserId, Timestamp dateCreated, String ipAddress, Long clientId, String msisdn) {
        this.storeUserId = storeUserId;
        this.dateCreated = dateCreated;
        this.ipAddress = ipAddress;
        this.clientId = clientId;
        this.msisdn = msisdn;
    }

    public SpStoreUserBlacklistArchive() {}

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Long getStoreUserId() {
        return storeUserId;
    }

    public void setStoreUserId(Long storeUserId) {
        this.storeUserId = storeUserId;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SpStoreUserBlacklistArchive)) return false;
        SpStoreUserBlacklistArchive that = (SpStoreUserBlacklistArchive) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getStoreUserId(), that.getStoreUserId())
                && Objects.equals(getDateCreated(), that.getDateCreated())
                && Objects.equals(getIpAddress(), that.getIpAddress())
                && Objects.equals(getClientId(), that.getClientId()) && Objects.equals(getMsisdn(), that.getMsisdn());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getStoreUserId(), getDateCreated(), getIpAddress(), getClientId(), getMsisdn());
    }
}
