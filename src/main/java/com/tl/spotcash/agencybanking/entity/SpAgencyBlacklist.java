package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_AGENCY_BLACKLIST")
public class SpAgencyBlacklist {
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SP_AGENCY_BLACKLIST_SEQ")
    @SequenceGenerator(name = "SP_AGENCY_BLACKLIST_SEQ", sequenceName = "SP_AGENCY_BLACKLIST_SEQ", allocationSize = 1)
    @Basic(optional = false)
    @NotNull
    @Id
    @Column(name = "ID")
    private BigInteger id;
    @Basic
    @Column(name = "MSISDN")
    private String msisdn;
    @Basic
    @Column(name = "DATE_CREATED")
    private Timestamp dateCreated;
    @Basic
    @Column(name = "IP_ADDRESS")
    private String ipAddress;
    @Basic
    @Column(name = "CLIENT_ID")
    private Long clientId;

    @Basic
    @Column(name = "STORE_USER_ID")
    private Long storeUserId;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Long getStoreUserId() {return storeUserId;}

    public void setStoreUserId(Long storeUserId) {this.storeUserId = storeUserId;}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SpAgencyBlacklist that = (SpAgencyBlacklist) o;
        return Objects.equals(id, that.id) && Objects.equals(msisdn, that.msisdn) && Objects.equals(dateCreated, that.dateCreated) && Objects.equals(ipAddress, that.ipAddress);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, msisdn, dateCreated, ipAddress);
    }
}
