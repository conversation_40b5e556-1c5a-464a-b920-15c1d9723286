/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TRANS_DIAG")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTransDiag.findAll", query = "SELECT s FROM SpTransDiag s"),
    @NamedQuery(name = "SpTransDiag.findById", query = "SELECT s FROM SpTransDiag s WHERE s.id = :id"),
    @NamedQuery(name = "SpTransDiag.findByTrxId", query = "SELECT s FROM SpTransDiag s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpTransDiag.findByAction", query = "SELECT s FROM SpTransDiag s WHERE s.action = :action"),
    @NamedQuery(name = "SpTransDiag.findByStatus", query = "SELECT s FROM SpTransDiag s WHERE s.status = :status"),
    @NamedQuery(name = "SpTransDiag.findByTimeInitiated", query = "SELECT s FROM SpTransDiag s WHERE s.timeInitiated = :timeInitiated"),
    @NamedQuery(name = "SpTransDiag.findByTimeCompleted", query = "SELECT s FROM SpTransDiag s WHERE s.timeCompleted = :timeCompleted"),
    @NamedQuery(name = "SpTransDiag.findByInitiatedBy", query = "SELECT s FROM SpTransDiag s WHERE s.initiatedBy = :initiatedBy"),
    @NamedQuery(name = "SpTransDiag.findByCompletedBy", query = "SELECT s FROM SpTransDiag s WHERE s.completedBy = :completedBy"),
    @NamedQuery(name = "SpTransDiag.findByDescription", query = "SELECT s FROM SpTransDiag s WHERE s.description = :description"),
    @NamedQuery(name = "SpTransDiag.findByCompletionNotes", query = "SELECT s FROM SpTransDiag s WHERE s.completionNotes = :completionNotes"),
    @NamedQuery(name = "SpTransDiag.findByReason", query = "SELECT s FROM SpTransDiag s WHERE s.reason = :reason")})
public class SpTransDiag implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Size(max = 255)
    @Column(name = "ACTION")
    private String action;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Column(name = "TIME_INITIATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeInitiated;
    @Column(name = "TIME_COMPLETED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timeCompleted;
    @Column(name = "INITIATED_BY")
    private BigInteger initiatedBy;
    @Column(name = "COMPLETED_BY")
    private BigInteger completedBy;
    @Size(max = 255)
    @Column(name = "DESCRIPTION")
    private String description;
    @Size(max = 255)
    @Column(name = "COMPLETION_NOTES")
    private String completionNotes;
    @Size(max = 255)
    @Column(name = "REASON")
    private String reason;

    public SpTransDiag() {
    }

    public SpTransDiag(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getTimeInitiated() {
        return timeInitiated;
    }

    public void setTimeInitiated(Date timeInitiated) {
        this.timeInitiated = timeInitiated;
    }

    public Date getTimeCompleted() {
        return timeCompleted;
    }

    public void setTimeCompleted(Date timeCompleted) {
        this.timeCompleted = timeCompleted;
    }

    public BigInteger getInitiatedBy() {
        return initiatedBy;
    }

    public void setInitiatedBy(BigInteger initiatedBy) {
        this.initiatedBy = initiatedBy;
    }

    public BigInteger getCompletedBy() {
        return completedBy;
    }

    public void setCompletedBy(BigInteger completedBy) {
        this.completedBy = completedBy;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCompletionNotes() {
        return completionNotes;
    }

    public void setCompletionNotes(String completionNotes) {
        this.completionNotes = completionNotes;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTransDiag)) {
            return false;
        }
        SpTransDiag other = (SpTransDiag) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTransDiag[ id=" + id + " ]";
    }
    
}
