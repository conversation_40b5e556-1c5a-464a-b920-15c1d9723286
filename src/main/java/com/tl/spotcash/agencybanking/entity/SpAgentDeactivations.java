package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.Date;

@Entity
@Table(name = "SP_AGENT_ACTIVATIONS")
public class SpAgentDeactivations implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigDecimal id;

    @NotNull
    @Column(name = "AGENT_ID")
    private BigInteger agentId;

    @NotNull
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;

    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;

    @Column(name = "STORE_USER_ID")
    private BigInteger storeUserId;

    @NotNull
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;

    @Size(max = 255)
    @Column(name = "OLD_DEVICE_ID")
    private String oldDeviceId;

    @Size(max = 255)
    @Column(name = "NEW_DEVICE_ID")
    private String newDeviceId;

    @Column(name = "OLD_SWAP_DATE")
    private LocalDate oldSwapDate;

    @Column(name = "NEW_SWAP_DATE")
    private LocalDate newSwapDate;

    @Size(max = 255)
    @Column(name = "DEACTIVATION_TYPE")
    private String deactivationType;

    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Size(max = 255)
    @Column(name = "DEACTIVATION_REASON")
    private String deactivationReason;

    public SpAgentDeactivations() {
    }

    public SpAgentDeactivations(BigDecimal id, BigInteger agentId, BigInteger clientId, BigInteger agentStoreId, BigInteger storeUserId, Date dateCreated, String oldDeviceId, String newDeviceId, LocalDate oldSwapDate, LocalDate newSwapDate, String deactivationType, String status, String deactivationReason) {
        this.id = id;
        this.agentId = agentId;
        this.clientId = clientId;
        this.agentStoreId = agentStoreId;
        this.storeUserId = storeUserId;
        this.dateCreated = dateCreated;
        this.oldDeviceId = oldDeviceId;
        this.newDeviceId = newDeviceId;
        this.oldSwapDate = oldSwapDate;
        this.newSwapDate = newSwapDate;
        this.deactivationType = deactivationType;
        this.status = status;
        this.deactivationReason = deactivationReason;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getOldDeviceId() {
        return oldDeviceId;
    }

    public void setOldDeviceId(String oldDeviceId) {
        this.oldDeviceId = oldDeviceId;
    }

    public String getNewDeviceId() {
        return newDeviceId;
    }

    public void setNewDeviceId(String newDeviceId) {
        this.newDeviceId = newDeviceId;
    }

    public LocalDate getOldSwapDate() {
        return oldSwapDate;
    }

    public void setOldSwapDate(LocalDate oldSwapDate) {
        this.oldSwapDate = oldSwapDate;
    }

    public LocalDate getNewSwapDate() {
        return newSwapDate;
    }

    public void setNewSwapDate(LocalDate newSwapDate) {
        this.newSwapDate = newSwapDate;
    }

    public String getDeactivationType() {
        return deactivationType;
    }

    public void setDeactivationType(String deactivationType) {
        this.deactivationType = deactivationType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public BigInteger getStoreUserId() {
        return storeUserId;
    }

    public void setStoreUserId(BigInteger storeUserId) {
        this.storeUserId = storeUserId;
    }
    public String getDeactivationReason() {  return deactivationReason; }
    public void setDeactivationReason(String deactivationReason) { this.deactivationReason = deactivationReason; }

    @Override
    public String toString() {
        return "SpAgentDeactivations{" +
                "id=" + id +
                ", agentId=" + agentId +
                ", clientId=" + clientId +
                ", agentStoreId=" + agentStoreId +
                ", storeUserId=" + storeUserId +
                ", dateCreated=" + dateCreated +
                ", oldDeviceId='" + oldDeviceId + '\'' +
                ", newDeviceId='" + newDeviceId + '\'' +
                ", oldSwapDate=" + oldSwapDate +
                ", newSwapDate=" + newSwapDate +
                ", deactivationType='" + deactivationType + '\'' +
                ", status='" + status + '\'' +
                ", deactivationReason='" + deactivationReason + '\'' +
                '}';
    }
}
