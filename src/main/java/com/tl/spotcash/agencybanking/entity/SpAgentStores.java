/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_AGENT_STORES")
@XmlRootElement
@NamedQueries({
	@NamedQuery(name = "SpAgentStores.findAll", query = "SELECT s FROM SpAgentStores s"),
	@NamedQuery(name = "SpAgentStores.findByAgentStoreId", query = "SELECT s FROM SpAgentStores s WHERE s.agentStoreId = :agentStoreId"),
	@NamedQuery(name = "SpAgentStores.findByAgentId", query = "SELECT s FROM SpAgentStores s WHERE s.agentId = :agentId"),
	@NamedQuery(name = "SpAgentStores.findByAgentStoreCode", query = "SELECT s FROM SpAgentStores s WHERE s.agentStoreCode = :agentStoreCode"),
	@NamedQuery(name = "SpAgentStores.findByAgentStoreName", query = "SELECT s FROM SpAgentStores s WHERE s.agentStoreName = :agentStoreName"),
	@NamedQuery(name = "SpAgentStores.findByContactName", query = "SELECT s FROM SpAgentStores s WHERE s.contactName = :contactName"),
	@NamedQuery(name = "SpAgentStores.findByContactMsisdn", query = "SELECT s FROM SpAgentStores s WHERE s.contactMsisdn = :contactMsisdn"),
	@NamedQuery(name = "SpAgentStores.findByDateCreated", query = "SELECT s FROM SpAgentStores s WHERE s.dateCreated = :dateCreated"),
	@NamedQuery(name = "SpAgentStores.findByIntrash", query = "SELECT s FROM SpAgentStores s WHERE s.intrash = :intrash"),
	@NamedQuery(name = "SpAgentStores.findByPasskey", query = "SELECT s FROM SpAgentStores s WHERE s.passkey = :passkey"),
	@NamedQuery(name = "SpAgentStores.findByActiveStatus", query = "SELECT s FROM SpAgentStores s WHERE s.activeStatus = :activeStatus"),
	@NamedQuery(name = "SpAgentStores.findByLocation", query = "SELECT s FROM SpAgentStores s WHERE s.location = :location"),
	@NamedQuery(name = "SpAgentStores.findByPin", query = "SELECT s FROM SpAgentStores s WHERE s.pin = :pin"),
	@NamedQuery(name = "SpAgentStores.findByPassword", query = "SELECT s FROM SpAgentStores s WHERE s.password = :password"),
	@NamedQuery(name = "SpAgentStores.findByDeviceId", query = "SELECT s FROM SpAgentStores s WHERE s.deviceId = :deviceId")})
public class SpAgentStores implements Serializable {

	private static final long serialVersionUID = 1L;
	// @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "AGENT_STORE_ID")
	private BigInteger agentStoreId;
	@Column(name = "AGENT_ID")
	private BigInteger agentId;
	@Size(max = 255)
	@Column(name = "AGENT_STORE_CODE")
	private String agentStoreCode;
	@Size(max = 255)
	@Column(name = "AGENT_STORE_NAME")
	private String agentStoreName;
	@Size(max = 255)
	@Column(name = "CONTACT_NAME")
	private String contactName;
	@Size(max = 255)
	@Column(name = "CONTACT_MSISDN")
	private String contactMsisdn;
	@Column(name = "DATE_CREATED")
	@Temporal(TemporalType.TIMESTAMP)
	private Date dateCreated;
	@Size(max = 255)
	@Column(name = "INTRASH")
	private String intrash;
	@Size(max = 255)
	@Column(name = "PASSKEY")
	private String passkey;
	@Column(name = "ACTIVE_STATUS")
	private BigInteger activeStatus;
	@Size(max = 255)
	@Column(name = "LOCATION")
	private String location;
	@Size(max = 255)
	@Column(name = "PIN")
	private String pin;
	@Size(max = 255)
	@Column(name = "PASSWORD")
	private String password;
	@Size(max = 255)
	@Column(name = "DEVICE_ID")
	private String deviceId;
	@Size(max = 255)
	@Column(name = "CLIENT_ID")
	private BigInteger clientId;
	@Column(name = "BRANCH_NAME")
	private String branchName;
	@Column(name = "BRANCH_CODE")
	private String branch_code;
	@Column(name = "UNIQUE_ID")
	private String uniqueId;

	@Column(name = "CENTRAL_LOCATION")
	private String centralLocation;

	@Column(name = "STORE_RADIUS")
	private float storeRadius;

	@Column(name = "START_TIME")
	private String startTime;

	@Column(name = "END_TIME")
	private String endTime;

	@Column(name = "AGENT_TYPE")
	private Integer agentType;

	public SpAgentStores() {
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public SpAgentStores(BigInteger agentStoreId) {
		this.agentStoreId = agentStoreId;
	}

	public String getCentralLocation() {
		return centralLocation;
	}

	public void setCentralLocation(String centralLocation) {
		this.centralLocation = centralLocation;
	}

	public float getStoreRadius() {
		return storeRadius;
	}

	public void setStoreRadius(float storeRadius) {
		this.storeRadius = storeRadius;
	}

	public String getStartTime() { return startTime; }

	public void setStartTime(String startTime) { this.startTime = startTime; }

	public String getEndTime() { return endTime;}

	public void setEndTime(String endTime) { this.endTime = endTime; }

	public BigInteger getAgentStoreId() {
		return agentStoreId;
	}

	public void setAgentStoreId(BigInteger agentStoreId) {
		this.agentStoreId = agentStoreId;
	}

	public BigInteger getAgentId() {
		return agentId;
	}

	public void setAgentId(BigInteger agentId) {
		this.agentId = agentId;
	}

	public String getAgentStoreCode() {
		return agentStoreCode;
	}

	public void setAgentStoreCode(String agentStoreCode) {
		this.agentStoreCode = agentStoreCode;
	}

	public String getAgentStoreName() {
		return agentStoreName;
	}

	public void setAgentStoreName(String agentStoreName) {
		this.agentStoreName = agentStoreName;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMsisdn() {
		return contactMsisdn;
	}

	public void setContactMsisdn(String contactMsisdn) {
		this.contactMsisdn = contactMsisdn;
	}

	public Date getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public String getIntrash() {
		return intrash;
	}

	public void setIntrash(String intrash) {
		this.intrash = intrash;
	}

	public String getPasskey() {
		return passkey;
	}

	public void setPasskey(String passkey) {
		this.passkey = passkey;
	}

	public BigInteger getActiveStatus() {
		return activeStatus;
	}

	public void setActiveStatus(BigInteger activeStatus) {
		this.activeStatus = activeStatus;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getPin() {
		return pin;
	}

	public void setPin(String pin) {
		this.pin = pin;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public BigInteger getClientId() {
		return clientId;
	}

	public void setClientId(BigInteger clientId) {
		this.clientId = clientId;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getBranch_code() { return branch_code; }

	public void setBranch_code(String branch_code) { this.branch_code = branch_code; }

	public Integer getAgentType() { return agentType; }

	public void setAgentType(Integer agentType) { this.agentType = agentType; }

	@Override
	public int hashCode() {
		int hash = 0;
		hash += (agentStoreId != null ? agentStoreId.hashCode() : 0);
		return hash;
	}

	@Override
	public boolean equals(Object object) {
		// TODO: Warning - this method won't work in the case the id fields are not set
		if (!(object instanceof SpAgentStores)) {
			return false;
		}
		SpAgentStores other = (SpAgentStores) object;
		if ((this.agentStoreId == null && other.agentStoreId != null) || (this.agentStoreId != null && !this.agentStoreId.equals(other.agentStoreId))) {
			return false;
		}
		return true;
	}

	@Override
	public String toString() {
		return "SpAgentStores{" + "agentStoreId=" + agentStoreId + ", agentId=" + agentId + ", agentStoreCode=" + agentStoreCode + ", agentStoreName=" + agentStoreName + ", contactName=" + contactName + ", contactMsisdn=" + contactMsisdn + ", dateCreated=" + dateCreated + ", intrash=" + intrash + ", passkey=" + passkey + ", activeStatus=" + activeStatus + ", location=" + location + ", pin=" + pin + ", password=" + password + ", deviceId=" + deviceId + ", centralLocation="+ centralLocation + ", storeRadius=" + storeRadius + "}";
	}

}
