/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_BANKS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpBanks.findAll", query = "SELECT s FROM SpBanks s"),
    @NamedQuery(name = "SpBanks.findById", query = "SELECT s FROM SpBanks s WHERE s.id = :id"),
    @NamedQuery(name = "SpBanks.findByBankName", query = "SELECT s FROM SpBanks s WHERE s.bankName = :bankName"),
    @NamedQuery(name = "SpBanks.findByBankBranch", query = "SELECT s FROM SpBanks s WHERE s.bankBranch = :bankBranch"),
    @NamedQuery(name = "SpBanks.findByAccountNumber", query = "SELECT s FROM SpBanks s WHERE s.accountNumber = :accountNumber"),
    @NamedQuery(name = "SpBanks.findByDestination", query = "SELECT s FROM SpBanks s WHERE s.destination = :destination")})
public class SpBanks implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "BANK_NAME")
    private String bankName;
    @Size(max = 255)
    @Column(name = "BANK_BRANCH")
    private String bankBranch;
    @Size(max = 255)
    @Column(name = "ACCOUNT_NUMBER")
    private String accountNumber;
    @Size(max = 255)
    @Column(name = "DESTINATION")
    private String destination;

    public SpBanks() {
    }

    public SpBanks(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpBanks)) {
            return false;
        }
        SpBanks other = (SpBanks) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpBanks[ id=" + id + " ]";
    }
    
}
