/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ACCESSCHANNEL_SUBSCRIPTIONS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAccesschannelSubscriptions.findAll", query = "SELECT s FROM SpAccesschannelSubscriptions s"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findById", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.id = :id"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findByClientId", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findByAccesschannelId", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.accesschannelId = :accesschannelId"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findByMessageCode", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.messageCode = :messageCode"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findByStatus", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.status = :status"),
    @NamedQuery(name = "SpAccesschannelSubscriptions.findByDateCreated", query = "SELECT s FROM SpAccesschannelSubscriptions s WHERE s.dateCreated = :dateCreated")})
public class SpAccesschannelSubscriptions implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Column(name = "ACCESSCHANNEL_ID")
    private BigInteger accesschannelId;
    @Size(max = 255)
    @Column(name = "MESSAGE_CODE")
    private String messageCode;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;

    public SpAccesschannelSubscriptions() {
    }

    public SpAccesschannelSubscriptions(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public BigInteger getAccesschannelId() {
        return accesschannelId;
    }

    public void setAccesschannelId(BigInteger accesschannelId) {
        this.accesschannelId = accesschannelId;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(String messageCode) {
        this.messageCode = messageCode;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAccesschannelSubscriptions)) {
            return false;
        }
        SpAccesschannelSubscriptions other = (SpAccesschannelSubscriptions) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAccesschannelSubscriptions[ id=" + id + " ]";
    }
    
}
