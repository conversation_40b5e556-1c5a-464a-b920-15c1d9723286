/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ACCOUNTS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAccounts.findAll", query = "SELECT s FROM SpAccounts s"),
    @NamedQuery(name = "SpAccounts.findById", query = "SELECT s FROM SpAccounts s WHERE s.id = :id"),
    @NamedQuery(name = "SpAccounts.findByClientId", query = "SELECT s FROM SpAccounts s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAccounts.findByAccountTypeId", query = "SELECT s FROM SpAccounts s WHERE s.accountTypeId = :accountTypeId"),
    @NamedQuery(name = "SpAccounts.findByStatus", query = "SELECT s FROM SpAccounts s WHERE s.status = :status"),
    @NamedQuery(name = "SpAccounts.findByMinBal", query = "SELECT s FROM SpAccounts s WHERE s.minBal = :minBal"),
    @NamedQuery(name = "SpAccounts.findByMaxBal", query = "SELECT s FROM SpAccounts s WHERE s.maxBal = :maxBal"),
    @NamedQuery(name = "SpAccounts.findByAvailBal", query = "SELECT s FROM SpAccounts s WHERE s.availBal = :availBal"),
    @NamedQuery(name = "SpAccounts.findByUnclearedBal", query = "SELECT s FROM SpAccounts s WHERE s.unclearedBal = :unclearedBal"),
    @NamedQuery(name = "SpAccounts.findByActualBal", query = "SELECT s FROM SpAccounts s WHERE s.actualBal = :actualBal"),
    @NamedQuery(name = "SpAccounts.findByLastUpdated", query = "SELECT s FROM SpAccounts s WHERE s.lastUpdated = :lastUpdated"),
    @NamedQuery(name = "SpAccounts.findBySendAlert", query = "SELECT s FROM SpAccounts s WHERE s.sendAlert = :sendAlert"),
    @NamedQuery(name = "SpAccounts.findByDateCreated", query = "SELECT s FROM SpAccounts s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpAccounts.findByAccountName", query = "SELECT s FROM SpAccounts s WHERE s.accountName = :accountName"),
    @NamedQuery(name = "SpAccounts.findByIntrash", query = "SELECT s FROM SpAccounts s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpAccounts.findByRunningBal", query = "SELECT s FROM SpAccounts s WHERE s.runningBal = :runningBal"),
    @NamedQuery(name = "SpAccounts.findByInconstAlert", query = "SELECT s FROM SpAccounts s WHERE s.inconstAlert = :inconstAlert"),
    @NamedQuery(name = "SpAccounts.findByThirdpartyId", query = "SELECT s FROM SpAccounts s WHERE s.thirdpartyId = :thirdpartyId"),
    @NamedQuery(name = "SpAccounts.findByAgentId", query = "SELECT s FROM SpAccounts s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpAccounts.findByAgentStoreId", query = "SELECT s FROM SpAccounts s WHERE s.agentStoreId = :agentStoreId")})
public class SpAccounts implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 20)
    @Column(name = "ACCOUNT_TYPE_ID")
    private String accountTypeId;
    @Column(name = "STATUS")
    private BigInteger status;
    @Column(name = "MIN_BAL")
    private BigDecimal minBal;
    @Column(name = "MAX_BAL")
    private BigDecimal maxBal;
    @Column(name = "AVAIL_BAL")
    private BigDecimal availBal;
    @Column(name = "UNCLEARED_BAL")
    private BigDecimal unclearedBal;
    @Column(name = "ACTUAL_BAL")
    private BigDecimal actualBal;
    @Column(name = "LAST_UPDATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUpdated;
    @Column(name = "SEND_ALERT")
    private BigInteger sendAlert;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Size(max = 255)
    @Column(name = "ACCOUNT_NAME")
    private String accountName;
    @Size(max = 10)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "RUNNING_BAL")
    private BigDecimal runningBal;
    @Column(name = "INCONST_ALERT")
    private Integer inconstAlert;
    @Column(name = "THIRDPARTY_ID")
    private BigInteger thirdpartyId;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Column(name = "AGENT_STORE_ID")
    private BigInteger agentStoreId;

    public SpAccounts() {
    }

    public SpAccounts(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getAccountTypeId() {
        return accountTypeId;
    }

    public void setAccountTypeId(String accountTypeId) {
        this.accountTypeId = accountTypeId;
    }

    public BigInteger getStatus() {
        return status;
    }

    public void setStatus(BigInteger status) {
        this.status = status;
    }

    public BigDecimal getMinBal() {
        return minBal;
    }

    public void setMinBal(BigDecimal minBal) {
        this.minBal = minBal;
    }

    public BigDecimal getMaxBal() {
        return maxBal;
    }

    public void setMaxBal(BigDecimal maxBal) {
        this.maxBal = maxBal;
    }

    public BigDecimal getAvailBal() {
        return availBal;
    }

    public void setAvailBal(BigDecimal availBal) {
        this.availBal = availBal;
    }

    public BigDecimal getUnclearedBal() {
        return unclearedBal;
    }

    public void setUnclearedBal(BigDecimal unclearedBal) {
        this.unclearedBal = unclearedBal;
    }

    public BigDecimal getActualBal() {
        return actualBal;
    }

    public void setActualBal(BigDecimal actualBal) {
        this.actualBal = actualBal;
    }

    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public BigInteger getSendAlert() {
        return sendAlert;
    }

    public void setSendAlert(BigInteger sendAlert) {
        this.sendAlert = sendAlert;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public BigDecimal getRunningBal() {
        return runningBal;
    }

    public void setRunningBal(BigDecimal runningBal) {
        this.runningBal = runningBal;
    }

    public Integer getInconstAlert() {
        return inconstAlert;
    }

    public void setInconstAlert(Integer inconstAlert) {
        this.inconstAlert = inconstAlert;
    }

    public BigInteger getThirdpartyId() {
        return thirdpartyId;
    }

    public void setThirdpartyId(BigInteger thirdpartyId) {
        this.thirdpartyId = thirdpartyId;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public BigInteger getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(BigInteger agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAccounts)) {
            return false;
        }
        SpAccounts other = (SpAccounts) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAccounts[ id=" + id + " ]";
    }
    
}
