/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_ACCOUNTING_ENTRIES")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpAccountingEntries.findAll", query = "SELECT s FROM SpAccountingEntries s"),
    @NamedQuery(name = "SpAccountingEntries.findById", query = "SELECT s FROM SpAccountingEntries s WHERE s.id = :id"),
    @NamedQuery(name = "SpAccountingEntries.findByClientId", query = "SELECT s FROM SpAccountingEntries s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpAccountingEntries.findByAccId", query = "SELECT s FROM SpAccountingEntries s WHERE s.accId = :accId"),
    @NamedQuery(name = "SpAccountingEntries.findByTrxId", query = "SELECT s FROM SpAccountingEntries s WHERE s.trxId = :trxId"),
    @NamedQuery(name = "SpAccountingEntries.findByDebit", query = "SELECT s FROM SpAccountingEntries s WHERE s.debit = :debit"),
    @NamedQuery(name = "SpAccountingEntries.findByCredit", query = "SELECT s FROM SpAccountingEntries s WHERE s.credit = :credit"),
    @NamedQuery(name = "SpAccountingEntries.findByDateTime", query = "SELECT s FROM SpAccountingEntries s WHERE s.dateTime = :dateTime"),
    @NamedQuery(name = "SpAccountingEntries.findByAvailBalance", query = "SELECT s FROM SpAccountingEntries s WHERE s.availBalance = :availBalance"),
    @NamedQuery(name = "SpAccountingEntries.findByNarration", query = "SELECT s FROM SpAccountingEntries s WHERE s.narration = :narration"),
    @NamedQuery(name = "SpAccountingEntries.findByAgentId", query = "SELECT s FROM SpAccountingEntries s WHERE s.agentId = :agentId")})
public class SpAccountingEntries implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "ACC_ID")
    private String accId;
    @Size(max = 255)
    @Column(name = "TRX_ID")
    private String trxId;
    @Column(name = "DEBIT")
    private BigInteger debit;
    @Column(name = "CREDIT")
    private BigInteger credit;
    @Column(name = "DATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateTime;
    @Column(name = "AVAIL_BALANCE")
    private BigInteger availBalance;
    @Size(max = 255)
    @Column(name = "NARRATION")
    private String narration;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;

    public SpAccountingEntries() {
    }

    public SpAccountingEntries(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getAccId() {
        return accId;
    }

    public void setAccId(String accId) {
        this.accId = accId;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public BigInteger getDebit() {
        return debit;
    }

    public void setDebit(BigInteger debit) {
        this.debit = debit;
    }

    public BigInteger getCredit() {
        return credit;
    }

    public void setCredit(BigInteger credit) {
        this.credit = credit;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public BigInteger getAvailBalance() {
        return availBalance;
    }

    public void setAvailBalance(BigInteger availBalance) {
        this.availBalance = availBalance;
    }

    public String getNarration() {
        return narration;
    }

    public void setNarration(String narration) {
        this.narration = narration;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpAccountingEntries)) {
            return false;
        }
        SpAccountingEntries other = (SpAccountingEntries) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpAccountingEntries[ id=" + id + " ]";
    }
    
}
