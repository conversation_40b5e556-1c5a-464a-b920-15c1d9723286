/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_BATCH_FILE_PROCESS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpBatchFileProcess.findAll", query = "SELECT s FROM SpBatchFileProcess s"),
    @NamedQuery(name = "SpBatchFileProcess.findByBatchFileProcId", query = "SELECT s FROM SpBatchFileProcess s WHERE s.batchFileProcId = :batchFileProcId"),
    @NamedQuery(name = "SpBatchFileProcess.findByAgentId", query = "SELECT s FROM SpBatchFileProcess s WHERE s.agentId = :agentId"),
    @NamedQuery(name = "SpBatchFileProcess.findByFileName", query = "SELECT s FROM SpBatchFileProcess s WHERE s.fileName = :fileName"),
    @NamedQuery(name = "SpBatchFileProcess.findByFileLogName", query = "SELECT s FROM SpBatchFileProcess s WHERE s.fileLogName = :fileLogName"),
    @NamedQuery(name = "SpBatchFileProcess.findByStatus", query = "SELECT s FROM SpBatchFileProcess s WHERE s.status = :status"),
    @NamedQuery(name = "SpBatchFileProcess.findByDateCreated", query = "SELECT s FROM SpBatchFileProcess s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpBatchFileProcess.findByDateProcessed", query = "SELECT s FROM SpBatchFileProcess s WHERE s.dateProcessed = :dateProcessed"),
    @NamedQuery(name = "SpBatchFileProcess.findByFileType", query = "SELECT s FROM SpBatchFileProcess s WHERE s.fileType = :fileType"),
    @NamedQuery(name = "SpBatchFileProcess.findByUserId", query = "SELECT s FROM SpBatchFileProcess s WHERE s.userId = :userId"),
    @NamedQuery(name = "SpBatchFileProcess.findByStatusDescription", query = "SELECT s FROM SpBatchFileProcess s WHERE s.statusDescription = :statusDescription"),
    @NamedQuery(name = "SpBatchFileProcess.findBySrcFolderPath", query = "SELECT s FROM SpBatchFileProcess s WHERE s.srcFolderPath = :srcFolderPath"),
    @NamedQuery(name = "SpBatchFileProcess.findByDestFolderPath", query = "SELECT s FROM SpBatchFileProcess s WHERE s.destFolderPath = :destFolderPath"),
    @NamedQuery(name = "SpBatchFileProcess.findByRecordsUploaded", query = "SELECT s FROM SpBatchFileProcess s WHERE s.recordsUploaded = :recordsUploaded"),
    @NamedQuery(name = "SpBatchFileProcess.findByUploadStatus", query = "SELECT s FROM SpBatchFileProcess s WHERE s.uploadStatus = :uploadStatus"),
    @NamedQuery(name = "SpBatchFileProcess.findByTotalRecordsInFile", query = "SELECT s FROM SpBatchFileProcess s WHERE s.totalRecordsInFile = :totalRecordsInFile"),
    @NamedQuery(name = "SpBatchFileProcess.findByRegionId", query = "SELECT s FROM SpBatchFileProcess s WHERE s.regionId = :regionId"),
    @NamedQuery(name = "SpBatchFileProcess.findByAuthenticationMode", query = "SELECT s FROM SpBatchFileProcess s WHERE s.authenticationMode = :authenticationMode"),
    @NamedQuery(name = "SpBatchFileProcess.findByProgramId", query = "SELECT s FROM SpBatchFileProcess s WHERE s.programId = :programId"),
    @NamedQuery(name = "SpBatchFileProcess.findByValidFrom", query = "SELECT s FROM SpBatchFileProcess s WHERE s.validFrom = :validFrom"),
    @NamedQuery(name = "SpBatchFileProcess.findByValidTo", query = "SELECT s FROM SpBatchFileProcess s WHERE s.validTo = :validTo"),
    @NamedQuery(name = "SpBatchFileProcess.findByApprovalStatus", query = "SELECT s FROM SpBatchFileProcess s WHERE s.approvalStatus = :approvalStatus"),
    @NamedQuery(name = "SpBatchFileProcess.findByApprovalLevel", query = "SELECT s FROM SpBatchFileProcess s WHERE s.approvalLevel = :approvalLevel"),
    @NamedQuery(name = "SpBatchFileProcess.findByMpesaShortCode", query = "SELECT s FROM SpBatchFileProcess s WHERE s.mpesaShortCode = :mpesaShortCode")})
public class SpBatchFileProcess implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "BATCH_FILE_PROC_ID")
    private BigDecimal batchFileProcId;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;
    @Size(max = 255)
    @Column(name = "FILE_NAME")
    private String fileName;
    @Size(max = 255)
    @Column(name = "FILE_LOG_NAME")
    private String fileLogName;
    @Size(max = 255)
    @Column(name = "STATUS")
    private String status;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "DATE_PROCESSED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateProcessed;
    @Size(max = 255)
    @Column(name = "FILE_TYPE")
    private String fileType;
    @Column(name = "USER_ID")
    private BigInteger userId;
    @Size(max = 255)
    @Column(name = "STATUS_DESCRIPTION")
    private String statusDescription;
    @Size(max = 255)
    @Column(name = "SRC_FOLDER_PATH")
    private String srcFolderPath;
    @Size(max = 255)
    @Column(name = "DEST_FOLDER_PATH")
    private String destFolderPath;
    @Column(name = "RECORDS_UPLOADED")
    private BigInteger recordsUploaded;
    @Column(name = "UPLOAD_STATUS")
    private BigInteger uploadStatus;
    @Column(name = "TOTAL_RECORDS_IN_FILE")
    private BigInteger totalRecordsInFile;
    @Column(name = "REGION_ID")
    private BigInteger regionId;
    @Column(name = "AUTHENTICATION_MODE")
    private BigInteger authenticationMode;
    @Column(name = "PROGRAM_ID")
    private BigInteger programId;
    @Column(name = "VALID_FROM")
    @Temporal(TemporalType.TIMESTAMP)
    private Date validFrom;
    @Column(name = "VALID_TO")
    @Temporal(TemporalType.TIMESTAMP)
    private Date validTo;
    @Size(max = 255)
    @Column(name = "APPROVAL_STATUS")
    private String approvalStatus;
    @Column(name = "APPROVAL_LEVEL")
    private BigInteger approvalLevel;
    @Size(max = 255)
    @Column(name = "MPESA_SHORT_CODE")
    private String mpesaShortCode;

    public SpBatchFileProcess() {
    }

    public SpBatchFileProcess(BigDecimal batchFileProcId) {
        this.batchFileProcId = batchFileProcId;
    }

    public BigDecimal getBatchFileProcId() {
        return batchFileProcId;
    }

    public void setBatchFileProcId(BigDecimal batchFileProcId) {
        this.batchFileProcId = batchFileProcId;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileLogName() {
        return fileLogName;
    }

    public void setFileLogName(String fileLogName) {
        this.fileLogName = fileLogName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Date getDateProcessed() {
        return dateProcessed;
    }

    public void setDateProcessed(Date dateProcessed) {
        this.dateProcessed = dateProcessed;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public BigInteger getUserId() {
        return userId;
    }

    public void setUserId(BigInteger userId) {
        this.userId = userId;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public void setStatusDescription(String statusDescription) {
        this.statusDescription = statusDescription;
    }

    public String getSrcFolderPath() {
        return srcFolderPath;
    }

    public void setSrcFolderPath(String srcFolderPath) {
        this.srcFolderPath = srcFolderPath;
    }

    public String getDestFolderPath() {
        return destFolderPath;
    }

    public void setDestFolderPath(String destFolderPath) {
        this.destFolderPath = destFolderPath;
    }

    public BigInteger getRecordsUploaded() {
        return recordsUploaded;
    }

    public void setRecordsUploaded(BigInteger recordsUploaded) {
        this.recordsUploaded = recordsUploaded;
    }

    public BigInteger getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(BigInteger uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public BigInteger getTotalRecordsInFile() {
        return totalRecordsInFile;
    }

    public void setTotalRecordsInFile(BigInteger totalRecordsInFile) {
        this.totalRecordsInFile = totalRecordsInFile;
    }

    public BigInteger getRegionId() {
        return regionId;
    }

    public void setRegionId(BigInteger regionId) {
        this.regionId = regionId;
    }

    public BigInteger getAuthenticationMode() {
        return authenticationMode;
    }

    public void setAuthenticationMode(BigInteger authenticationMode) {
        this.authenticationMode = authenticationMode;
    }

    public BigInteger getProgramId() {
        return programId;
    }

    public void setProgramId(BigInteger programId) {
        this.programId = programId;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidTo() {
        return validTo;
    }

    public void setValidTo(Date validTo) {
        this.validTo = validTo;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public BigInteger getApprovalLevel() {
        return approvalLevel;
    }

    public void setApprovalLevel(BigInteger approvalLevel) {
        this.approvalLevel = approvalLevel;
    }

    public String getMpesaShortCode() {
        return mpesaShortCode;
    }

    public void setMpesaShortCode(String mpesaShortCode) {
        this.mpesaShortCode = mpesaShortCode;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (batchFileProcId != null ? batchFileProcId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpBatchFileProcess)) {
            return false;
        }
        SpBatchFileProcess other = (SpBatchFileProcess) object;
        if ((this.batchFileProcId == null && other.batchFileProcId != null) || (this.batchFileProcId != null && !this.batchFileProcId.equals(other.batchFileProcId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpBatchFileProcess[ batchFileProcId=" + batchFileProcId + " ]";
    }
    
}
