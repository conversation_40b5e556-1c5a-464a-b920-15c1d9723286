/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_USERS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpUsers.findAll", query = "SELECT s FROM SpUsers s"),
    @NamedQuery(name = "SpUsers.findById", query = "SELECT s FROM SpUsers s WHERE s.id = :id"),
    @NamedQuery(name = "SpUsers.findByClientId", query = "SELECT s FROM SpUsers s WHERE s.clientId = :clientId"),
    @NamedQuery(name = "SpUsers.findByUsername", query = "SELECT s FROM SpUsers s WHERE s.username = :username"),
    @NamedQuery(name = "SpUsers.findByFirstName", query = "SELECT s FROM SpUsers s WHERE s.firstName = :firstName"),
    @NamedQuery(name = "SpUsers.findByMiddleName", query = "SELECT s FROM SpUsers s WHERE s.middleName = :middleName"),
    @NamedQuery(name = "SpUsers.findByLastName", query = "SELECT s FROM SpUsers s WHERE s.lastName = :lastName"),
    @NamedQuery(name = "SpUsers.findByActiveStatus", query = "SELECT s FROM SpUsers s WHERE s.activeStatus = :activeStatus"),
    @NamedQuery(name = "SpUsers.findByCreatedBy", query = "SELECT s FROM SpUsers s WHERE s.createdBy = :createdBy"),
    @NamedQuery(name = "SpUsers.findByEmail", query = "SELECT s FROM SpUsers s WHERE s.email = :email"),
    @NamedQuery(name = "SpUsers.findByMsisdn", query = "SELECT s FROM SpUsers s WHERE s.msisdn = :msisdn"),
    @NamedQuery(name = "SpUsers.findByLastLogin", query = "SELECT s FROM SpUsers s WHERE s.lastLogin = :lastLogin"),
    @NamedQuery(name = "SpUsers.findByLastPasswordChange", query = "SELECT s FROM SpUsers s WHERE s.lastPasswordChange = :lastPasswordChange"),
    @NamedQuery(name = "SpUsers.findByIntrash", query = "SELECT s FROM SpUsers s WHERE s.intrash = :intrash"),
    @NamedQuery(name = "SpUsers.findByDateCreated", query = "SELECT s FROM SpUsers s WHERE s.dateCreated = :dateCreated"),
    @NamedQuery(name = "SpUsers.findByLastEdit", query = "SELECT s FROM SpUsers s WHERE s.lastEdit = :lastEdit"),
    @NamedQuery(name = "SpUsers.findByPassword", query = "SELECT s FROM SpUsers s WHERE s.password = :password"),
    @NamedQuery(name = "SpUsers.findByAgentId", query = "SELECT s FROM SpUsers s WHERE s.agentId = :agentId")})
public class SpUsers implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Column(name = "CLIENT_ID")
    private BigInteger clientId;
    @Size(max = 255)
    @Column(name = "USERNAME")
    private String username;
    @Size(max = 255)
    @Column(name = "FIRST_NAME")
    private String firstName;
    @Size(max = 255)
    @Column(name = "MIDDLE_NAME")
    private String middleName;
    @Size(max = 255)
    @Column(name = "LAST_NAME")
    private String lastName;
    @Column(name = "ACTIVE_STATUS")
    private BigInteger activeStatus;
    @Size(max = 255)
    @Column(name = "CREATED_BY")
    private String createdBy;
    // @Pattern(regexp="[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?", message="Invalid email")//if the field contains email address consider using this annotation to enforce field validation
    @Size(max = 255)
    @Column(name = "EMAIL")
    private String email;
    @Size(max = 255)
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "LAST_LOGIN")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastLogin;
    @Column(name = "LAST_PASSWORD_CHANGE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastPasswordChange;
    @Size(max = 255)
    @Column(name = "INTRASH")
    private String intrash;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column(name = "LAST_EDIT")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastEdit;
    @Column(name = "PASSWORD")
    private String password;
    @Column(name = "AGENT_ID")
    private BigInteger agentId;

    public SpUsers() {
    }

    public SpUsers(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public BigInteger getClientId() {
        return clientId;
    }

    public void setClientId(BigInteger clientId) {
        this.clientId = clientId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public BigInteger getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(BigInteger activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public Date getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(Date lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Date getLastPasswordChange() {
        return lastPasswordChange;
    }

    public void setLastPasswordChange(Date lastPasswordChange) {
        this.lastPasswordChange = lastPasswordChange;
    }

    public String getIntrash() {
        return intrash;
    }

    public void setIntrash(String intrash) {
        this.intrash = intrash;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Date getLastEdit() {
        return lastEdit;
    }

    public void setLastEdit(Date lastEdit) {
        this.lastEdit = lastEdit;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public BigInteger getAgentId() {
        return agentId;
    }

    public void setAgentId(BigInteger agentId) {
        this.agentId = agentId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpUsers)) {
            return false;
        }
        SpUsers other = (SpUsers) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpUsers[ id=" + id + " ]";
    }
    
}
