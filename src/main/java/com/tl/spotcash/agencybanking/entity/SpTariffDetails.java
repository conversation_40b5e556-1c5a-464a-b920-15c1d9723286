/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "SP_TARIFF_DETAILS")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "SpTariffDetails.findAll", query = "SELECT s FROM SpTariffDetails s"),
    @NamedQuery(name = "SpTariffDetails.findById", query = "SELECT s FROM SpTariffDetails s WHERE s.id = :id"),
    @NamedQuery(name = "SpTariffDetails.findByTariffId", query = "SELECT s FROM SpTariffDetails s WHERE s.tariffId = :tariffId"),
    @NamedQuery(name = "SpTariffDetails.findByAmount", query = "SELECT s FROM SpTariffDetails s WHERE s.amount = :amount"),
    @NamedQuery(name = "SpTariffDetails.findBySpotcashComission", query = "SELECT s FROM SpTariffDetails s WHERE s.spotcashComission = :spotcashComission"),
    @NamedQuery(name = "SpTariffDetails.findByServiceCharge", query = "SELECT s FROM SpTariffDetails s WHERE s.serviceCharge = :serviceCharge"),
    @NamedQuery(name = "SpTariffDetails.findByDateCreated", query = "SELECT s FROM SpTariffDetails s WHERE s.dateCreated = :dateCreated")})
public class SpTariffDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private BigDecimal id;
    @Size(max = 255)
    @Column(name = "TARIFF_ID")
    private String tariffId;
    @Column(name = "AMOUNT")
    private BigDecimal amount;
    @Column(name = "SPOTCASH_COMISSION")
    private BigDecimal spotcashComission;
    @Column(name = "SERVICE_CHARGE")
    private BigDecimal serviceCharge;
    @Column(name = "DATE_CREATED")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dateCreated;
    @Column (name = "SACCO_COMMISSION")
    private BigDecimal saccoCommission;

    public SpTariffDetails() {
    }

    public SpTariffDetails(BigDecimal id) {
        this.id = id;
    }

    public BigDecimal getId() {
        return id;
    }

    public void setId(BigDecimal id) {
        this.id = id;
    }

    public String getTariffId() {
        return tariffId;
    }

    public void setTariffId(String tariffId) {
        this.tariffId = tariffId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSpotcashComission() {
        return spotcashComission;
    }

    public void setSpotcashComission(BigDecimal spotcashComission) {
        this.spotcashComission = spotcashComission;
    }

    public BigDecimal getServiceCharge() {
        return serviceCharge;
    }

    public void setServiceCharge(BigDecimal serviceCharge) {
        this.serviceCharge = serviceCharge;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public BigDecimal getSaccoCommission() {
        return saccoCommission;
    }

    public void setSaccoCommission(BigDecimal saccoCommission) {
        this.saccoCommission = saccoCommission;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SpTariffDetails)) {
            return false;
        }
        SpTariffDetails other = (SpTariffDetails) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.tl.spotcash.agencybanking.entity.SpTariffDetails[ id=" + id + " ]";
    }
    
}
