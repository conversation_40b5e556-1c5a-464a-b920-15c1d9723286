package com.tl.spotcash.agencybanking.entity;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "SP_AGENCY_CONFIG_MESSAGES")
public class SpAgencyConfigMessages {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private BigInteger id;

    @Basic
    @Column(name = "CLIENT_ID")
    private Long clientId;

    @Basic
    @Column(name = "DATE_CREATED")
    private Timestamp dateCreated;

    @Lob
    @Column(name = "CONFIG_MESSAGES")
    private String configMessages;

    @Basic
    @Column(name = "IN_TRASH")
    private String inTrash;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getConfigMessages() {
        return configMessages;
    }

    public void setConfigMessages(String configMessages) {
        this.configMessages = configMessages;
    }

    public String getInTrash() {
        return inTrash;
    }

    public void setInTrash(String inTrash) {
        this.inTrash = inTrash;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SpAgencyConfigMessages)) return false;
        SpAgencyConfigMessages that = (SpAgencyConfigMessages) o;
        return getId().equals(that.getId()) && getClientId().equals(that.getClientId()) && Objects.equals(getDateCreated(), that.getDateCreated()) && Objects.equals(getConfigMessages(), that.getConfigMessages()) && Objects.equals(getInTrash(), that.getInTrash());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getClientId(), getDateCreated(), getConfigMessages(), getInTrash());
    }
}