package com.tl.spotcash.agencybanking;

import bsh.StringUtil;
import com.tl.spotcash.agencybanking.adapter.CorebankingSystemInvoker;
import com.tl.spotcash.agencybanking.adapter.CorebankingSystemInvokerImpl;
import com.tl.spotcash.agencybanking.adapter.ZposIntegratorInvoker;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.*;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposIntegratorResponse;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposRequestData;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.exceptions.OzoneAuthenticationException;
import com.tl.spotcash.agencybanking.service.ReserveFundsConfiguration;
import com.tl.spotcash.agencybanking.service.SpotcashserviceMap;
import com.tl.spotcash.agencybanking.utils.AesEncryption;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import com.tl.spotcash.agencybanking.xiputils.SpotcashTrxDetails;
import com.tl.spotcash.agencybanking.xiputils.ZposHttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.util.*;

import static com.tl.spotcash.agencybanking.utils.Keys.keys.INVALID_CUST_ID;
import static com.tl.spotcash.agencybanking.utils.Keys.keys.TELPO_AGENT;

public class ApiRequestprocessorEncrypted implements CorebankingSystemInvoker, ZposIntegratorInvoker {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiRequestprocessorEncrypted.class);
    private final String requestData;
    private final CrudTransactionController crudTransactions;
    private final Environment environment;
    private Boolean cbsAuth = false;
    private ReserveFundsConfiguration reserveFundsConfiguration;

    public ApiRequestprocessorEncrypted(CrudTransactionController crudTransactions, String requestData, Environment environment,ReserveFundsConfiguration reserveFundsConfiguration) {
        this.requestData = requestData;
        this.crudTransactions = crudTransactions;
        this.environment = environment;
        this.reserveFundsConfiguration = reserveFundsConfiguration;
    }

    /**
     * Authentication entry point for all transactions . Transaction type
     * switcher, checks the transaction type in requestData and uses it to get
     * the method to invoke in SpotcashserviceMap.class via reflection. Check
     * SpotcashserviceMap.class on the methods what they return.
     *
     * @param requestData
     * @return
     */
    public PosResponseMapper transactionTypewatcher(HttpProcessorRequest requestData) {
        final String secretKey = AesEncryption.encodeKey(environment.getRequiredProperty("secret.key"));
        if (requestData.getRequestData().getAccNo() != null) {
            requestData.getRequestData().setAccNo(AesEncryption.decrypt(requestData.getRequestData().getAccNo().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getCustPin() != null) {
            requestData.getRequestData().setCustPin(AesEncryption.decrypt(requestData.getRequestData().getCustPin().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getAgentPin() != null) {
            requestData.getRequestData().setAgentPin(AesEncryption.decrypt(requestData.getRequestData().getAgentPin().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getCustid() != null) {
            requestData.getRequestData().setCustid(AesEncryption.decrypt(requestData.getRequestData().getCustid().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getPassword() != null) {
            requestData.getRequestData().setPassword(AesEncryption.decrypt(requestData.getRequestData().getPassword().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getXid() != null) {
            requestData.getRequestData().setXid(AesEncryption.decrypt(requestData.getRequestData().getXid().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getAgentMsisdn() != null) {
            requestData.getRequestData().setAgentMsisdn(AesEncryption.decrypt(requestData.getRequestData().getAgentMsisdn().replaceAll("\n",""), secretKey));
        }
        if (requestData.getHeader().getMsisdn() != null) {
            requestData.getHeader().setMsisdn(AesEncryption.decrypt(requestData.getHeader().getMsisdn().replaceAll("\n",""), secretKey));
        }
        if (requestData.getHeader().getAccountNumber() != null) {
            requestData.getHeader().setAccountNumber(AesEncryption.decrypt(requestData.getHeader().getAccountNumber().replaceAll("\n",""), secretKey));
        }
        if (requestData.getHeader().getUserXid() != null) {
            requestData.getHeader().setUserXid(AesEncryption.decrypt(requestData.getHeader().getUserXid().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getDescription() != null) {
            requestData.getRequestData().setDescription(AesEncryption.decrypt(requestData.getRequestData().getDescription().replaceAll("\n",""), secretKey));
        }
        if (requestData.getRequestData().getAgentPhoneNumber() != null) {
            requestData.getRequestData().setAgentPhoneNumber(AesEncryption.decrypt(requestData.getRequestData().getAgentPhoneNumber().replaceAll("\n",""), secretKey));
        }
        SpAgentStores agentStoreData = crudTransactions.fetchAgentStoreData(requestData.getHeader().getUserXid(), "254" + requestData.getHeader().getMsisdn());
        if (agentStoreData == null) {
            return new JsonResponseConstructor()
                    .posResponse(new ApiTransactionsResponseBuilder()
                            .missingTransactionDetails("Agent information not found."));
        }
        try {
            boolean isAuth = authenticateAgent_Customer(requestData, agentStoreData);
            if (!isAuth) {
                LOGGER.info("AGENT AND CUSTOMER AUTHENTICATION FAILED");
                return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedAuthResponse());
            } else {
                LOGGER.info("AGENT AND CUSTOMER AUTHENTICATED SUCCESSFULLY");
                PosResponseMapper response = null;
                SpCustomers customerDetail = null;
                CorebankingResponse transresponse;
                SpAccounts agentFloat;
                Class serviceMappingsClass = SpotcashserviceMap.class;
                Object serviceObj = serviceMappingsClass.newInstance();
                String xipService = requestData.getRequestData().getTxnType();//SABE,SDT,SPS
                Method serviceMethod = serviceMappingsClass.getMethod("get" + xipService + "");
                String xip_service_id = (String) serviceMethod.invoke(serviceObj);
                SpotcashTrxDetails transactionDataObj = new SpotcashTrxDetails();
                SpAgents spAgent = null;

                Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(requestData.getHeader().getClientId())));
                if (optionalAgent.isPresent()) {
                    spAgent = optionalAgent.get();
                    LOGGER.info("AGENT AUTH: " + spAgent.getCbsAuth());
                    int resp = spAgent.getCbsAuth().compareTo(new BigInteger("1"));
                    if (resp == 0) {
                        cbsAuth = true;
                    }
                    LOGGER.info("AGENT AUTH BOOL: " + cbsAuth);
                } else {
                    LOGGER.info("AGENT NOT FOUND PASSED ");
                }

                LOGGER.info("SERVICE PASSED " + xip_service_id.toUpperCase());
                switch (xip_service_id.toUpperCase().trim()) {
                    case "SLOG":
                        Map<String, Object> slogresponse = retrieveTransactions(requestData.getRequestData().getXid(), requestData.getHeader().getMsisdn(), requestData.getHeader().getMsisdn(), "SLOG", requestData.getRequestData().getTrxId(), agentStoreData);
                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().process_SLOG(requestData, slogresponse));
                        break;
                    case "SLOGP":
                        Map<String, Object> trxresponse = retrieveTransactions(requestData.getRequestData().getXid(), requestData.getHeader().getMsisdn(), requestData.getHeader().getMsisdn(), "SLOGP", requestData.getRequestData().getTrxId(), agentStoreData);
                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().process_SLOG(requestData, trxresponse));
                        break;
                    case "SABE":
                        BigDecimal agentStorefloat = crudTransactions.agentFloat(agentStoreData.getAgentId().toString(), agentStoreData.getAgentStoreId().toString()).getAvailBal();
                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().process_SABE(requestData, agentStorefloat));
                        break;
                    case "SV":
                        List<SpAirtimeVouchers> thevouchers = crudTransactions.getVoucherbyDenomination(requestData.getRequestData().getAmount(), agentStoreData.getAgentId().toString(), agentStoreData.getAgentStoreId().toString());
                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().airtimeVoucher(thevouchers));
                        break;
                    case "AGBAL"://Agent Balance
                        LOGGER.info("...........IT'S AGENT BALANCE TRANSACTION...........");
                        try {
                            transactionDataObj.setAccountNumber(requestData.getRequestData().getAccNo());
                            transactionDataObj.setAmount("00");
                            transactionDataObj.setTrnx_charges("0");
                            transactionDataObj.setAgentPhoneNumber(requestData.getRequestData().getAgentPhoneNumber());
                            transactionDataObj.setCustomer_msisdn(requestData.getRequestData().getAgentMsisdn());
                            transactionDataObj.setCustomer_name("");
                            transactionDataObj.setSpotcash_agent_id(agentStoreData.getAgentId());
                            transactionDataObj.setSpotcash_agent_store_id(agentStoreData.getAgentStoreId());
                            transactionDataObj.setSpotcash_service_id("59");
                            transactionDataObj.setClient_id(requestData.getHeader().getClientId());

                            LOGGER.info("TRANSACTION OBJECT " + transactionDataObj.toString());
                        } catch (Exception e) {
                            LOGGER.info("ERROR OCCURRED DURING AGENT BALANCE ENQUIRY " + e);
                            e.printStackTrace();
                        }

                        CorebankingResponse cbsinvoke = invokecorebankingsystem(crudTransactions, constructCbsRequestData(transactionDataObj, spAgent, agentStoreData, null, null, null, requestData));

                        //						response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().process_AgentBalanceResponse(requestData, cbsinvoke.getCorebankingResponseMessage()));
                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().AgentBalanceResponse(requestData, cbsinvoke.getCorebankingResponseMessage(), cbsinvoke.getSpotcash_agent_id(),environment));
                        //LOGGER.info("..........AGENT BALANCE RESPONSE SHOULD BE HERE........");
                        break;
                    case "UTIL":
                        String utilityCode = getUtilityKeywordCode(requestData.getRequestData().getUtilityKeyword());
                        if (!cbsAuth) {
                            customerDetail = crudTransactions.fetchCustomerData("spCustId", requestData.getRequestData().getCustid());
                            transactionDataObj.setCustomer_msisdn(customerDetail.getMsisdn());
                            transactionDataObj.setClient_id(customerDetail.getClientId().toString());
                        } else {
                            transactionDataObj.setCustomer_msisdn(requestData.getHeader().getCustomerMsisdn());
                            transactionDataObj.setClient_id(requestData.getHeader().getClientId());
                        }
                        transactionDataObj.setAccountNumber(requestData.getHeader().getAccountNumber());
                        transactionDataObj.setAmount(requestData.getRequestData().getAmount());
                        transactionDataObj.setUtilityAccountNumber(requestData.getRequestData().getUtilityAccNumber());
                        transactionDataObj.setUtilityKeyword(requestData.getRequestData().getUtilityKeyword());
                        transactionDataObj.setSpotcash_service_id(utilityCode);

                        LOGGER.info("TRANSACTION OBJECT " + transactionDataObj);

                        agentFloat = crudTransactions.agentFloat(agentStoreData.getAgentId().toString(), agentStoreData.getAgentStoreId().toString());
                        if (!new SpotcashUtilities().isAgentBalanceEnough(new BigDecimal(requestData.getRequestData().getAmount()), agentFloat.getAvailBal())) {
                            return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedUtilityPayment("Agent account balance insufficient."));
                        }
                        ZposIntegratorResponse zposIntegratorResponse;
                        try {
                            transresponse = createTransTempPayment(transactionDataObj, spAgent, agentStoreData, requestData);
                            if (transresponse != null) {
                                ZposRequestData zposRequestData = new ZposRequestData();
                                zposRequestData.setAccountNumber(transactionDataObj.getUtilityAccountNumber());
                                zposRequestData.setAmount(transactionDataObj.getAmount());
                                zposRequestData.setCustomerMSISDN(transactionDataObj.getCustomer_msisdn());
                                zposRequestData.setTransactionId(transactionDataObj.getTransactionId());
                                zposRequestData.setUtility(transactionDataObj.getUtilityKeyword());
                                zposIntegratorResponse = zposIntegratorResponse(crudTransactions, zposRequestData);

                                if (Integer.parseInt(zposIntegratorResponse.getStatus()) == 0) {
                                    response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().successfulUtilityPayment(zposIntegratorResponse));

                                } else {
                                    response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedUtilityPayment(zposIntegratorResponse.getMessage()));
                                    // TO Do Reverse transaction.
                                }
                            } else {
                                response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedUtilityPayment("No response received from CBS"));
                            }
                        } catch (Exception e) {
                            if (e instanceof OzoneAuthenticationException) {
                                return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedPaymentAuthentication());
                            }
                            response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedUtilityPayment(e.getMessage()));
                        }
                        break;
                    case "60"://NON MEMBER DEPOSIT REQUEST
                    default:
                        //All Numeric transactions based on spotcash service IDs
                        //See Spotcash DB for reference on service ID definitions.
                        transactionDataObj.setDescription(requestData.getRequestData().getDescription());
                        if ("00".equals(requestData.getRequestData().getCustid())) {
                            //Fail the transaction immediately and return a response;
                            if (requestData.getHeader().getCategoryName().contains(TELPO_AGENT)) {
                                LOGGER.info("CONTAINS TELPO IN THE CATEGORY NAME");
                                return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transactionFailureResponseWithReason(INVALID_CUST_ID));
                            } else {
                                LOGGER.info("DOES NOT CONTAINS TELPO IN THE CATEGORY NAME");
                                return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transactionFailureResponse());
                            }
                        } else {
                            LOGGER.info("CUSTOMER DATA RETRIEVED >>>>" + requestData.getRequestData().getCustid());
                            if (!cbsAuth)
                                customerDetail = crudTransactions.fetchCustomerData("spCustId", requestData.getRequestData().getCustid());
                            //LOGGER.info("CUSTOMER DATA RETRIEVED >>>>" + customerDetail.toString());
                            if (requestData.getHeader().getCategoryName().contains(TELPO_AGENT)) {
                                LOGGER.info("SET ACCOUNT NUMBER FOR TELPO TRANSACTION");
                                transactionDataObj.setAccountNumber(requestData.getRequestData().getAccNo());
                            } else {
                                LOGGER.info("SET ACCOUNT NUMBER FOR XIPOS TRANSACTION");
                                if (!cbsAuth)
                                    transactionDataObj.setAccountNumber(customerDetail.getAccountNumber());
                            }

                            if (null != requestData.getRequestData().getAmount()) {
                                transactionDataObj.setAmount(requestData.getRequestData().getAmount());
                            } else {
                                transactionDataObj.setAmount("00");
                            }

                            transactionDataObj.setTrnx_charges("0");//hard coded.. to consult
                            if (!cbsAuth) {
                                if (customerDetail != null) {
                                    transactionDataObj.setCustomer_msisdn(customerDetail.getMsisdn());
                                    transactionDataObj.setCustomer_name(customerDetail.getCustomerName());
                                    transactionDataObj.setClient_id(customerDetail.getClientId().toString());
                                } else {
                                    transactionDataObj.setClient_id(requestData.getHeader().getClientId());
                                    transactionDataObj.setCustomer_msisdn(requestData.getHeader().getCustomerMsisdn());
                                    transactionDataObj.setCustomer_name(requestData.getRequestData().getCustid());
                                }
                            } else {
                                transactionDataObj.setCustomer_msisdn(requestData.getHeader().getCustomerMsisdn());
                                transactionDataObj.setCustomer_name(requestData.getRequestData().getCustid());
                                transactionDataObj.setClient_id(requestData.getHeader().getClientId());
                            }
                            transactionDataObj.setSpotcash_agent_id(agentStoreData.getAgentId());
                            transactionDataObj.setSpotcash_agent_store_id(agentStoreData.getAgentStoreId());
                            transactionDataObj.setSpotcash_service_id(xip_service_id);
                            transactionDataObj.setCustomerType(requestData.getRequestData().getCustomerType());

                            if (SpotcashTransactionService.CLIENT_DEPOSIT.getServiceId().equals(xip_service_id.toUpperCase() + "67".trim())) {
                                agentFloat = crudTransactions.agentFloat(agentStoreData.getAgentId().toString(), agentStoreData.getAgentStoreId().toString());
                                if (new SpotcashUtilities().isAgentBalanceEnough(new BigDecimal(requestData.getRequestData().getAmount()), agentFloat.getAvailBal()) == false) {
                                    return new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().failedAgentBalance());
                                } else {
                                    try {
                                        transresponse = createTransTempPayment(transactionDataObj, spAgent, agentStoreData, requestData);
                                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transTempPaymentResponse(transresponse, requestData.getHeader().getMsisdn()));
                                    } catch (Exception ex) {
                                        LOGGER.error("Error processing transaction # Trans temp payment " + ex.toString());
                                        response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transactionFailureResponse());
                                    }
                                }
                            } else {
                                try {
                                    transresponse = createTransTempPayment(transactionDataObj, spAgent, agentStoreData, requestData);
                                    response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transTempPaymentResponse(transresponse, requestData.getHeader().getMsisdn()));
                                } catch (Exception ex) {
                                    LOGGER.error("Error processing transaction # Trans temp payment " + ex.toString());
                                    ex.printStackTrace();
                                    response = new JsonResponseConstructor().posResponse(new ApiTransactionsResponseBuilder().transactionFailureResponse());
                                }
                            }
                        }
                        break;
                }
                return response;
            }
        } catch (Exception e) {
            LOGGER.error("ERROR OCCURRED WHILE PROCESSING REQUEST {}", e.getMessage(), e);
            e.printStackTrace();
            return new JsonResponseConstructor()
                    .posResponse(new ApiTransactionsResponseBuilder()
                            .missingTransactionDetails("Error Occurred While Processing Request."));
        }
    }

    /**
     * @param transactionDataObj
     * @param agentStoreData
     * @return
     */
    public CorebankingResponse createTransTempPayment(SpotcashTrxDetails transactionDataObj, SpAgents spAgent, SpAgentStores agentStoreData, HttpProcessorRequest requestData) {
        Calendar currenttime = Calendar.getInstance();
        Date sqldate = new Date((currenttime.getTime()).getTime());
        BigDecimal return_id = null;
        CorebankingResponse response = null;
        BigDecimal commission;
        BigDecimal serviceCharge;
        SpTransTempTable tranTempObj = new SpTransTempTable();
        LOGGER.info("GETTING COMMISSION SERVICE ID## " + transactionDataObj.getSpotcash_service_id());
        LOGGER.info("GETTING COMMISSION CLIENT ID ## " + transactionDataObj.getClient_id());
        LOGGER.info("GETTING COMMISSION ## AMOUNT " + transactionDataObj.getAmount());
        try {
            Map<String, BigDecimal> comissionChargeData = getAgencyCommissionServiceChargeData(new BigInteger(transactionDataObj.getSpotcash_service_id()), new BigInteger(transactionDataObj.getClient_id()), new BigDecimal(transactionDataObj.getAmount()));
            LOGGER.info("COMMISSION MAP RETURNED ## " + comissionChargeData);
            if (comissionChargeData != null) {
                if (comissionChargeData.size() > 0) {
                    if (comissionChargeData != null) { //Check using service id type
                        commission = comissionChargeData.get("commission");
                        serviceCharge = comissionChargeData.get("serviceCharge");
                    } else { //Non commissionable transaction
                        commission = BigDecimal.ZERO;
                        serviceCharge = BigDecimal.ZERO;
                    }
                } else {
                    commission = BigDecimal.ZERO;
                    serviceCharge = BigDecimal.ZERO;
                }
            } else {
                commission = BigDecimal.ZERO;
                serviceCharge = BigDecimal.ZERO;
            }
            LOGGER.info("commission . " + commission);
            LOGGER.info("serviceCharge . " + serviceCharge);
            String instransactionId = getupdateTransactionId();

            LOGGER.info("Create the record in the SpLocationLog for Geo Mapping");
            try {
                SpLocationLog spLocationLog = new SpLocationLog();
                spLocationLog.setTrxId(instransactionId);
                spLocationLog.setRequestTime(sqldate);
                spLocationLog.setClientId(new BigInteger(transactionDataObj.getClient_id()));
                spLocationLog.setLocation("Nairobi");
                spLocationLog.setAgentId(new BigDecimal(transactionDataObj.getSpotcash_agent_id()));
                spLocationLog.setAgentStoreId(transactionDataObj.getSpotcash_agent_store_id());
                spLocationLog.setStoreUserId(new BigInteger("803"));
                spLocationLog.setIpAddress("test.************");
                spLocationLog.setTransactionType(transactionDataObj.getSpotcash_service_id());
                crudTransactions.updateLocationLog(spLocationLog);
            }catch (Exception ex){
                LOGGER.error("Error saving Location record: {}", ex.getLocalizedMessage());
            }
            transactionDataObj.setTransactionId(instransactionId);
            LOGGER.info("TRANSACTION SEQUENCE GENERATED " + instransactionId);
            if (SpotcashTransactionService.CLIENT_AIRTIME.getServiceId().equals(transactionDataObj.getSpotcash_service_id())
                    && (!transactionDataObj.getDescription().equals(""))) {
                tranTempObj.setMsisdn(transactionDataObj.getDescription());
            } else {
                tranTempObj.setMsisdn(transactionDataObj.getCustomer_msisdn());
            }
            tranTempObj.setAmount(new BigDecimal(transactionDataObj.getAmount()));
            tranTempObj.setClientId(new BigInteger(transactionDataObj.getClient_id()));
            tranTempObj.setServiceId(new BigInteger(transactionDataObj.getSpotcash_service_id()));
            tranTempObj.setCustomerName(transactionDataObj.getCustomer_name());
            tranTempObj.setAgentId(transactionDataObj.getSpotcash_agent_id());
            tranTempObj.setAgentStoreId(transactionDataObj.getSpotcash_agent_store_id());
            tranTempObj.setAccesschannelId(BigInteger.valueOf(5));
            tranTempObj.setProcessorFailCount(0);
            tranTempObj.setFailCount(BigInteger.ZERO);
            tranTempObj.setIntermediateStatus(BigInteger.ZERO);
            tranTempObj.setTrxId(instransactionId);
            tranTempObj.setRequestTime(sqldate);
            LOGGER.info(" The organization client ID IS ### " + transactionDataObj.getClient_id());
            String client_type = crudTransactions.fetchclient_type(new BigDecimal(transactionDataObj.getClient_id())).getClientType();
            if ("VPN".equals(client_type)) {
                tranTempObj.setTrxStatus(BigInteger.ONE);
            } else {
                tranTempObj.setTrxStatus(BigInteger.ZERO);
            }
            tranTempObj.setSpotcashCommission(commission);
            tranTempObj.setThirdpartyCharge(serviceCharge);
            tranTempObj.setAccountNumber(transactionDataObj.getAccountNumber());
            CbsRequestData constructCbsRequestData = constructCbsRequestData(transactionDataObj, spAgent, agentStoreData, return_id, instransactionId, serviceCharge, requestData);
            tranTempObj.setAgentPhoneNumber(constructCbsRequestData.getAgentPhoneNumber());

            LOGGER.info("ABOUT TO INSERT TRANS TEMP PAYMENT OBJECT #### ");
            return_id = this.crudTransactions.insertTransTmpTable(tranTempObj);
            LOGGER.info("[TRANSACTION PROCESSORS.CREATE TRANS TEMP PAYMENT] >> " + return_id);

            if (ClientConnectionTypes.DIRECT_CONNECTION.getconnectionType().equals(client_type) & return_id.compareTo(BigDecimal.ZERO) > 0) {
                constructCbsRequestData.setTranstemptableid(return_id.toString());
                response = invokecorebankingsystem(crudTransactions, constructCbsRequestData);
                LOGGER.info("CBS RESPONSE " + response);
            } else {
                LOGGER.info("CLIENT IS NOT VPN CONFIGURED, FIX THAT FIRST, EXITING .... ");
            }
            return response;
        } catch (Exception e) {
            LOGGER.error("PROCESS REQUEST EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return response;
    }

    private CbsRequestData constructCbsRequestData(SpotcashTrxDetails transactionDataObj, SpAgents spAgent, SpAgentStores agentStoreData, BigDecimal return_id, String instransactionId, BigDecimal serviceCharge, HttpProcessorRequest requestData) {
        String msisdn = agentStoreData.getContactMsisdn();
        CbsRequestData cbsRequestObj = new CbsRequestData();
        try {
            cbsRequestObj.setMsisdn(transactionDataObj.getCustomer_msisdn());
            cbsRequestObj.setTrnx_charges(transactionDataObj.getTrnx_charges());
            cbsRequestObj.setAmount(transactionDataObj.getAmount());
            cbsRequestObj.setDescription(transactionDataObj.getDescription());
            cbsRequestObj.setClientId(transactionDataObj.getClient_id());
            cbsRequestObj.setTransactionId(instransactionId != null ? instransactionId : "");
            cbsRequestObj.setService_charge(serviceCharge != null ? serviceCharge.toString() : "0");
            cbsRequestObj.setServiceId(transactionDataObj.getSpotcash_service_id());
            cbsRequestObj.setCustomer_name(transactionDataObj.getCustomer_name() != null ? transactionDataObj.getCustomer_name() : "");
            cbsRequestObj.setTranstemptableid(return_id != null ? return_id.toString() : "");
            cbsRequestObj.setAccount_number(transactionDataObj.getAccountNumber());
            cbsRequestObj.setAccountName(requestData.getRequestData().getAccName());
            cbsRequestObj.setAgent_store_code(agentStoreData.getAgentStoreCode());
            if (agentStoreData.getBranch_code().equals("0") || agentStoreData.getBranch_code().equals("null")) {
                cbsRequestObj.setCustomerType(transactionDataObj.getCustomerType());
            } else {
                cbsRequestObj.setCustomerType(agentStoreData.getBranch_code());
            }
            if (requestData.getHeader().getUserId() != null) {
                if (Integer.parseInt(requestData.getHeader().getUserId()) > 0) {
                    cbsRequestObj.setUserId(requestData.getHeader().getUserId());
                    Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchStoreUserWithId(new BigInteger(requestData.getHeader().getUserId()), requestData.getHeader().getUserXid()));
                    if (optionalStoreUser.isPresent()) {
                        SpStoreUsers spStoreUser = optionalStoreUser.get();
                        msisdn = spStoreUser.getContactMsisdn();
                    }
                }
            }
            cbsRequestObj.setAgent_msisdn(msisdn);
            cbsRequestObj.setAgentPhoneNumber(msisdn);
            cbsRequestObj.setNewPrintDesign(spAgent.getNewPrintDesign().toString());
            cbsRequestObj.setWithdrawal_sms(spAgent.getEnable_withdrawals_sms().toString());
            return cbsRequestObj;
        } catch (Exception e) {
            LOGGER.error("CONSTRUCT CBS REQUEST OBJECT EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return cbsRequestObj;
    }

    /**
     * @param amount
     * @param spotcashCommission
     * @param spotcashCharges
     * @param client_id
     * @param serviceId
     * @param agent_store_id
     */
    protected void updateUnclearedBalance(BigDecimal amount, BigDecimal spotcashCommission, BigDecimal spotcashCharges, BigInteger client_id, BigInteger serviceId, BigInteger agent_store_id) {
        List<ChargeableAccounts> chargeableAccountsList = this.crudTransactions.chargeableAccountsList(serviceId);
        String sqlQueryDebitAmount = null;
        String sqlQueryCreditAmount = null;
        String sqlQueryDebitCommission = null;
        String sqlQueryCreditCommission = null;
        String sqlQueryCreditCharges = null;
        String sqlQueryDebitCharges = null;

        if (chargeableAccountsList.size() > 0) {
            for (ChargeableAccounts chargeableAccounts : chargeableAccountsList) {
                chargeableAccounts.setSERVICEID(serviceId.intValue());
                chargeableAccounts.setAGENTSTOREID(agent_store_id.intValue());
                chargeableAccounts.setCLIENTID(client_id.intValue());
                chargeableAccounts.setCRPARTY(chargeableAccounts.getCRPARTY());
                Object cr_party_acc_id = crudTransactions.returnSpotcashAccount_id(chargeableAccounts);

                chargeableAccounts.setTRXPARTY(chargeableAccounts.getDRPARTY());
                Object dr_party_acc_id = crudTransactions.returnSpotcashAccount_id(chargeableAccounts);
                switch ((chargeableAccounts.getTRANSACTIONFIELD()).toUpperCase()) {
                    case "AMOUNT":
                        if (chargeableAccounts.getISAMOUNTCHARGEABLE().compareTo(BigDecimal.ONE) == 0) {
                            //Apply debits
                            sqlQueryDebitAmount = "update sp_accounts set avail_bal = (avail_bal - " + amount + "), uncleared_bal = (uncleared_bal - " + amount + "), LAST_UPDATED=systimestamp where id = '" + dr_party_acc_id + "'";
                            //Apply credits
                            sqlQueryCreditAmount = "update sp_accounts set uncleared_bal = (uncleared_bal + " + amount + "),actual_bal = (actual_bal + " + amount + "), LAST_UPDATED=systimestamp where id = '" + cr_party_acc_id + "'";
                        }
                        break;
                    case "COMMISSION":
                        //Apply debits
                        sqlQueryDebitCommission = "update spGETTING COMMISSION SERVICE_accounts set avail_bal = (avail_bal - " + spotcashCommission + "), uncleared_bal = (uncleared_bal - " + spotcashCommission + "), LAST_UPDATED=systimestamp where id = '" + dr_party_acc_id + "'";
                        //Apply credits
                        sqlQueryCreditCommission = "update sp_accounts set uncleared_bal = (uncleared_bal + " + spotcashCommission + "),actual_bal = (actual_bal + " + spotcashCommission + "), LAST_UPDATED=systimestamp where id = '" + cr_party_acc_id + "'";
                        break;
                    case "CHARGE":
                        //Apply debits
                        sqlQueryDebitCharges = "update sp_accounts set avail_bal = (avail_bal - " + spotcashCharges + "), uncleared_bal = (uncleared_bal - " + spotcashCharges + "), LAST_UPDATED=systimestamp where id = '" + dr_party_acc_id + "'";
                        //Apply credits
                        sqlQueryCreditCharges = "update sp_accounts set uncleared_bal = (uncleared_bal + " + spotcashCharges + "),actual_bal = (actual_bal + " + spotcashCharges + "), LAST_UPDATED=systimestamp where id = '" + cr_party_acc_id + "'";
                        break;
                }
            }
            crudTransactions.updateAccounts(sqlQueryDebitAmount, sqlQueryCreditAmount, sqlQueryDebitCommission, sqlQueryCreditCommission, sqlQueryDebitCharges, sqlQueryCreditCharges);
        }
    }

    /**
     * @param service_id
     * @param client_id
     * @param amount
     * @return
     */
    public Map<String, BigDecimal> getComissionServiceChargeData(BigInteger service_id, BigInteger client_id, BigDecimal amount) {
        LOGGER.info("Get commission service data:");
        SpServiceSubscriptions service_commissionData = crudTransactions.fetchServicesubscriptionData(service_id, client_id);
        LOGGER.info("Get commission service data: 2");
        if (service_commissionData != null) {
            List<SpTariffDetails> tariffDetail = crudTransactions.fetchTariffsDetails(service_commissionData.getTariffId(), amount);
            Map<String, BigDecimal> tariffDetailsData = new HashMap<>();
            for (SpTariffDetails spTransData : tariffDetail) {
                tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
            }
            return tariffDetailsData;
        } else {
            LOGGER.info(" TRANSACTION PROCESSORS No tariff details information for service ID >> " + service_id + " & Client ID " + client_id);
            return null;
        }
    }

    /**
     * @param service_id
     * @param client_id
     * @param amount
     * @return
     */
    public Map<String, BigDecimal> getAgencyCommissionServiceChargeData(BigInteger service_id, BigInteger client_id, BigDecimal amount) {
        LOGGER.info("Get commission service data:");
        try {
            SpServiceSubscriptions service_commissionData = crudTransactions.fetchServicesubscriptionData(service_id, client_id);
            if (service_commissionData != null) {
                SpAgencyTariff agencyTariffData = crudTransactions.fetchAgencyTariffData(service_id, client_id);
                Map<String, BigDecimal> tariffDetailsData = new HashMap<>();
                if (agencyTariffData != null) {
                    List<SpAgencyTariffDetails> tariffDetail = crudTransactions.fetchAgencyTariffsDetails(agencyTariffData.getId().toString(), amount, agencyTariffData.getType());
                    if (tariffDetail != null) {
                        if (tariffDetail.size() > 0) {
                            for (SpAgencyTariffDetails spTransData : tariffDetail) {
                                tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                                tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
                            }
                        } else {
                            tariffDetailsData.put("commission", new BigDecimal("0"));
                            tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
                        }
                    }
                    return tariffDetailsData;
                }
                List<SpTariffDetails> tariffDetail = crudTransactions.fetchTariffsDetails(service_commissionData.getTariffId(), amount);
                if (tariffDetail != null) {
                    if (tariffDetail.size() > 0) {
                        for (SpTariffDetails spTransData : tariffDetail) {
                            tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                            tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
                        }
                    } else {
                        tariffDetailsData.put("commission", new BigDecimal("0"));
                        tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
                    }
                }
                return tariffDetailsData;
            } else {
                LOGGER.info(" TRANSACTION PROCESSORS No tariff details information for service ID >> " + service_id + " & Client ID " + client_id);
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("COMPUTE AGENCY TARIFF EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @return
     */
    public String getupdateTransactionId() { //Get current transaction Id
        try {
            String transactionId = crudTransactions.fetchsetLastTransactionId();
            //After fetching, increment the id
            String newTrsanctionId = new SpotcashUtilities().nextTransactionId(transactionId);
            crudTransactions.incrementReturnTransactionId(newTrsanctionId);
            return newTrsanctionId;
        } catch (Exception e) {
            LOGGER.error("Error generating a new transaction id" + e.toString());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param xipdevice_id
     * @param xipmsisdn
     * @param usermsisdn
     * @param logType
     * @param trxId
     * @param agentStoreData
     * @return
     */
    public Map<String, Object> retrieveTransactions(String xipdevice_id, String xipmsisdn, String usermsisdn, String logType, String trxId, SpAgentStores agentStoreData) {
        Calendar currenttime = Calendar.getInstance();
        Date sqldate = new Date((currenttime.getTime()).getTime());
        List<SpTransTempTable> customerData;
        Map<String, Object> lastFiveresponseData = new HashMap<>();
        if (agentStoreData.getAgentId() != null && agentStoreData.getAgentStoreId() != null) {
            LOGGER.info("SLOG TRANSACTION REQUEST  ## \n" + "AGENT ID : " + agentStoreData.getAgentId() + "\nSTORE ID : " + agentStoreData.getAgentStoreId() + "\nUSER NUMBER " + usermsisdn);
            customerData = crudTransactions.fetchTransTempTableData(agentStoreData.getAgentId().toString(), agentStoreData.getAgentStoreId().toString(), usermsisdn, trxId, 5);
            if (customerData != null) {
                List txnInfoList = new ArrayList<>();
                for (SpTransTempTable customerdetailData : customerData) {
                    Map<String, String> txnInfo = new HashMap<>();
                    txnInfo.put("amt", customerdetailData.getCustomerName());
                    if (customerdetailData.getRespCode() != null) {
                        if (!customerdetailData.getRespCode().equalsIgnoreCase("00")) {
                            txnInfo.put("to", "Failed" + "               " + "Txn Amount:" + customerdetailData.getAmount().toString());
                            txnInfo.put("fr", "Failed" + "               " + "Txn Amount:" + customerdetailData.getAmount().toString());
                        } else {
                            txnInfo.put("to", "Success" + "              " + "Txn Amount:" + customerdetailData.getAmount().toString());
                            txnInfo.put("fr", "Success" + "              " + "Txn Amount:" + customerdetailData.getAmount().toString());
                        }
                    } else {
                        txnInfo.put("to", "Failed" + "               " + "Txn Amount:" + customerdetailData.getAmount().toString());
                        txnInfo.put("fr", "Failed" + "               " + "Txn Amount:" + customerdetailData.getAmount().toString());
                    }
                    if (customerdetailData.getTimeCompleted() != null) {
                        String[] timelist = StringUtil.split(customerdetailData.getTimeCompleted().toString(), " ");
                        txnInfo.put("dt", timelist[0] + "     " + "Time:" + timelist[1]);
                    } else {
                        txnInfo.put("dt", sqldate.toString());
                    }
                    txnInfo.put("tid", customerdetailData.getTrxId());
                    txnInfo.put("type", crudTransactions.getserviceName(customerdetailData.getServiceId().toString()).getTitle());
                    txnInfoList.add(txnInfo);
                    lastFiveresponseData.put("trx", txnInfoList);
                }
                lastFiveresponseData.put("sc", "0000|OK");
                lastFiveresponseData.put("td", "Transaction complete");
                return lastFiveresponseData;
            } else {
                return lastFiveresponseData;
            }
        } else {
            return lastFiveresponseData;
        }
    }

    /**
     * @param request
     * @param agentStoreData
     * @return
     */
    protected boolean authenticateAgent_Customer(HttpProcessorRequest request, SpAgentStores agentStoreData) {
        boolean isAuth = false;
        try {
            if ("Telpo_Agent".equals(request.getHeader().getCategoryName().get(0))) {
                LOGGER.info("IT'S TELPO AGENT");
                return true;
            }
            switch (request.getRequestData().getTxnType()) {
                case "SLOG":
                case "SLOGP":
                case "UTIL":
                    isAuth = true;
                    break;
                case "SV":
                case "SABE":
                    LOGGER.info("AGENT STORE ID PASSED IS " + agentStoreData.getAgentStoreId());
                    if (request.getHeader().getUserId() != null) {
                        if (Integer.parseInt(request.getHeader().getUserId()) > 0) {
                            isAuth = crudTransactions.authAgent(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getHeader().getUserId(), request.getHeader().getUserXid());
                        } else {
                            isAuth = crudTransactions.authAgent(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getHeader().getUserXid());
                        }
                    } else {
                        isAuth = crudTransactions.authAgent(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getHeader().getUserXid());
                    }
                    break;
                default:
                    String cust_id = crudTransactions.fetchCustomerData("spCustId", request.getRequestData().getCustid()).getId().toString();
                    LOGGER.info("CUSTOMER toString==> " + cust_id);
                    if (agentStoreData.getAgentStoreId() != null) {
                        LOGGER.info("agent store not null==> ");
                        LOGGER.info("CustAgent Auth Credentials " + request.getRequestData().getAgentPin() + " " + agentStoreData.getAgentStoreId() + " " + request.getRequestData().getCustPin() + " " + cust_id);
                        if (request.getHeader().getUserId() != null) {
                            if (Integer.parseInt(request.getHeader().getUserId()) > 0) {
                                isAuth = crudTransactions.authAgentCustomer(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getRequestData().getCustPin(), cust_id, request.getHeader().getUserId(), request.getHeader().getUserXid());
                            } else {
                                isAuth = crudTransactions.authAgentCustomer(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getRequestData().getCustPin(), cust_id, request.getHeader().getUserXid());
                            }
                        } else {
                            isAuth = crudTransactions.authAgentCustomer(request.getRequestData().getAgentPin(), String.valueOf(agentStoreData.getAgentStoreId()), request.getRequestData().getCustPin(), cust_id, request.getHeader().getUserXid());
                        }
                        LOGGER.info("DID AgentCustomer Authenticated==> " + isAuth);
                    } else {
                        LOGGER.info("CUSTOMER is null==> ");
                        isAuth = false;
                    }
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("AUTHENTICATION ERROR {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return isAuth;
    }

    /**
     * @return @throws Exception
     */
    public PosResponseMapper call() throws Exception {
        try {
            LOGGER.info("REQUEST DATA AT @CALL>>>" + requestData);
            return transactionTypewatcher(new SpotcashUtilities().returnMappedXipRequest(requestData));
        } catch (Exception e) {
            LOGGER.info("REQUEST DATA AT @CALL (in Exception call)>>>" + requestData);
            LOGGER.error("TRANSACTION PROCESSOR ERROR >>>>> " + e.getLocalizedMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * @param crudTransactions
     * @param vpnConfigs
     * @return
     */
    @Override
    public CorebankingResponse invokecorebankingsystem(CrudTransactionController crudTransactions, CbsRequestData vpnConfigs) {
        LOGGER.info("CBSREQUESTADATA " + vpnConfigs);
        try {
            return new CorebankingSystemInvokerImpl(crudTransactions,reserveFundsConfiguration).callCbs(vpnConfigs, environment);
        } catch (Exception ex) {
            LOGGER.error("Error invoking CBS - Exception " + ex.toString());
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public ZposIntegratorResponse zposIntegratorResponse(CrudTransactionController crudTransactionController,
                                                         ZposRequestData zposRequestData) throws OzoneAuthenticationException {
        ZposHttpRequest httpRequest = new ZposHttpRequest(this.environment);
        return httpRequest.postPayment(zposRequestData);
    }

    private String getUtilityKeywordCode(final String keyword) {
        return UtilityCodes.valueOf(keyword).getUtilityCode();
    }
}