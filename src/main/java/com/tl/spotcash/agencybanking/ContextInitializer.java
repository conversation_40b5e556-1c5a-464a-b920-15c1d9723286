package com.tl.spotcash.agencybanking;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Initializes application-wide e.g loggin system environment variables.
 *
 * <AUTHOR>
 */
@Component
public class ContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext c) {
        System.setProperty("engine.name", "agency-banking-service");
        System.setProperty("log.target", "File");

        InetAddress ip;
        try {
            ip = InetAddress.getLocalHost();
            System.setProperty("engine.host", ip.getHostAddress());
        } catch (UnknownHostException ex) {
            System.setProperty("engine.host", "UKNOWN");
        }
    }

}
