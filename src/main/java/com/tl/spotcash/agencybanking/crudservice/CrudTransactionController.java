package com.tl.spotcash.agencybanking.crudservice;

import com.tl.spotcash.agencybanking.dao.Spotcashdaoimpl;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.service.ErrorMessageService;
import com.tl.spotcash.agencybanking.service.SpotcashserviceMap;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class CrudTransactionController {

    @Autowired
    Environment environment;
    @Autowired
    private JdbcTemplate jdbcTemplate;




    @Autowired
    CrudService crudService;

    @Autowired
    ErrorMessageService errorMessageService;


    private static final Logger LOGGER = LoggerFactory.getLogger(CrudTransactionController.class);



    public String fetchBMSdata(String query) {
        String responseMessage = null;
        try {
            responseMessage = new Spotcashdaoimpl(environment, crudService).fetchBMSdata(query);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR GETTING RECORDS #" + e.getLocalizedMessage());
        }
        return responseMessage;
    }

    public String fetchSpotcashdata(String query) {
        String responseMessage = null;
        try {
            responseMessage = new Spotcashdaoimpl(environment, crudService).fetchSpotcashdata(query);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR GETTING RECORDS #" + e.getLocalizedMessage());
        }
        return responseMessage;
    }

    //Fetch Client Application Info
    public SpClientsAppConfigurations fetchAppData(BigInteger clientId) {
        SpClientsAppConfigurations clientsAppConfigurations = null;
        try {
            clientsAppConfigurations = new Spotcashdaoimpl(environment, crudService).fetchAppDetails(clientId);
        }
        catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING APP DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return clientsAppConfigurations;
    }

    //Fetch Message Templates
    public SpMsgTemplates fetchMessageTemplate(BigInteger clientId, String messageType){
        SpMsgTemplates messageTemplate= null;
        try {

            messageTemplate  =  new Spotcashdaoimpl(environment, crudService).fetchMessageTemplate(clientId,messageType);
        }
        catch (Exception e){
            return null;
        }
        return messageTemplate;
    }

    public BigDecimal insertTransTmpTable(SpTransTempTable tempTransactionsData) {
        BigDecimal insertStatus = null;
        try {
            LOGGER.info("INSERTING A TEMP TRANS");
            insertStatus = new Spotcashdaoimpl(environment, crudService).createStageTransactions(tempTransactionsData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR INSERTING DATA INTO TRANS TEMP TABLE #" + e.getLocalizedMessage());
        }
        return insertStatus;
    }

    public BigDecimal insertAgencyTransTable(SpAgencyTransactions agencyTransactionData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).createAgencyTransaction(agencyTransactionData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR INSERTING DATA INTO AGENCY TRANS TABLE #" + e.getLocalizedMessage());
        }
        return insertStatus;
    }

    public BigDecimal insertAccountingEntries(SpAccountingEntries accountEntryData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).createAccountingEntries(accountEntryData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR CREATING ACCOUNTING ENTRIES#" + e.getLocalizedMessage());
        }

        return insertStatus;
    }



    public BigDecimal insertupdateAccounts(String operation, SpAccounts accountsData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).insertUpdateAccounts(accountsData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING ACCOUNTS TABLE #" + e.getLocalizedMessage());
        }

        return insertStatus;
    }
    public SpVpnclientsConfigs fetchClientAppBridgeConfiguration(BigInteger clientId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetch_vpn_configs(clientId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CLIENT APP BRIDGE CONFIGURATION #" + e.getLocalizedMessage());
            return null;
        }
    }
    public BigDecimal insertPaymentRecords(SpMpesaRecords paymentData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).insertPaymentRecords(paymentData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR INSERTING PAYMENT RECORDS #" + e.getLocalizedMessage());
        }

        return insertStatus;
    }

    public BigDecimal insertRequestsLogs(SpRequestLogs logsData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).insertRequestsLogs(logsData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR INSERTING REQUEST LOGS #" + e.getLocalizedMessage());
        }

        return insertStatus;
    }

    public List<SpTariff> fetchTariffs(BigDecimal serviceId) {
        List<SpTariff> tarrifData = null;
        try {
            tarrifData = new Spotcashdaoimpl(environment, crudService).fetchTariffs(serviceId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING TARIFF DATA #" + e.getLocalizedMessage());
        }
        return tarrifData;
    }

    public List<SpTariffDetails> fetchTariffsDetails(String tariffId, BigDecimal amount) {
        List<SpTariffDetails> tarrifDataDetails = null;
        try {
            tarrifDataDetails = new Spotcashdaoimpl(environment, crudService).fetchTariffDetails(tariffId, amount);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING TARIFF DETAILS #" + e.getLocalizedMessage());
        }
        return tarrifDataDetails;
    }

    public List<SpAgencyTariffDetails> fetchAgencyTariffsDetails(String tariffId, BigDecimal amount, String tariffType) {
        List<SpAgencyTariffDetails> tariffDataDetails = null;
        try {
            tariffDataDetails = new Spotcashdaoimpl(environment, crudService).fetchAgencyTariffDetails(tariffId, amount, tariffType);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENCY TARIFF DETAILS #" + e.getLocalizedMessage());
        }
        return tariffDataDetails;
    }

    public String fetchsetLastTransactionId() {
        String transactionId = null;
        try {
            transactionId = new Spotcashdaoimpl(environment, crudService).fetchsetLastTransactionId();
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING OR GETTING LAST TRANSACTION ID #" + e.getLocalizedMessage());
        }
        return transactionId;
    }

    public List<SpTransTempTable> checkTrxByStatus(BigInteger status) {
        List<SpTransTempTable> trxstatusDetails = null;
        try {
            trxstatusDetails = new Spotcashdaoimpl(environment, crudService).checkTransactionByStatus(status);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR GETTING TRANSACTION BY STATUS #" + e.getLocalizedMessage());
        }
        return trxstatusDetails;
    }

    public BigDecimal updateTrxStatus(BigDecimal theid, BigInteger status) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).updateTransactionStatus(theid, status);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING TRANSACTION STATUS #" + e.getLocalizedMessage());
        }
        return insertStatus;
    }

    public String incrementReturnTransactionId(String transaction_id) {
        String updated_transaction_id = null;
        try {
            updated_transaction_id = new Spotcashdaoimpl(environment, crudService).updateLastTransactionId(transaction_id);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR INCREMENTING TRANSACTION ID #" + ex.getLocalizedMessage());
        }
        return updated_transaction_id;
    }

    public SpServiceSubscriptions fetchServicesubscriptionData(BigInteger service_id, BigInteger client_id) {
        LOGGER.info("[ CRUD TRANSACTIONS ] Fetching service subscriptions for service ID >> " + service_id);
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchServicesubscriptionData(service_id, client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE SUBSCRIPTION DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    //overloaded
    public SpServiceSubscriptions fetchServicesubscriptionData(String serviceCode, BigInteger client_id) {
        LOGGER.info("[ CRUD TRANSACTIONS ] Fetching service subscriptions for service ID >><><><> " + serviceCode);
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchServicesubscriptionData(serviceCode, client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE SUBSCRIPTION DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpXmlTemplates fetchXmlTemplate(String functionName, BigInteger client_id) {
        LOGGER.info("[ CRUD TRANSACTIONS ] Fetching xml template for function >><><><> " + functionName);
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchXmlTemplate(functionName, client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE SUBSCRIPTION DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    //overloaded
    public SpServiceSubscriptions fetchServicesubscriptionData1(String serviceCode, BigInteger client_id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchServicesubscriptionData1(serviceCode, client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE SUBSCRIPTION DATA #" + e.getLocalizedMessage());
            return null;
        }
    }


    public SpAgencyTariff fetchAgencyTariffData(BigInteger service_id, BigInteger client_id) {
        LOGGER.info("[ CRUD TRANSACTIONS ] Fetching Agency Tariff for service ID >> " + service_id);
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgencyTariffData(service_id, client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENCY TARIFF DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpClients fetchclient_type(BigDecimal client_id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchClientType(client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CLIIENT TYPE #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpVpnclientsConfigs fetch_vpn_configs(BigInteger client_id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetch_vpn_configs(client_id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR GETTING VPN CONFIGS #" + e.getLocalizedMessage());
            return null;
        }
    }

    public String updateTransTempTable(SpTransTempTable fieldsData) {
        try {
            return new Spotcashdaoimpl(environment, crudService).updateTempTableTransactionStatus(fieldsData);
        } catch (Exception ex) {
            LOGGER.error("[ CRUD TRX CONTROLLER.UPDATETRANSTEMPTABLE ] FAILED UPDATING TRANSTEMP TABLE ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
            return null;
        }
    }

    public void updateTransTable(SpTransTempTable trx) {
        new Spotcashdaoimpl(environment, crudService).updateTransTempTable(trx);
    }

    public String createAgencyTransaction(SpTransTempTable fieldsData) {
        try {
            return new Spotcashdaoimpl(environment, crudService).createAgencyTransaction(fieldsData);
        } catch (Exception ex) {
            LOGGER.error("[ ERROR ENCOUNTERED DURING CREATING AGENCY TRANSACTION >> " + ex.getLocalizedMessage());
            return null;
        }
    }

    public boolean checkForDuplicateTransactions(HttpProcessorRequest requestData){
        try {
            return new Spotcashdaoimpl(environment, crudService).checkForDuplicateTransactions(requestData);
        } catch (Exception ex) {
            LOGGER.error("[ ERROR ENCOUNTERED ON CHECKING FOR DUPLICATE AGENCY TRANSACTION >> " + ex.getLocalizedMessage());
            return false;
        }
    }

    public SpCustomers fetchCustomerData(String identifier, String identifierValue) {
        SpCustomers updated_transaction_id = null;
        try {
            updated_transaction_id = new Spotcashdaoimpl(environment, crudService).fetchCustomerDetails(identifier, identifierValue);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return updated_transaction_id;
    }

    public SpCustomers fetchCustomerDataNewCon(String identifier, String identifierValue) {
        SpCustomers updated_transaction_id = null;
        try {
            updated_transaction_id = new Spotcashdaoimpl(environment, crudService).fetchCustomerDetailsNewCon(identifier, identifierValue);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return updated_transaction_id;
    }

    public void saveCustomerFingerprint(String nationalId, byte[] fingerprint) {
        try {
            new Spotcashdaoimpl(environment, crudService).saveCustomerFingerprint(nationalId, fingerprint);
        } catch (Exception ex) {

            LOGGER.error("THERE WAS AN ERROR SAVING THE CUSTOMER FINGERPRING >> ");
            ex.printStackTrace();
        }
    }

    public SpCustomers fetchCustomerByPin(String pin, String nationalId,String clientid) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByPin(pin, nationalId, clientid);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }

    public SpCustomers fetchCustomerByPinNewConn(String pin, String nationalId,String clientid) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByPinNewConn(pin, nationalId,clientid);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }

    //........................
    public SpCustomers fetchCustomerByPinNoClient(String pin, String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByPinNoClient(pin,nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }

    public SpCustomers fetchCustomerByPinNewConnNoClient(String pin, String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByPinNewConnNoClient(pin, nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }
    //........................


    public SpCustomers fetchCustomerByClientId(Long clientId, String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByClientId(clientId, nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }

    public SpCustomers fetchCustomerByClientIdAndNationalId(Long clientId, String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByClientIdAndNationalId(clientId, nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
            ex.printStackTrace();
        }
        return customers;
    }
    public SpCustomers fetchCustomerByClientIdAndMsisdn(Long clientId, String msisdn) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByClientIdAndMsisdn(clientId, msisdn);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
            ex.printStackTrace();
        }
        return customers;
    }

    public SpCustomers fetchSubscribedCustomerByNationalId(String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchSubscribedCustomerByNationalId(nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }

    public BigDecimal gettrantempId(String msisdn, String transactionId) {
        BigDecimal theid = null;
        try {
            theid = new Spotcashdaoimpl(environment, crudService).transtemptableid(msisdn, transactionId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR GETTING TRAN TEMP ID #" + e.getLocalizedMessage());
        }
        return theid;
    }

    public SpAgentStores fetchAgentStoreData(String device_id, String msisdn) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentStoreData(device_id, msisdn);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpAgents fetchAgentWithId(String id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentWithId(id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpAgentStores fetchAgentStoreDataWithMsIdnAndPin(String msisdn, String pin) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentStoreDataWithMsisdnAndPin(msisdn, pin);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchAgentStoreUserDataWithMsiSdnAndPin(String msisdn, String pin) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentStoreUserDataWithMsisdnAndPin(msisdn, pin);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchAgentStoreUserDataWithAgentStoreId(String agentStoreId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentStoreUserDataWithAgentStoreId(agentStoreId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpAgentStores fetchAgentStoreConfiguration(String clientId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentStoreConfiguration(clientId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE CONFIGURATION #" + e.getLocalizedMessage());
            return null;
        }
    }

    public List<SpTransTempTable> fetchTransTempTableData(String agent_id, String store_id, String clientmsisdn, String trxId, Integer numberofTransactions) {
        List<SpTransTempTable> transtable = null;
        try {
            transtable = new Spotcashdaoimpl(environment, crudService).fetchTransTempTableData(agent_id, store_id, clientmsisdn, trxId, numberofTransactions);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING DATA FROM TRANS TEMP TABLE #" + e.getLocalizedMessage());
        }
        return transtable;
    }

    public SpServices getserviceName(String service_id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getserviceName(service_id);
        } catch (Exception ex) {
            LOGGER.error("[ CRUD TRX CONTROLLER.UPDATETRANSTEMPTABLE ] FAILED UPDATING TRANSTEMP TABLE ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
            return null;
        }

    }

    public boolean authAgentCustomer(String agentPin, String agent_id, String custPin, String customer_id, String deviceId) {
        boolean isAuth;
        boolean authAgent = authAgent(agentPin, agent_id, deviceId);
        boolean authCust = authCust(custPin, customer_id);

        if ((authAgent && authCust) == true) {
            LOGGER.info("BOTH PARTIES AUTH SUCCESS");
            isAuth = Boolean.TRUE;
        } else {
            LOGGER.info("SOME ONE GAVE A WRONG PIN LETS CHECK ## ");
            isAuth = Boolean.FALSE;
        }
        return isAuth;
    }

    public boolean authAgentCustomer(String agentPin, String agent_id, String custPin, String customer_id, String user_id, String deviceId) {
        boolean isAuth;
        boolean authenticateAgent = authAgent(agentPin, agent_id, user_id, deviceId);
        boolean authCust = authCust(custPin, customer_id);

        if ((authenticateAgent && authCust) == true) {
            LOGGER.info("BOTH PARTIES AUTH SUCCESS");
            isAuth = Boolean.TRUE;
        } else {
            LOGGER.info("SOME ONE GAVE A WRONG PIN LETS CHECK ## ");
            isAuth = Boolean.FALSE;
        }
        return isAuth;
    }

    public SpAccounts agentFloat(String agent_id, String agentStoreId) {
        LOGGER.info("AGENT DATA: " + agent_id + " / " + agentStoreId);
        SpAccounts agentFloat = null;
        try {
            agentFloat = new Spotcashdaoimpl(environment, crudService).accountInfo(agent_id, agentStoreId);
            LOGGER.info("AVAILABLE BALANCE ##### " + agentFloat.getAvailBal());
        } catch (Exception e) {
            LOGGER.error("ERROR GETTING ACCOUNTS INFO " + e.getLocalizedMessage());
        }
        return agentFloat;
    }

    public boolean authAgent(String agentPin, String agent_id, String deviceId) {
        boolean isAuthAgent = false;
        try {
            isAuthAgent = new Spotcashdaoimpl(environment, crudService).authenticateAgent(agentPin, agent_id, deviceId);
            LOGGER.info("AGENT AUTHENTICATION IS ## " + isAuthAgent);
            return isAuthAgent;
        } catch (Exception e) {
            LOGGER.error("SOMETHING WENT WRONG AUTHENTICATING THIS AGENT ## ");
            return isAuthAgent;
        }
    }

    public boolean authAgent(String agentPin, String agent_id, String user_id, String deviceId) {
        boolean isAuthAgent = false;
        try {
            isAuthAgent = new Spotcashdaoimpl(environment, crudService).authenticateAgent(agentPin, agent_id, user_id, deviceId);
            LOGGER.info("AGENT AUTHENTICATION IS ## " + isAuthAgent);
            return isAuthAgent;
        } catch (Exception e) {
            LOGGER.error("SOMETHING WENT WRONG AUTHENTICATING THIS AGENT ## ");
            return isAuthAgent;
        }
    }

    public boolean authCust(String custPin, String customer_id) {
        boolean isAuthCust = false;
        try {
            isAuthCust = new Spotcashdaoimpl(environment, crudService).authenticateCustomer(custPin, customer_id);
            LOGGER.info("CUSTOMER AUTHENTICATION IS ## " + isAuthCust);
            return isAuthCust;
        } catch (Exception e) {
            LOGGER.error("SOMETHING WENT WRONG AUTHENTICATING THIS CUTOMER ## ");
            return isAuthCust;
        }

    }

    public List<SpAirtimeVouchers> getVoucherbyDenomination(String amount, String agent_id, String agent_store_id) {
        List<SpAirtimeVouchers> airtimeData = null;
        try {
            airtimeData = new Spotcashdaoimpl(environment, crudService).getVoucherbyDenomination(amount, agent_id, agent_store_id);
        } catch (Exception ex) {
            LOGGER.error("[ FAILED FETCHING VOUCHERS ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
        }
        return airtimeData;

    }

    public SpTransResponseTable insertAgencyMsgs(SpTransResponseTable msgsObj) {
        SpTransResponseTable responseStatus = null;
        try {
            responseStatus = new Spotcashdaoimpl(environment, crudService).insertAgencyMsgs(msgsObj);
            return responseStatus;
        } catch (Exception e) {
            LOGGER.error("There was an error persisting agency messages " + e.toString());
            return null;
        }
    }
    public SpMessages insertAgentMsgs(SpMessages msgsObj) {
        SpMessages responseStatus = null;
        try {
            responseStatus = new Spotcashdaoimpl(environment, crudService).insertAgentMsgs(msgsObj);
            return responseStatus;
        } catch (Exception e) {
            LOGGER.error("There was an error persisting agency messages " + e.toString());
            return null;
        }
    }

    public List<ChargeableAccounts> chargeableAccountsList(BigInteger serviceId) {
        return new Spotcashdaoimpl(environment, crudService).chargeableAccounts(serviceId);
    }

    public List<Object[]> getChargeableAccountsList(BigInteger serviceId) {
        return new Spotcashdaoimpl(environment, crudService).getChargeableAccounts(serviceId);
    }

//    public List<Object[]> chargeableAccountsList(BigInteger serviceId) {
//        return new Spotcashdaoimpl(environment, crudService).chargeableAccounts(serviceId);
//    }

    public Object returnSpotcashAccount_id(ChargeableAccounts chargeableObject) {
        return new Spotcashdaoimpl(environment, crudService).returnSpotcashAccountId(chargeableObject);
    }

    public void updateAccounts(String queryAmountDebit, String queryAmountCredit, String queryCommDebit, String queryCommCredit, String queryChargeDebit, String queryChargeCredit) {
        new Spotcashdaoimpl(environment, crudService).updateAccountBalances(queryAmountDebit, queryAmountCredit, queryCommDebit, queryCommCredit, queryChargeDebit, queryChargeCredit);
    }

    public void updateAccounts(String queryAmountDebit) {
        new Spotcashdaoimpl(environment, crudService).updateAccountBalances(queryAmountDebit);
    }

    public Double getAgentBalance(String phoneNumber) {
        return new Spotcashdaoimpl(environment, crudService).getAgentBalance(phoneNumber);
    }

    public SpAgents fetchAgentData(BigInteger clientId) {
        SpAgents agents = null;
        try {
            agents = new Spotcashdaoimpl(environment, crudService).fetchAgentDetails(clientId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return agents;
    }

    public List<SpAgentAuthentication> fetchAgentAuthenticationData(BigInteger agentId) {
        List<SpAgentAuthentication> authData = null;
        try {
            authData = new Spotcashdaoimpl(environment, crudService).fetchAuthData(agentId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING Agent Authentication DATA #" + e.getLocalizedMessage());
        }
        return authData;
    }

    public SpAgentStores fetchAgentWithStoreId(BigInteger id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentWithStoreId(id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public String resetAgentStorePin(String msisdn) {
        try {
            return new Spotcashdaoimpl(environment, crudService).resetAgentStorePin(msisdn);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR RESETTING AGENT STORE PIN#" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchStoreUserWithId(BigInteger id, String deviceId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchStoreUserWithId(id, deviceId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchStoreUserWithId(String storeUserId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchStoreUserWithId(storeUserId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            throw new UsernameNotFoundException("STORE USER WITH STORE_USER_ID :: "+ storeUserId + " NOT FOUND!");
        }
    }

    public ArrayList<Map<String, Object>> updateAgentStorePin(String msisdn, String pin) {
        try {
            return new Spotcashdaoimpl(environment, crudService).updateAgentStorePin(msisdn, pin);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING AGENT STORE PIN #" + e.getLocalizedMessage());
            return null;
        }
    }

    public Map<String, Object> updateAgentStorePinOld(String msisdn, String pin) {
        try {
            return new Spotcashdaoimpl(environment, crudService).updateAgentStorePinOld(msisdn, pin);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING AGENT STORE PIN #" + e.getLocalizedMessage());
            return null;
        }
    }


    public SpTransTempTable getTransaction(String trxId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getTransaction(trxId);
        } catch (Exception ex) {
            LOGGER.error("[ CRUD GET TRANSACTION MODEL FAILED. ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
            return null;
        }
    }
    public SpTransTempTable getTransactionById(String trxId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getTransactionById(trxId);
        } catch (Exception ex) {
            LOGGER.error("[ CRUD GET TRANSACTION MODEL FAILED. ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
            return null;
        }
    }

    public BigDecimal updateTransTmpTable(SpTransTempTable tempTransactionsData) {
        BigDecimal insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).updateTransTmpTable(tempTransactionsData);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING DATA INTO TRANS TEMP TABLE #" + e.getLocalizedMessage());
        }
        return insertStatus;
    }

    public ArrayList<SpAgencyReverseTransactions> fetchReverseTransactions(BigInteger count) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchReverseTransactions(count);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("THERE WAS AN ERROR FETCHING REVERSE TRANSACTIONS #" + e.getMessage());
            return null;
        }
    }

    public ArrayList<SpAgencyPendingTransactions> fetchPendingTransactions() {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchPendingTransactions();
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("THERE WAS AN ERROR FETCHING PENDING TRANSACTIONS #" + e.getMessage());
            return null;
        }
    }

    public String fetchforceUpdateStatus(BigInteger clientId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchforceUpdateStatus(clientId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void sendSmswithLink(BigInteger clientId, String msisdn) {
        try {
            new Spotcashdaoimpl(environment, crudService).sendSmswithLink(clientId, msisdn);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateCustomerPinRetries(BigDecimal spCustomerId, Integer pinRetries) {
        try {
            new Spotcashdaoimpl(environment, crudService).updateCustomerPinRetries(spCustomerId, pinRetries);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateCustomerOtpRetries(BigDecimal spCustomerId, Integer pinRetries) {
        try {
            new Spotcashdaoimpl(environment, crudService).updateCustomerOtpRetries(spCustomerId, pinRetries);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void lockCustomerDueToPin(BigDecimal spCustomerId, Integer pinRetries) {
        try {
            new Spotcashdaoimpl(environment, crudService).lockCustomerDueToPin(spCustomerId, pinRetries);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void lockCustomerDueToOtp(BigDecimal spCustomerId, Integer otpRetries) {
        try {
            new Spotcashdaoimpl(environment, crudService).lockCustomerDueToOtp(spCustomerId, otpRetries);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resetPinRetries(BigDecimal spCustomerId) {
        try {
            new Spotcashdaoimpl(environment, crudService).resetCustomerPinRetries(spCustomerId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void resetOtpRetries(BigDecimal spCustomerId) {
        try {
            new Spotcashdaoimpl(environment, crudService).resetCustomerOtpRetries(spCustomerId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<SpAgencyRequestLog> fetchPreviousRequestLogsLimitedToMins(String ipAddress, Long timeLimitInMinutes) {
        try {
            return new Spotcashdaoimpl(environment, crudService)
                    .fetchPreviousRequestLogsLimitedToMins(ipAddress, timeLimitInMinutes);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public void archiveRequestLogs(String ipAddress) {
        try {
            new Spotcashdaoimpl(environment, crudService)
                    .archiveRequestLogs(ipAddress);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public SpCustomers fetchCustomerByPinEncrypted(String pin, String nationalId) {
        SpCustomers customers = null;
        try {
            customers = new Spotcashdaoimpl(environment, crudService).fetchCustomerByPinEncrypted(pin, nationalId);
        } catch (Exception ex) {
            LOGGER.error("THERE WAS AN ERROR FETCHING CUSTOMER DATA # ERROR DETAIL >> " + ex.getLocalizedMessage());
        }
        return customers;
    }


    public SpServiceSubscriptions getTransactionCharges(BigDecimal clientId, String serviceId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getTransactionCharges(clientId, serviceId);
        } catch (Exception ex) {
            LOGGER.error("ERROR OCCURED FETCHING SERVICE SUBSCRIPTION DETAILS >> " + ex.getLocalizedMessage());
            return null;
        }
    }

    public BigInteger getMaxAllowed(String clientId, String msisdn, String serviceId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getMaxAllowed(clientId, msisdn, serviceId);
        } catch (Exception ex) {
            LOGGER.error("ERROR OCCURED FETCHING MAXIMUM ALLOWED DAILY TRANSACTION FOR " + msisdn + " OF CLIENT ID " + clientId + " FOR SERVICE ID " + serviceId + "  >> " + ex.getLocalizedMessage());
            return null;
        }
    }

    public SpAccounts getAccountDetails(String spotcashAccountId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getAccountDetails(spotcashAccountId);
        } catch (Exception ex) {
            LOGGER.error("ERROR OCCURED FETCHING SERVICE SUBSCRIPTION DETAILS >> " + ex.getLocalizedMessage());
            return null;
        }
    }


    public String getServiceID(String xipService) {
        try {
            return new Spotcashdaoimpl(environment, crudService).getServiceID(xipService);
        } catch (Exception ex) {
            LOGGER.error("ERROR OCCURED FETCHING SERVICE DETAILS >> " + ex.getLocalizedMessage());
            return null;
        }
    }



    public Optional<SpAgencyOtpLog> fetchOtpLogByNationalIdNumber(String nationalId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchOtpLogsByNationalId(nationalId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    public Optional<List<SpAgencyOtpLog>> fetchWrongOtpLogEntriesUsingStoreUserId(String storeUserId, Timestamp timePeriod) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchWrongOtpLogEntriesUsingStoreUserId(storeUserId, timePeriod);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }

    public void blacklistMsisdnForClient(String msisdn, Long clientId, String ipAddress) {
        try  {
            new Spotcashdaoimpl(environment, crudService).blacklistMsisdnForClient(msisdn, clientId, ipAddress);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void blacklistMsisdnForClient(String msisdn, Long clientId, Long storeUserId, String ipAddress) {
        try  {
            new Spotcashdaoimpl(environment, crudService).blacklistMsisdnForClient(msisdn, clientId, storeUserId, ipAddress);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void blacklistStoreUserId(String storeUserId, String ipAddress, long clientId, String msisdn) {
        try  {
            new Spotcashdaoimpl(environment, crudService).blacklistStoreUserId(storeUserId, ipAddress, clientId, msisdn);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isBlacklisted(String msisdn, Long clientId) {
        try  {
           return new Spotcashdaoimpl(environment, crudService).isBlacklisted(msisdn, clientId.intValue());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean isAgentBlacklisted(Long storeUserId) {
        try  {
            return new Spotcashdaoimpl(environment, crudService).isAgentBlacklisted(storeUserId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void resetOtpLog(String nationalId) {
        try  {
            new Spotcashdaoimpl(environment, crudService).resetOtpLog(nationalId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void createOtpLog(String msisdn, String nationalId, String clientId, String otp) {
        try  {
            new Spotcashdaoimpl(environment, crudService).createOtpLog(msisdn, nationalId, Long.parseLong(clientId), otp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void createOtpLog(String msisdn, String nationalId, String clientId, String storeUserId, String otp) {
        try  {
            new Spotcashdaoimpl(environment, crudService).createOtpLog(msisdn, nationalId, Long.parseLong(clientId), Long.parseLong(storeUserId), otp);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public <T> void save(T entity) {
        new Spotcashdaoimpl(environment, crudService).save(entity);
    }

    public String fetchAgentIdForAuthentication(String agentStoreId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgentIdForAuthentication(agentStoreId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchStoreUserWithMsIdn(String msisdn) {
        return new Spotcashdaoimpl(environment, crudService, errorMessageService).fetchStoreUserWithMsIdn(msisdn);
    }
    public String checkUniqueDeviceId(String storeId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchUniqueDeviceId(storeId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING DEVICE UNIQUE ID" + e.getLocalizedMessage());
            return null;
        }
    }
    public void updateAppUniqueId(String agentStoreId, String appUniqueId){
        try {
            new Spotcashdaoimpl(environment, crudService).updateAppUniqueId(agentStoreId, appUniqueId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING APP UNIQUE ID: {}" + e.getLocalizedMessage());

        }
    }
    public SpImsiRecords fetchImsiRecord(String msisdn){
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchImsiRecord(msisdn);
        } catch (Exception e) {
            LOGGER.error("THERE WAS ERROR FETCHING IMSI RECORD: {}" + e.getLocalizedMessage());
            return null;
        }
    }

    public void updateImsiRecord(SpImsiRecords imsiRecords){
        try {
            new Spotcashdaoimpl(environment, crudService).updateImsiRecord(imsiRecords);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING IMSI RECORD: {}" + e.getLocalizedMessage());
        }
    }
    public void deactivatedStoreUser(String msisdn){
        try {
            new Spotcashdaoimpl(environment, crudService).deactivatedStoreUser(msisdn);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR DEACTIVATING USER: {}" + e.getLocalizedMessage());
        }
    }

    public void updateLocationLog(SpLocationLog spLocationLog){
        try {
            new Spotcashdaoimpl(environment, crudService).updateLocationLog(spLocationLog);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR CREATING LOCATION RECORD LOG: {}" + e.getLocalizedMessage());
        }
    }

    public SpServices fetchServiceWithId(String serviceId){
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchServiceWithId(serviceId);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE: {}" + e.getLocalizedMessage());
            return null;
        }
    }
    public BigDecimal saveUpdateTokenAndKey(SpTokenKey tokenKey){
        try {
            new Spotcashdaoimpl(environment, crudService).saveUpdateTokenAndKey(tokenKey);
        }catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error Persisting the token record to db: {}" + e.getLocalizedMessage());
        }
        return tokenKey.getId();
    }
    public SpTokenKey fetchTokenAndKey(BigInteger storeUserId){
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchTokenAndKey(storeUserId);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING KEY: {}" + e.getLocalizedMessage());
        }
        return null;
    }

    public SpTokenKey fetchTokenAndKeyWithToken(String token){
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchTokenAndKeyWithToken(token);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("THERE WAS AN ERROR FETCHING KEY: {}" + e.getLocalizedMessage());
        }
        return null;
    }
    public void deactivateAgentStore(BigInteger storeId){
        try {
            new Spotcashdaoimpl(environment, crudService).deactivateAgentStore(storeId);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR DEACTIVATING AGENTSTORE: {}" + e.getLocalizedMessage());
        }
    }
    public void saveNewDeviceId(BigInteger storeId, String deviceId){
        try {
            new Spotcashdaoimpl(environment, crudService).saveNewDeviceId(storeId, deviceId);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR saving new device id: {}" + e.getLocalizedMessage());
        }
    }

    public SpAgencyConfigMessages fetchAgencyConfigMessages(String clientId){
        LOGGER.info("[ CRUD TRANSACTIONS ] Fetching agency config messages for client ID >><><><> " + clientId);
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAgencyConfigMessages(clientId);
        }catch (Exception e) {
            LOGGER.error("ERROR FETCHING AGENCY CONFIG MESSAGES FOR CLIENT ID: {}. ERROR :: {}", clientId, e.getLocalizedMessage());
            return null;
        }
    }

    public SpStoreUsers fetchStoreUserWithPhoneNumber(String msisdn) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchStoreUserWithPhoneNumber(msisdn);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public void resetCustomerRetryCount(String nationalId, String clientId) {
        try  {
            new Spotcashdaoimpl(environment, crudService).resetCustomerRetryCount(nationalId, clientId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public BigDecimal getMaximumDailyAmount(String clientId, String msisdn, String serviceCode){
        try  {
            Class serviceMappingsClass = SpotcashserviceMap.class;
            String xip_service_id = (String) serviceMappingsClass.getMethod("get" + serviceCode)
                    .invoke(serviceMappingsClass.newInstance());
            return new Spotcashdaoimpl(environment, crudService).getMaximumDailyAmount(clientId, msisdn, xip_service_id);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("ERROR OCCURRED FETCHING MAXIMUM ALLOWED DAILY TRANSACTION FOR " + msisdn + " OF CLIENT ID "
                    + clientId + " FOR SERVICE ID " + serviceCode + "  >> " + e.getLocalizedMessage());
            return BigDecimal.ZERO;
        }
    }

    public boolean isServiceChargeable(String serviceCode) {
        try {
            return new Spotcashdaoimpl(environment, crudService).isServiceChargeable(serviceCode);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("ERROR OCCURRED ON CHECKING IF THE SERVICE:'{}' IS  CHARGEABLE. ERROR :: {} ",
                    serviceCode, e.getMessage());
            return false;
        }
    }

    public void resetStoreUserPinRetry(String msisdn){
        try {
            new Spotcashdaoimpl(environment, crudService).resetStoreUserPinRetry(msisdn);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR RE-SETTING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public void updateStoreUserPinRetry(String msisdn, String retryCount){
        try {
            new Spotcashdaoimpl(environment, crudService).updateStoreUserPinRetry(msisdn, retryCount);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public void updateStoreUserDeactivationCount(String msisdn, String retryCount){
        try {
            new Spotcashdaoimpl(environment, crudService).updateStoreUserDeactivationCount(msisdn, retryCount);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public void resetStoreUserDeactivationCount(String msisdn){
        try {
            new Spotcashdaoimpl(environment, crudService).resetStoreUserDeactivationCount(msisdn);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR RE-SETTING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public SpAgencyEncryptionKey fetchEncryptionKey(String deviceId, String clientId) {
        try { return new Spotcashdaoimpl(environment, crudService).fetchEncryptionKey(deviceId, clientId); }
        catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            e.printStackTrace();
            return null;
        }
    }

    public SpStoreUsers fetchStoreUser(String msisdn, String clientId) {
        try { return new Spotcashdaoimpl(environment, crudService).fetchStoreUser(msisdn, clientId); }
        catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING AGENT STORE USER DATA #" + e.getLocalizedMessage());
            return null;
        }
    }

    public void updateStoreUserPinRetry(String msisdn, String retryCount, String clientId){
        try { new Spotcashdaoimpl(environment, crudService).updateStoreUserPinRetry(msisdn, retryCount, clientId); }
        catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public void deactivatedStoreUser(String msisdn, String clientId){
        try { new Spotcashdaoimpl(environment, crudService).deactivatedStoreUser(msisdn, clientId); }
        catch (Exception e) { LOGGER.error("THERE WAS AN ERROR DEACTIVATING USER: {}" + e.getLocalizedMessage());}
    }

    public void resetStoreUserPinRetry(String msisdn, String clientId){
        try {
            new Spotcashdaoimpl(environment, crudService).resetStoreUserPinRetry(msisdn, clientId);
        }catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR RE-SETTING STORE_USER PIN_RETRIES: {}" + e.getLocalizedMessage());
        }
    }

    public String fetchStoreUserMsisdn(String id) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchStoreUserMsisdn(id);
        } catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR FETCHING STORE USER MSISDN #" + e.getLocalizedMessage());
            return null;
        }
    }
    public SpAccounts fetchAccounts(String clientId, String serviceId) {
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchAccounts(clientId, serviceId);
        } catch (Exception ex) {
            LOGGER.error("ERROR OCCURED FETCHING ACCOUNTS >> " + ex.getLocalizedMessage());
            return null;
        }
    }
    public BigInteger createtrxValue(SpTrxValue spTrxValue) {
        LOGGER.error("PASSES HERE #");
        BigInteger insertStatus = null;
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).createtrxValue(spTrxValue);
        }
        catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR INSERTING DATA INTO sp trx value TABLE #" + e.getMessage());
            e.printStackTrace();
        }
        return insertStatus;
    }
    public String updateTrxValue(SpTrxValue spTrxValue) {
        String insertStatus = "";
        try {
            insertStatus = new Spotcashdaoimpl(environment, crudService).updateSpTrxValue(spTrxValue);
        }
        catch (Exception e) {
            LOGGER.error("THERE WAS AN ERROR UPDATING TRANSACTION STATUS #" + e.getLocalizedMessage());
        }
        return insertStatus;
    }
    public SpTrxValue fetchSptrxValue(String trxId) {
        SpTrxValue spTrxValue= null;
        try {
            spTrxValue = new Spotcashdaoimpl(environment,crudService).fetchSptrxValue(trxId);
        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("THERE WAS AN ERROR FETCHING SERVICE SUBSCRIPTION DATA #" + e.getLocalizedMessage());
        }
        return spTrxValue;
    }

    public Optional<List<SpPostCompletedTransactionsCbs>> fetchSpPostCompleteTransactionsCbs(){
        try {
            return new Spotcashdaoimpl(environment, crudService).fetchSpPostCompleteTransactionsCbs();

        }catch (Exception e) {
            e.printStackTrace();
        }
        return Optional.empty();
    }


    public SpTransTempTable getEventTransaction(String msisdn, String eventId, String amount, String serviceId) {
        try {return new Spotcashdaoimpl(environment, crudService).getEventTransaction(msisdn, eventId, amount, serviceId);}
        catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("[ CRUD GET EVENT TRANSACTION MODEL FAILED. ERROR ENCOUNTERED >> " + ex.getLocalizedMessage());
            return null;
        }
    }


public boolean isStoreUserSubscribedToService(String serviceCode, Long clientId, Long storeuserId) {
    if(serviceCode.equals("AGBAL")){ return true; }

    String query = "SELECT COUNT(*) AS COUNT_RESULT " +
            "FROM SP_STORE_USER_SERVICE_SUBSCRIPTION " +
            "WHERE CLIENT_ID = :clientId " +
            "AND STORE_USER_ID = :storeuserId " +
            "AND SERVICE_ID IN (SELECT ID FROM SP_SERVICES WHERE SERVICE_CODE = :serviceCode ) " +
            "AND STATUS = 1";


    Map<String, Object> params = new HashMap<>();
    params.put("clientId", clientId);
    params.put("storeuserId", storeuserId);
    params.put("serviceCode", serviceCode);

    List<Object> result = crudService.fetchWithNativeQuery(query, params, 0, 1);
    if (result.isEmpty()) return false;

    Object countValue = result.get(0);
    if (countValue instanceof BigDecimal) {
        return ((BigDecimal) countValue).intValue() > 0;
    } else if (countValue instanceof Number) {
        return ((Number) countValue).intValue() > 0;
    }

    return false;
}



public boolean isAgentServiceSubscriptionEnabled(String agentIdFromDb, String serviceCode) {
    String query = "SELECT AGENT_SERVICE_SUBSCRIPTION FROM SP_AGENTS WHERE AGENT_ID = :agentIdFromDb AND AGENT_SERVICE_SUBSCRIPTION = 1";

    Map<String, Object> params = new HashMap<>();
    params.put("agentIdFromDb", agentIdFromDb);

    List<Object> result = crudService.fetchWithNativeQuery(query, params, 0, 1);

    return !result.isEmpty(); // If any record returned, service subscription is enabled
}

    public String fetchAgentIdFromStoreId(BigDecimal storeId) {
        String sql = "SELECT AGENT_ID FROM SP_AGENT_STORES WHERE AGENT_STORE_ID = :storeId";
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", storeId);

        List<Object> result = crudService.fetchWithNativeQuery(sql, params, 0, 1);
        if (!result.isEmpty()) {
            Object agentId = result.get(0);  // It’s already a scalar value (e.g., BigDecimal)
            return agentId != null ? agentId.toString() : null;
        }
        return null;
    }





}
