package com.tl.spotcash.agencybanking.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class SpotcashserviceMap {

	private static final Logger LOGGER = LoggerFactory.getLogger(SpotcashserviceMap.class);

	public final String SLOG = "SLOG";
	public final String SLOGP = "SLOGP";
	public final String SPS = "SPS";
	public final String SGDC = "SGDC";
	public final String SDT = "57";
	public final String SWD = "56";
	public final String SCMS = "12";
	public final String SCBE = "58";
	public final String SAT = "3";
	public final String SABE = "SABE";
	public final String EXTD="60";
	public final String SV = "SV";
	public final String UTIL = "UTIL";
	public final String AGBAL = "AGBAL";
	public final String NMD	= "63";
	public final String KT ="70";
	public final String KP ="71";
	public final String MPF = "821";
	public final String IAT = "822";

//    CLIENT_DEPOSIT("57"),
//    CLIENT_WITHDRAWAL("56"),
//    CLIENT_MINISTATEMENT("12"),
//    CLIENT_BALANCE("58"),
//    CLIENT_AIRTIME("3"),
	public String getSLOG() {
		LOGGER.info("SLOG RETURNED ID : " + SLOG);
		return SLOG;
	}

	public String getSLOGP() {
		LOGGER.info("SLOGP RETURNED ID : " + SLOGP);
		return SLOGP;
	}

	public String getSPS() {
		LOGGER.info("SPS RETURNED ID : " + SPS);
		return SPS;
	}

	public String getSGDC() {
		LOGGER.info("SGDC RETURNED ID : " + SGDC);
		return SGDC;
	}

	public String getSDT() {
		LOGGER.info("SDT RETURNED ID : " + SDT);
		return SDT;
	}

	public String getSCMS() {
		LOGGER.info("SCMS RETURNED ID : " + SCMS);
		return SCMS;
	}

	public String getSCBE() {
		LOGGER.info("SCBE RETURNED ID : " + SCBE);
		return SCBE;
	}

	public String getSAT() {
		LOGGER.info("SAT RETURNED ID : " + SAT);
		return SAT;
	}

	public String getSABE() {
		LOGGER.info("SABE RETURNED ID : " + SABE);
		return SABE;
	}

	public String getSV() {
		LOGGER.info("SV RETURNED ID : " + SV);
		return SV;
	}

	public String getSWD() {
		LOGGER.info("SWD RETURNED ID : " + SWD);
		return SWD;
	}

	public String getNMD() {
		LOGGER.info("NMD RETURNED ID: " +NMD);
		return NMD;
	}

	public String getUTIL() {
		LOGGER.info("UTIL RETURNED ID : " + UTIL);
		return UTIL;
	}

	public String getAGBAL() {
		LOGGER.info("AGBAL RETURNED ID : " + AGBAL);
		return AGBAL;
	}

	public String getEXTD() {
		return EXTD;
	}

	public String getKT() {
		LOGGER.info("KPLC TOKEN PURCHASE ID: " +KT);
		return KT;
	}

	public String getKP() {
		LOGGER.info("KPLC POST PAID PURCHASE ID: " +KP);
		return KP;
	}
	public String getMPF(){
		LOGGER.info("MPF MPESA FLOAT PURCHASE:"+MPF);
		return MPF;
	}
	public String getIAT(){
		LOGGER.info("IAT Inter  Account Transfer:"+IAT);
		return IAT;
	}
}
