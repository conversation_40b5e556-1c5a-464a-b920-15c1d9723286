package com.tl.spotcash.agencybanking.service;

import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.controller.api.ApiRequestController;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.AdditionalMsgResponse;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.CbsResponseData;
import com.tl.spotcash.agencybanking.custommodels.SpotcashTransactionService;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.custommodels.event.FetchMpesaMembersRequest;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import com.tl.spotcash.agencybanking.xiputils.SpotcashTrxDetails;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class FetchMpesaMembersScheduleTask {
    private final ApiRequestController apiRequestController;
    @Autowired
    SpotcashCbsService cbsService;

    @Autowired
    CrudTransactionController crudTransactionController;
    private String validationResponseMessage;

    //@Scheduled(initialDelay = 5000, fixedDelay = 30000)
    public void fetchMpesaMembers() throws IOException {
        //LOGGER.info("Initiating Fail Pending transaction");
        FetchMpesaMembersRequest request = new FetchMpesaMembersRequest();
        request.setResponseCode("");
        request.setResponseMessage("");
        request.setNewMembersPayload("");
        String clientId = "52";
        String serviceId = "798";

        // Fetch the response from the service
        FetchMemberByMpesaCbsResponse fetchMemberByMpesaCbsResponse = cbsService.fetchMemberByMpesaCbsResponse(request, clientId);

        // Check if the response is valid and has the right code
        if (fetchMemberByMpesaCbsResponse != null && "00".equalsIgnoreCase(fetchMemberByMpesaCbsResponse.getResponseCode())) {

            // Get the new members payload
            NewMembersPayload newMembersPayload = fetchMemberByMpesaCbsResponse.getNewMembersPayload();

            // Check if the payload contains any members
            if (newMembersPayload != null && newMembersPayload.getNewMembersMpesa() != null) {

                // Iterate through each member in the payload
                for (NewMembersMpesa newMember : newMembersPayload.getNewMembersMpesa()) {

                    String customerMsisdn = newMember.getPhoneNo();
                    //String amount = newMember.getAmount();
                    BigDecimal amountConverted = null;
                    String amount = newMember.getAmount();
                    if (amount != null && !amount.trim().isEmpty()) {
                        // Remove any commas from the amount string before converting
                        amount = amount.replace(",", "");
                         amountConverted = new BigDecimal(amount);
                    } else {
                        // Handle the null or empty amount case
                        throw new IllegalArgumentException("Amount is null or empty");
                    }

                    validationResponseMessage = apiRequestController.validateEventFloat(amountConverted, new BigDecimal(clientId), customerMsisdn, crudTransactionController, serviceId);
                    if (validationResponseMessage.contains("00")) {
                        //Check if the transaction was already initialized using the msisdn, eventId, amount
                        SpTransTempTable existingSpTransTempTable = crudTransactionController.getEventTransaction(
                                newMember.getPhoneNo(), newMember.getEventId(), newMember.getAmount(), serviceId);

                        if(existingSpTransTempTable == null){
                            // Create a new transaction object for each member
                            SpotcashTrxDetails transactionDataObj = new SpotcashTrxDetails();

                            // Set the transaction details using the member data
                            transactionDataObj.setAmount(newMember.getAmount());  // Set the amount
                            transactionDataObj.setCustomer_msisdn(newMember.getPhoneNo());  // Use phone number from member
                            transactionDataObj.setCustomer_name(newMember.getMemberName());  // Set customer name or other relevant field
                            transactionDataObj.setAgentPhoneNumber(newMember.getAgentDevicePhoneNo());
                            transactionDataObj.setClient_id(clientId);  // Set the client ID
                            //transactionDataObj.setSpotcash_agent_id(new BigInteger(agentId));  // Set the agent ID
                            transactionDataObj.setSpotcash_service_id(serviceId);  // Set the service ID

                            String trxId = apiRequestController.createTransTempPayment(transactionDataObj, null,null,null);
                            log.info("transaction ID {}", trxId);
                            SpTransTempTable spTransTempTable = crudTransactionController.getTransaction(trxId);
                            spTransTempTable.setTrxStatus(BigInteger.valueOf(2));
                            spTransTempTable.setIntermediateStatus(BigInteger.valueOf(1));
                            spTransTempTable.setRespCode("00");
                            spTransTempTable.setNotifyCbs(0);
                            //spTransTempTable.setTrxStatus(BigInteger.valueOf(4));
                            spTransTempTable.setCbsDocNo(newMember.getDocumentNo());
                            spTransTempTable.setOtherFields(newMember.getEventId());
                            crudTransactionController.updateTransTempTable(spTransTempTable);

                            SpTransResponseTable msgObject = new SpTransResponseTable();
                            String msg = "<refId> Confirmed. Dear " + newMember.getMemberName() + " you have received Ksh "
                                    + newMember.getAmount() + " for attending the " + newMember.getEventName()
                                    + " Event. Thank you for your participation.";
                            msgObject.setMessage(msg);
                            msgObject.setTrxId(trxId);
                            try{
                                SpClientsAppConfigurations clientsAppConfigurations = crudTransactionController.fetchAppData(new BigInteger(clientId));
                                if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
                                    SpotcashTransactionService service_id = SpotcashTransactionService.getvalueof(serviceId);
                                    String messageType = "AB_" + service_id +"_"+ "00";

                                    CbsRequestData cbsrequestdata = new CbsRequestData();
                                    CbsResponseData cbsResponse = new CbsResponseData();

                                    cbsrequestdata.setCustomer_name(newMember.getMemberName());
                                    cbsrequestdata.setTransactionId(trxId);
                                    cbsrequestdata.setMsisdn(newMember.getPhoneNo());
                                    cbsResponse.setAmount(newMember.getAmount());
                                    cbsResponse.setMessage(newMember.getEventName());
                                    cbsResponse.setResponseMessage("");

                                    SpMsgTemplates messageTemplate = crudTransactionController.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
                                    if(null != messageTemplate){
                                        //Additional Data that may be required for the message and is not part of the cbs request and response data
                                        AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();

                                        msg = new SharedFunctions().messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
                                        if (!ObjectUtils.isEmpty(msg)) msgObject.setMessage(msg);
                                    }
                                }
                            }
                            catch (Exception e) {
                                log.info("FAILED TO PROCESS EVENT MESSAGE TEMPLATE FOR TRX_ID: {}. ERROR: {}",
                                        trxId, e.getMessage());
                                e.printStackTrace();
                            }

                            crudTransactionController.insertAgencyMsgs(msgObject);
                        }
                        else {
                            log.info("An event fund disbursement transaction was already initialized. REQUEST_BODY: {}",
                                    new Gson().toJson(newMember));
                        }
                    }
                    else if (validationResponseMessage.contains("01#07")) log.error("Float not available for mpesa fund disbursement");
                }
            }
        }
    }

   //@Scheduled(initialDelay = 5000, fixedDelay = 30000)
    public void isPosted() throws IOException{
        //fetch the completed mpesa
        Optional<List<SpPostCompletedTransactionsCbs>> spPostCompletedTransactionsCbs = crudTransactionController.fetchSpPostCompleteTransactionsCbs();
        if (spPostCompletedTransactionsCbs != null && spPostCompletedTransactionsCbs.isPresent()){
            for (SpPostCompletedTransactionsCbs spPostCompletedTransactionsCbs1 : spPostCompletedTransactionsCbs.get()) {
                SpTransTempTable spTransTempTable = crudTransactionController.getTransaction(spPostCompletedTransactionsCbs1.getTrxId());
                spTransTempTable.setNotifyCbs(2); //To prevent it to be picked again
                crudTransactionController.updateTransTempTable(spTransTempTable);

                FetchTranscationIdRequest request = new FetchTranscationIdRequest();
                request.setPhoneNo(spPostCompletedTransactionsCbs1.getMsisdn());
                request.setTrxId(spPostCompletedTransactionsCbs1.getTrxId());
                request.setPaymentReference(!ObjectUtils.isEmpty(spPostCompletedTransactionsCbs1.getPaymentReference())
                    ? spPostCompletedTransactionsCbs1.getPaymentReference() : "");
                request.setEventId(spPostCompletedTransactionsCbs1.getEventId());
                FetchTranscationIdResponse fetchTranscationIdResponse = cbsService.fetchTranscationIdResponse(
                        request, spTransTempTable.getClientId().toString());

                //if response is available
                if (fetchTranscationIdResponse != null && fetchTranscationIdResponse.getResponseCode().equalsIgnoreCase("00")){
                    log.info("mpesa fund disbursement event transaction posted to cbs successfully. TRX_ID: {}, " +
                            "MpesaId: {}", request.getTrxId(), request.getPaymentReference());
                    spTransTempTable.setNotifyCbs(1);
                    crudTransactionController.updateTransTempTable(spTransTempTable);
                }
                else{
                    String cbsResCode = !ObjectUtils.isEmpty(fetchTranscationIdResponse)
                            ? fetchTranscationIdResponse.getResponseCode() : "";
                    String cbsResMsg = !ObjectUtils.isEmpty(fetchTranscationIdResponse)
                            ? fetchTranscationIdResponse.getResponseMessage() : "";

                    log.info("Error on notifying CBS on mpesa fund disbursement event transaction! TRX_ID: {}, " +
                            "MpesaId: {}, CBS_ResponseCode: {}, CBS_ResponseMessage: {}", request.getTrxId(),
                            request.getPaymentReference(), cbsResCode, cbsResMsg);
                    spTransTempTable.setNotifyCbs(0);
                    crudTransactionController.updateTransTempTable(spTransTempTable);
                }
            }
        }
    }


}
