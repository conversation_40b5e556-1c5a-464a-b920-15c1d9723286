package com.tl.spotcash.agencybanking.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.MobileWebserviceStubsImpl;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.WebserviceStubsImpl;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.NewMember;
import com.tl.spotcash.agencybanking.custommodels.SOACbsAuthParams;
import com.tl.spotcash.agencybanking.custommodels.ValidateEventCodeRequest;
import com.tl.spotcash.agencybanking.custommodels.ValidateEventsResponse;
import com.tl.spotcash.agencybanking.custommodels.agent.AgentReportResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.custommodels.event.*;
import com.tl.spotcash.agencybanking.custommodels.member.GetMemberRequest;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service("spotcashCbsService")
//@Transactional
public class SpotcashCbsServiceImpl implements SpotcashCbsService {

	private final static Logger LOG = LoggerFactory.getLogger(SpotcashCbsServiceImpl.class);

	@Autowired
	Environment environment;

	@Autowired
	CrudTransactionController crudTransactions;

	@Override
	public CbsAccountsResponseMapper fetchCustomerAccounts(String identifierValue, String cbs_client_id) {
		CbsAccountsResponseMapper accountsResponseMapper = new CbsAccountsResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

			if (null != cbsClientConfigData) {
				if(cbsClientConfigData.getUseMobileBanking() == 1){
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
					MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(), environment, crudTransactions, cbsClientConfigData.getHostUrl(),
							new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					accountsResponseMapper = stubsresults.spotcash_cbs_accounts_call(identifierValue);
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(), environment, crudTransactions, cbsClientConfigData.getHostUrl(),
							new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					accountsResponseMapper = stubsresults.spotcash_cbs_accounts_call(identifierValue);
				}

			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", cbs_client_id);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................IDNumber/MSISDN " + identifierValue);
			LOG.error("An error occurred during accounts retrieval for CBS_client_ID " + cbs_client_id + " | error thrown \n" + ex);
		}
		return accountsResponseMapper;
	}

	@Override
	public CbsTransactionsResponseMapper fetchTransactions(String cbs_client_id, String phoneNumber,
														   String startDate, String endDate,
														   String startTime, String endTime) {
		CbsTransactionsResponseMapper transactionsResponseMapper  = new CbsTransactionsResponseMapper();
		try {
			SpVpnclientsConfigs  cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

			if (null != cbsClientConfigData) {

				if(cbsClientConfigData.getUseMobileBanking() == 1){
					SpStoreUsers storeUser = crudTransactions.fetchStoreUser(phoneNumber, cbs_client_id);
					if(storeUser != null /*&& !ObjectUtils.isEmpty(storeUser.getAccountNumber())*/){
						SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
						MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
								cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),
								new BigInteger(cbs_client_id));
						stubsresults.authenticate_to_corebanking();
						transactionsResponseMapper = stubsresults.spotcash_cbs_transactions_call(phoneNumber, /*storeUser.getAccountNumber(),*/
								startDate, endDate, startTime, endTime);
					}
					else {
						LOG.info("FAILED TO FETCH TRANSACTIONS REPORT. AGENT WITH MSISDN:{} HAS NO ACCOUNT NUMBER CONFIGURED", phoneNumber);
						return transactionsResponseMapper;
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),
							new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					transactionsResponseMapper = stubsresults.spotcash_cbs_transactions_call(phoneNumber,
							startDate, endDate, startTime, endTime);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations" +
						" for client with client_id {} ", cbs_client_id);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................phoneNumber " + phoneNumber);
			LOG.error("An error occurred during transactions retrieval for CBS_client_ID " + cbs_client_id
					+ " | error thrown \n" + ex);
		}
		return transactionsResponseMapper;
	}


	@Override
	public NewMember insertNewMember(HashMap<String, String> requestMap) {
		NewMember newMember  = new NewMember();
		try {
			LOG.info("New Member To Register ", requestMap );
			SpAgentStores agentStoreData = crudTransactions.fetchAgentStoreData(requestMap.get("deviceId"), requestMap.get("agentPhone"));
			//LOG.info("Agent " + agentStoreData);
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(agentStoreData.getClientId());
			if (agentStoreData!=null){
				if (null != cbsClientConfigData.getApiParams()) {

					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(),
							authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),agentStoreData.getClientId());
					stubsresults.authenticate_to_corebanking();

					newMember = stubsresults.spotcash_cbs_new_registration_call(requestMap);

				}
				else{
					LOG.error("DIDN'T FIND AGENT STORE WITH DEVICE ID AND PHONE NUMBER OF "+requestMap.get("deviceId")+" and "+requestMap.get("agentPhone"));
				}
			}
			else{
				LOG.error("AGENT STORE NOT FOUND");
			}


		} catch (Exception ex) {
			LOG.error("An error occurred during insertNewMember | error thrown \n" + ex);
		}
		return newMember;
	}

	public  String formatPhoneNumber(String msisdn) {
		String start_char = String.valueOf(msisdn.charAt(0));

		int msisdn_length = msisdn.length();
		System.out.println("Starting char" + start_char);
		System.out.println("MSISDN Length" + msisdn_length);

		if (start_char.equals("+") && msisdn_length == 13) {
			msisdn = msisdn.substring(4);
		} else if (start_char.equals("2") && msisdn_length == 12) {
			msisdn = msisdn.substring(3);
		} else if (start_char.equals("0") && msisdn_length == 10) {
			msisdn = msisdn.substring(1);
		} else if (start_char.equals("7") && msisdn_length == 9) {
			msisdn = msisdn;
		} else {
			return "0";
		}
		return msisdn;
	}

	@Override
	public AgentReportResponseMapper fetchAgentReport(String deviceId, String phoneNo, String date_from, String date_to, String clientId) {
		AgentReportResponseMapper agentReportResponseMapper  = new AgentReportResponseMapper();
		try {
			SpAgentStores agentStoreData = crudTransactions.fetchAgentStoreData(deviceId, "254" + phoneNo);

			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(agentStoreData.getAgentId());

			if (null != cbsClientConfigData.getApiParams()) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment);
				stubsresults.authenticate_to_corebanking();
				agentReportResponseMapper = stubsresults.spotcash_cbs_agent_report_call("254" + phoneNo, date_from, date_to);
			}

		} catch (Exception ex) {
			LOG.error("An error occurred during fetchAgentReport | error thrown \n" + ex);
		}
		return agentReportResponseMapper;
	}

    @Override
    public ValidateAccountResponseMapper validateCustomerAccounts(String accountNumber, String cbs_client_id) {
		ValidateAccountResponseMapper validateAccountResponseMapper  = new ValidateAccountResponseMapper();
        try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

            if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				if(cbsClientConfigData.getUseMobileBanking() == 1){
					MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(), environment, crudTransactions, cbsClientConfigData.getHostUrl(),
							new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					validateAccountResponseMapper = stubsresults.spotcash_validate_account(accountNumber);
				}
				else{
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(), environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					validateAccountResponseMapper = stubsresults.spotcash_validate_accounts_call(accountNumber);
				}

            } else {
                LOG.info(".............CLIENT CONFIGS IS NULL.............");
                LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", cbs_client_id);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            LOG.info("...........................Account Number " + accountNumber);
            LOG.error("An error occurred during account verification for CBS_client_ID " + cbs_client_id + " | error thrown \n" + ex);
        }
        return validateAccountResponseMapper;
    }

	@Override
	public FetchImageResponseMapper fetchMemberImage(String identifier, String cbs_client_id) {
		FetchImageResponseMapper fetchImageResponseMapper  = new FetchImageResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

			if (null != cbsClientConfigData) {
				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				if(cbsClientConfigData.getUseMobileBanking() == 1) {
					MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					fetchImageResponseMapper = stubsresults.spotcash_validate_accounts_call(identifier, "");
				}
				else{
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					fetchImageResponseMapper = stubsresults.fetch_member_image_call(identifier);
				}
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", cbs_client_id);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................Identifier(ID/MSISDN/Account Number) " + identifier);
			LOG.error("An error occurred during fetch image for CBS_client_ID " + cbs_client_id + " | error thrown \n" + ex);
		}
		return fetchImageResponseMapper;
	}

	@Override
	public FetchImageResponseMapper fetchMemberImage(String identifierValue, String cbs_client_id, String identifier) {
		FetchImageResponseMapper fetchImageResponseMapper  = new FetchImageResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

			if (null != cbsClientConfigData) {
				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				if(cbsClientConfigData.getUseMobileBanking() == 1) {
					MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					fetchImageResponseMapper = stubsresults.spotcash_validate_accounts_call(identifierValue, identifier);
				}
				else{
					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,
							crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
					stubsresults.authenticate_to_corebanking();
					fetchImageResponseMapper = stubsresults.fetch_member_image_call(identifierValue);
				}
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", cbs_client_id);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................Identifier(ID/MSISDN/Account Number) " + identifierValue);
			LOG.error("An error occurred during fetch image for CBS_client_ID " + cbs_client_id + " | error thrown \n" + ex);
		}
		return fetchImageResponseMapper;
	}

	@Override
	public CbsResponseMapper lockCustomerAccount(String accountNumber, String idNumber, String clientId) {
		CbsResponseMapper cbsResponseMapper  = new CbsResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(clientId));

			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
						cbsClientConfigData.getHostUrl(), environment,
						crudTransactions,cbsClientConfigData.getHostUrl(), new BigInteger(clientId));
				stubsresults.authenticate_to_corebanking();
				cbsResponseMapper = stubsresults.spotcash_lock_account_call(accountNumber, idNumber);
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", clientId);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................Lock Account Number: " + accountNumber+ " Id Number: "+idNumber);
			LOG.error("An error occurred during locking account for CBS_client_ID " + clientId + " | error thrown \n" + ex);
		}
		return cbsResponseMapper;
	}

	@Override
	public CbsNonMemberAccountsResponseMapper fetchNonMemberAccounts(String cbs_client_id) {
		CbsNonMemberAccountsResponseMapper nmaccountsResponseMapper = new CbsNonMemberAccountsResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(cbs_client_id));

			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
						cbsClientConfigData.getHostUrl(), environment,
						crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(cbs_client_id));
				stubsresults.authenticate_to_corebanking();
				nmaccountsResponseMapper = stubsresults.spotcash_cbs_nmaccounts_call(cbs_client_id);
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", cbs_client_id);
			}

		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................CLIENTID " + cbs_client_id);
			LOG.error("An error occurred during accounts retrieval for CBS_client_ID " + cbs_client_id + " | error thrown \n" + ex);
		}
		return nmaccountsResponseMapper;
	}

	@Override
	public ReversalResponseMapper reverseTransaction(SpTransTempTable trx, SpTransTempTable originalTransaction) {
		ReversalResponseMapper responseMapper = new ReversalResponseMapper();
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(trx.getClientId());

			if (null != cbsClientConfigData) {
				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
						cbsClientConfigData.getHostUrl(), environment,
						crudTransactions,cbsClientConfigData.getHostUrl(),originalTransaction.getClientId());
				stubsresults.authenticate_to_corebanking();
				responseMapper = stubsresults.reverse_cbs_transaction(trx, originalTransaction);
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check SP_APP_CLIENT_CONFIGS table for client configurations for client with client_id {} ", trx.getClientId());
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during reverse transaction for CBS Client ID " + trx.getClientId() + " | error thrown \n" + ex);
		}
		return responseMapper;
	}

	@Override
	public GetMemberCbsResponse getMember(GetMemberRequest request) {
		GetMemberCbsResponse getMemberCbsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {

				if(cbsClientConfigData.getUseSpotpay() == 1) {
					if(!ObjectUtils.isEmpty(cbsClientConfigData.getSpotpayOrgId())){
						Map<String, String> requestBody = new HashMap<>();
						requestBody.put("orgId", cbsClientConfigData.getSpotpayOrgId());
						requestBody.put("idNumber", request.getIdNumber());
						requestBody.put("eventId", request.getEventId());

						ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
								cbsClientConfigData.getHostUrl() + "/events/getMember", requestBody, "");

						if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
							getMemberCbsResponse = new Gson().fromJson(responseEntity.getBody(), GetMemberCbsResponse.class);
						}
					}
					else {
						LOG.error("SPOTPAY ORG ID HAS NOT YET BEEN CONFIGURED FOR CLIENT ID {}. FAILED TO GET MEMBER", request.getClientId());
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					getMemberCbsResponse = stubsresults.getMember(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................Id Number " + request.getIdNumber());
			LOG.error("An error occurred during fetch image for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return getMemberCbsResponse;
	}


	@Override
	public GetMemberCbsResponse getMemberImage(GetMemberRequest request) {
		GetMemberCbsResponse getMemberCbsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {
				if(cbsClientConfigData.getUseSpotpay() == 1) {
					if(!ObjectUtils.isEmpty(cbsClientConfigData.getSpotpayOrgId())){
						Map<String, String> requestBody = new HashMap<>();
						requestBody.put("orgId", cbsClientConfigData.getSpotpayOrgId());
						requestBody.put("idNumber", request.getIdNumber());
						requestBody.put("eventId", request.getEventId());

						ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
								cbsClientConfigData.getHostUrl() + "/events/getMember", requestBody, "");

						if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
							getMemberCbsResponse = new Gson().fromJson(responseEntity.getBody(), GetMemberCbsResponse.class);
						}
					}
					else {
						LOG.error("SPOTPAY ORG ID HAS NOT YET BEEN CONFIGURED FOR CLIENT ID {}. FAILED TO GET MEMBER", request.getClientId());
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					getMemberCbsResponse = stubsresults.getMemberImage(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.info("...........................Id Number " + request.getIdNumber());
			LOG.error("An error occurred during fetch image for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return getMemberCbsResponse;
	}


	@Override
	public GetEventsCbsResponse getEvents(GetEventsRequest request) {
		GetEventsCbsResponse getEventsrCbsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {

				if(cbsClientConfigData.getUseSpotpay() == 1) {
					if(!ObjectUtils.isEmpty(cbsClientConfigData.getSpotpayOrgId())){
						Map<String, String> requestBody = new HashMap<>();
						requestBody.put("orgId", cbsClientConfigData.getSpotpayOrgId());

						ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
								cbsClientConfigData.getHostUrl() + "/events/getEvents", requestBody, "");

						if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
							getEventsrCbsResponse = new Gson().fromJson(responseEntity.getBody(), GetEventsCbsResponse.class);
						}
					}
					else {
						LOG.error("SPOTPAY ORG ID HAS NOT YET BEEN CONFIGURED FOR CLIENT ID {}. FAILED TO GET MEMBER", request.getClientId());
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					getEventsrCbsResponse = stubsresults.getEvents(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching events for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return getEventsrCbsResponse;
	}




	@Override
	public ValidateEventsResponse validateEventCode (ValidateEventCodeRequest request) {
		ValidateEventsResponse getValidateEventsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(request.getClientId());

			if (null != cbsClientConfigData) {
				if(cbsClientConfigData.getUseSpotpay() == 1) {
					if(!ObjectUtils.isEmpty(cbsClientConfigData.getSpotpayOrgId())){
						Map<String, String> requestBody = new HashMap<>();
						requestBody.put("idNumber", request.getIdNumber());
						requestBody.put("eventId", request.getEventId());
						requestBody.put("eventCode", request.getEventCode());
						requestBody.put("phoneNumber", request.getPhoneNumber());
						requestBody.put("devicePhoneNo", request.getAgentPhoneNumber());

						ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
								cbsClientConfigData.getHostUrl() + "/events/validateEventCode", requestBody, "");

						if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
							getValidateEventsResponse = new Gson().fromJson(responseEntity.getBody(), ValidateEventsResponse.class);
						}
					}
					else {
						LOG.error("SPOTPAY ORG ID HAS NOT YET BEEN CONFIGURED FOR CLIENT ID {}. FAILED TO GET MEMBER", request.getClientId());
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					getValidateEventsResponse = stubsresults.validateEventCode(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching events for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return getValidateEventsResponse;
	}



	@Override
	public EventRegistrationCbsResponse registerMemberToEvent(RegisterMemberRequest request) {
		EventRegistrationCbsResponse cbsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {
				if(cbsClientConfigData.getUseSpotpay() == 1) {
					Map<String, String> requestBody = new HashMap<>();
					requestBody.put("idNumber", request.getIdNumber());
					requestBody.put("eventId", request.getEventId());
					requestBody.put("authenticationMode", request.getAuthenticationMode());
					requestBody.put("agentDevicePhone", crudTransactions.fetchStoreUserMsisdn(request.getStoreUserId()));
					requestBody.put("disbursementMethod", request.getDisbursementMethod());

					ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
							cbsClientConfigData.getHostUrl() + "/events/registerMember", requestBody, "");

					if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
						cbsResponse = new Gson().fromJson(responseEntity.getBody(), EventRegistrationCbsResponse.class);
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),
							new BigInteger(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					cbsResponse = stubsresults.registerMemberToEvent(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching events for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return cbsResponse;
	}

	@Override
	public EventRegistrationCbsResponse registerNonMemberToEvent(RegisterNonMemberRequest request) {
		EventRegistrationCbsResponse cbsResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {
				if(cbsClientConfigData.getUseSpotpay() == 1) {
					Map<String, String> requestBody = new HashMap<>();
					requestBody.put("name", request.getName());
					requestBody.put("idNumber", request.getIdNumber());
					requestBody.put("phoneNumber", request.getPhoneNumber());
					requestBody.put("gender", request.getGender());
					requestBody.put("agentDevicePhone", crudTransactions.fetchStoreUserMsisdn(request.getStoreUserId()));
					requestBody.put("eventId", request.getEventId());
					requestBody.put("image", request.getImage());
					requestBody.put("jobGroupId", request.getJobGroupId());

					ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
							cbsClientConfigData.getHostUrl() + "/events/registerNonMember", requestBody, "");

					if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
						cbsResponse = new Gson().fromJson(responseEntity.getBody(), EventRegistrationCbsResponse.class);
					}
				}
				else{
					SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

					WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
							cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),
							new BigInteger(request.getClientId()));
					stubsresults.authenticate_to_corebanking();
					cbsResponse = stubsresults.registerNonMemberToEvent(request);
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching events for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return cbsResponse;
	}

	@Override
	public GetJobGroupResponse getJobGroups(GetJobGroupRequest request) {
		GetJobGroupResponse getJobGroupResponse = null;
		try {
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));

			if (null != cbsClientConfigData) {

				if(!ObjectUtils.isEmpty(cbsClientConfigData.getSpotpayOrgId())){
					Map<String, String> requestBody = new HashMap<>();
					requestBody.put("orgId", cbsClientConfigData.getSpotpayOrgId());

					ResponseEntity<String> responseEntity = SharedFunctions.postJSONRequest(
							cbsClientConfigData.getHostUrl() + "/events/getJobGroups", requestBody, "");

					if(responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
						getJobGroupResponse = new Gson().fromJson(responseEntity.getBody(), GetJobGroupResponse.class);
					}
				}
				else {
					LOG.error("SPOTPAY ORG ID HAS NOT YET BEEN CONFIGURED FOR CLIENT ID {}. FAILED TO GET JOB GROUPS", request.getClientId());
				}
			}
			else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("CBS client data is not provided, check CB_CLIENT_CONFIGS table for client configurations for client with client_id {} ", request.getClientId());
			}

		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching job group for CBS_client_ID " + request.getClientId() + " | error thrown \n" + ex);
		}
		return getJobGroupResponse;
	}



	//method handling response from cbs for the available redeemable items
	@Override
	public RedeemItemCbsResponse getRedeemItems(RedeemItemsRequest request) {
		RedeemItemCbsResponse redeemItemCbsResponse = null;
		try{
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));
			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
				stubsresults.authenticate_to_corebanking();
				redeemItemCbsResponse = stubsresults.redeemableItems(request);
			}else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching items to redeem" + ex);
		}
		return redeemItemCbsResponse;
	}


	//method handling response from cbs for customer's selected items to be redeemed
	public GetCustomerRewardsCbsResponse insertCustomerRewards(RedeemItem redeemItem, String clientId, String eventId, String customerId, SpVpnclientsConfigs cbsClientConfigData, String transactionId) {

		GetCustomerRewardsCbsResponse getCustomerRewardsCbsResponse = null;
		try {
			if (null != cbsClientConfigData) {
				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);
				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(), environment, crudTransactions, cbsClientConfigData.getHostUrl(), new BigInteger(clientId));
				stubsresults.authenticate_to_corebanking();
				getCustomerRewardsCbsResponse = stubsresults.selectedCustomerRewards(redeemItem, clientId, eventId, customerId, transactionId);
			} else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred while redeeming customer rewards" + ex);
		}
		return getCustomerRewardsCbsResponse;
	}


	//method handling cbs response for all redeemed customer rewards fetched
	public GetRedeemedCustomerRewardsCbsResponse getAllRedeemedRewards(RedeemedCustomerRewardsRequest request) {
		GetRedeemedCustomerRewardsCbsResponse getRedeemedCustomerRewardsCbsResponse = null;
		try{
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));
			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
				stubsresults.authenticate_to_corebanking();
				getRedeemedCustomerRewardsCbsResponse = stubsresults.redeemedCustomerRewards(request);
			}else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching items to redeem" + ex);
		}
		return getRedeemedCustomerRewardsCbsResponse;
	}
	public FetchMemberByMpesaCbsResponse fetchMemberByMpesaCbsResponse(FetchMpesaMembersRequest request, String clientId) {
		FetchMemberByMpesaCbsResponse fetchMemberByMpesaCbsResponse = null;
		try{
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(clientId));
			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(clientId));
				stubsresults.authenticate_to_corebanking();
				fetchMemberByMpesaCbsResponse = stubsresults.fetchMemberByMpesaCbsResponse(request);
			}else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching items to redeem" + ex);
		}
		return fetchMemberByMpesaCbsResponse;
	}
	public FetchTranscationIdResponse fetchTranscationIdResponse(FetchTranscationIdRequest request, String clientId) {
		FetchTranscationIdResponse fetchTranscationIdResponse = null;
		try{
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(clientId));
			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(clientId));
				stubsresults.authenticate_to_corebanking();
				fetchTranscationIdResponse = stubsresults.fetchTranscationIdResponse(request);
			}else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching items to redeem" + ex);
		}
		return fetchTranscationIdResponse;
	}
	public GetServiceChargeCbsResponse getServiceCharge(ServiceChargeRequest request) {
		GetServiceChargeCbsResponse getServiceChargeResponse = null;
		try{
			SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(request.getClientId()));
			if (null != cbsClientConfigData) {

				SOACbsAuthParams authParams = new ObjectMapper().readValue(cbsClientConfigData.getApiParams(), SOACbsAuthParams.class);

				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(), cbsClientConfigData.getHostUrl(),environment,crudTransactions,cbsClientConfigData.getHostUrl(),new BigInteger(request.getClientId()));
				stubsresults.authenticate_to_corebanking();
				getServiceChargeResponse = stubsresults.getServiceChargeResponse(request);
			}else {
				LOG.info(".............CLIENT CONFIGS IS NULL.............");
				LOG.warn("");
			}
		}
		catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("An error occurred during fetching items to redeem" + ex);
		}
		return getServiceChargeResponse;
	}


	@Override
	public String POSTRequestWithSession(String URL, Object requestBody, String sessionId) {
		try {
			HttpHeaders requestHeaders = new HttpHeaders();
			requestHeaders.add("Cookie", "JSESSIONID=" + sessionId);
			RestTemplate restTemplate = new RestTemplate();
			restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
			HttpEntity<Object> request = new HttpEntity<>(requestBody, requestHeaders);
			String response = restTemplate.postForObject(URL, request, String.class);
			return response;
		} catch (Exception ex) {
			ex.printStackTrace();
			LOG.error("Error Occured:" + ex.getMessage());
			return null;
		}
	}

}
