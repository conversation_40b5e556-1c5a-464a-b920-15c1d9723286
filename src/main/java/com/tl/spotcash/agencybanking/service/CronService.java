package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.ResponseCodes;
import com.tl.spotcash.agencybanking.entity.SpAgencyPendingTransactions;
import com.tl.spotcash.agencybanking.entity.SpAgencyReverseTransactions;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.threads.ProcessReversalThread;
import com.tl.spotcash.agencybanking.xiputils.SpotcashTrxDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by mwendwakelvin on 22/08/2018.
 */
@RestController
public class CronService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CronService.class);

    @Autowired
    private Environment environment;

    @Autowired
    CrudTransactionController crudTransactionController;

    @Autowired
    SpotcashCbsService cbsService;

    @Autowired
    TaskExecutor taskExecutor;

    // poll for reversal transactions
    //@Scheduled(fixedDelay = 3000)
    public void listReversalTransactions() {
        //LOGGER.info("Initiating Reversal transaction");
        BigInteger count = new BigInteger("" + environment.getProperty("datasource.spotcash.reverseTransactionCount"));
        List<SpAgencyReverseTransactions> trxTr = crudTransactionController.fetchReverseTransactions(count);
        if (trxTr.isEmpty()) {
            //LOGGER.info("Reversal: No records found!");
        }

        int delay = Integer.parseInt("" + environment.getProperty("datasource.spotcash.reverseTransactionDelay"));
        Date date = new Date();
        long time = date.getTime();
        Timestamp now = new Timestamp(time);
        Timestamp delayed_time = new Timestamp(time - TimeUnit.MINUTES.toMillis(delay));
        //LOGGER.info("compare is  " + now.compareTo(delayed_time) + "  delayed is  " + delayed_time);

        for (SpAgencyReverseTransactions trx : trxTr) {
            if(trx.getReverseCount() == null || trx.getReverseCount().equals("")){
                processReversal(trx, now);
            }
            else{
                if(trx.getReverseCount().equals(new BigInteger("0"))){
                    processReversal(trx, now);
                }
                else{
                    if (delayed_time.compareTo(trx.getLastReverseTime()) == 1) {
                        processReversal(trx, now);
                    }
                }
            }
        }
    }

    private void processReversal(SpAgencyReverseTransactions trx, Timestamp now){
        try {
            LOGGER.info("Found Reversal Transaction: "+trx.getTrxId());
            SpTransTempTable transTempTable = crudTransactionController.getTransaction(trx.getTrxId());
            if (transTempTable != null) {
                SpTransTempTable originalTransaction = crudTransactionController.getTransaction(trx.getOriginalTxnId());
                if (originalTransaction != null) {
                    transTempTable.setReverse(BigInteger.ONE);
                        transTempTable.setReverseCount(trx.getReverseCount().add(new BigInteger("1")));
                    transTempTable.setLastReverseTime(now);
                    crudTransactionController.updateTransTable(transTempTable);
                    taskExecutor.execute(new ProcessReversalThread(transTempTable, environment, crudTransactionController, cbsService, originalTransaction));
                }
                else{
                    LOGGER.info("Invalid Reversal. Original Record missing in TRANS TEMP TBL.");
                    transTempTable.setReverse(BigInteger.ONE);
                    transTempTable.setReverseCount(trx.getReverseCount().add(new BigInteger("1")));
                    transTempTable.setLastReverseTime(now);
                    crudTransactionController.updateTransTable(transTempTable);
                }
            } else {
                LOGGER.info("Invalid Reversal. Record missing in TRANS TEMP TBL.");
            }
        } catch (Exception ex) {
            LOGGER.error("Error in Reversal Transaction" + ex.getMessage());
            ex.printStackTrace();
        }
    }

    // poll for pending transactions
    //@Scheduled(fixedDelay = 3000)
    public void failPendingTransactions() {
        //LOGGER.info("Initiating Fail Pending transaction");
        List<SpAgencyPendingTransactions> trxTr = crudTransactionController.fetchPendingTransactions();
        if (trxTr.isEmpty()) {
            //LOGGER.info("Fail Pending transaction: No records found!");
        }
        int delay = Integer.parseInt("" + environment.getProperty("datasource.spotcash.pendingTransactionDelay"));
        Date date = new Date();
        long time = date.getTime();
        Timestamp now = new Timestamp(time);
        Timestamp delayed_time = new Timestamp(time - TimeUnit.MINUTES.toMillis(delay));
        //LOGGER.info("compare is  " + now.compareTo(delayed_time) + "  delayed is  " + delayed_time);
        for (SpAgencyPendingTransactions trx : trxTr) {
            if (delayed_time.compareTo(trx.getRequestTime()) == 1) {
                try {
                    LOGGER.info("Found Pending Agency Transaction");
                    SpTransTempTable transTempTable = crudTransactionController.getTransaction(trx.getTrxId());
                    if (transTempTable != null) {
                        transTempTable.setRespCode(ResponseCodes.FAILED_REVERSIBLE_TRANSACTION.getResponsecode());
                        transTempTable.setOriginalTxnId(trx.getTrxId());
                        transTempTable.setDescription("No Response From CBS.");
                        transTempTable.setIntermediateStatus(new BigInteger("5"));
                        transTempTable.setTrxStatus(BigInteger.valueOf(2));
                        crudTransactionController.updateTransTable(transTempTable);
                    } else {
                        LOGGER.info("Invalid Pending Agency Transaction. Record missing in TRANS TEMP TBL.");
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error in Failing Pending Agency Transaction" + ex.getMessage());
                    ex.printStackTrace();
                }
            }
        }
    }
}
