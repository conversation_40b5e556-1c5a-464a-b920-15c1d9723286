package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Service
public class ReserveFundsConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReserveFundsConfiguration.class);

    private final JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;

    public ReserveFundsConfiguration(@Qualifier("primaryDS") DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.dataSource = dataSource;
    }

    public HashMap<String, String> executeSpResverseProc(SpTransTempTable transObj) {
        LOGGER.info("----------- INITIATION OF RESERVING FUNDS FOR TRANSACTION " + transObj.getTrxId() + " --------");
        HashMap<String, String> response = new HashMap<>();
        String errorMessage = "Error in reserving funds";

        try {
            SimpleJdbcCall jdbcCall = new SimpleJdbcCall(dataSource)
                    .withProcedureName("SP_RESERVE_FUNDS");

            MapSqlParameterSource inParams = new MapSqlParameterSource()
                    .addValue("p_trx_id", transObj.getTrxId())
                    .addValue("p_client_id", transObj.getClientId())
                    .addValue("p_service_id", transObj.getServiceId())
                    .addValue("p_amount", transObj.getAmount())
                    .addValue("p_commission", transObj.getSpotcashCommission())
                    .addValue("p_charge", transObj.getThirdpartyCharge());

            Map<String, Object> out = jdbcCall.execute(inParams);

            String code = (String) out.get("P_RESPONSE_CODE");
            String message = (String) out.get("P_RESPONSE_MSG");

            response.put("responseCode", code);
            response.put("responseMessage", message);
            LOGGER.info("Success reverse funds");

        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error occurred during reservation of funds", e);
            response.put("responseCode", "01");
            response.put("responseMessage", errorMessage);
        }

        return response;
    }
}
