package com.tl.spotcash.agencybanking.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

@Service
public class OtpService {
    @Autowired
    CrudTransactionController crudTransactions;

    private static final Logger LOGGER = LoggerFactory.getLogger(OtpService.class);
    Calendar currenttime = Calendar.getInstance();

    //cache based on username and OPT MAX 8
    private static final Integer EXPIRE_MINS = 1;
    private LoadingCache<String, Integer> otpCache;
    private LoadingCache<String, String> keyClientMapCache;

    final Environment environment;

    public OtpService(Environment environment) {
        super();
        this.environment = environment;

        String otpTimeout = this.environment.getRequiredProperty("app.otpTimeout");
        otpCache = CacheBuilder.newBuilder().
                expireAfterWrite(Integer.parseInt(otpTimeout), TimeUnit.MINUTES).build(new CacheLoader<String, Integer>() {
                    public Integer load(String key) {
                        return 0;
                    }
                });
        keyClientMapCache = CacheBuilder.newBuilder().
                expireAfterWrite(Integer.parseInt(otpTimeout), TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
                    public String load(String key) {
                        return "";
                    }
                });
    }

    //This method is used to push the opt number against Key. Rewrite the OTP if it exists
    //Using user id  as key
    public int generateOTP(String key) {
        SecureRandom random = new SecureRandom();
        int otp = 1000 + random.nextInt(9000);
        otpCache.put(key, otp);
        LOGGER.debug("*** OTP INITIALLY SAVED IN CACHE. OTP :: {} ***", otp);
        LOGGER.debug("*** ENTRIES IN OTP_CACHE AS MAP :: {} ***", otpCache.asMap());
        return otp;
    }

    public int generateOTP(String key, String clientId) {
        int otp = generateOTP(key);
        keyClientMapCache.put(key, clientId);
        return otp;
    }

    //This method is used to return the OPT number against Key->Key values is username
    public int getOtp(String key) {
        try {
            return otpCache.get(key);
        } catch (Exception e) {
            return 0;
        }
    }

    public String getClientId(String key) {
        try {
            return keyClientMapCache.get(key);
        } catch (Exception e) {
            return "";
        }
    }

    public static String generatePin() {
        SecureRandom random = new SecureRandom();
        int pin_ = 1000 + random.nextInt(9000);
        return String.valueOf(pin_);
    }

    //This method is used to clear the OTP catched already
    public void clearOTP(String key) {
        otpCache.invalidate(key);
    }

    public void clearKeyClientMap(String key) {
        keyClientMapCache.invalidate(key);
    }

    public ConcurrentMap<String, Integer> getAllEntries(){
        return otpCache.asMap();
    }

    public HashMap<String, String> updateOtpEntries(String id, Long clientId, Long agentStoreId, String ipAddress) {
        int otpRetryCount = 3; // Default OTP retry count
        int otpRetriesLeft;
        HashMap<String, String> otpResult = new HashMap<>();

        // Fetching OTP retries from SP_AGENTS using the agent_store_id
        Optional<SpAgentStores> agentStoreOutcome = Optional.ofNullable(crudTransactions.fetchAgentWithStoreId(BigInteger.valueOf(agentStoreId)));
        if(agentStoreOutcome.isPresent()) {
            SpAgentStores agentStore = agentStoreOutcome.get();
            BigInteger agentId = agentStore.getAgentId();

            Optional<SpAgents> spAgentResult = Optional.ofNullable(crudTransactions.fetchAgentWithId(String.valueOf(agentId)));
            if (spAgentResult.isPresent()) {
                SpAgents spAgent = spAgentResult.get();
                otpRetryCount = spAgent.getOtpRetries(); // update default otp retries with the configured otp retires
                otpResult.put("otpRetryCount", String.valueOf(otpRetryCount));
                LOGGER.info("OTP RETRIES COUNT FROM SP_AGENTS: {}", otpRetryCount);
            } else {
                LOGGER.warn("AGENT WITH AGENT_ID :: {}, DOESN'T EXIST  ", agentId);
            }
        }
        else {
            LOGGER.warn("AGENT_STORE WITH AGENT_STORE_ID :: {}, DOESN'T EXIST  ", agentStoreId);
        }

        LOGGER.info("Updating otpLog - nationalId :: {}", id);
        Long storeUserid = null;
        Optional<SpAgencyOtpLog> spAgencyOtpLogResult = crudTransactions.fetchOtpLogByNationalIdNumber(id);
        if (spAgencyOtpLogResult.isPresent()) {
            SpAgencyOtpLog spAgencyOtpLog = spAgencyOtpLogResult.get();
            storeUserid = spAgencyOtpLog.getStoreUserId();
            Integer otpRetries = spAgencyOtpLog.getRetryCount();
            ++otpRetries;
            otpRetriesLeft = Math.max((otpRetryCount - otpRetries), 0);
            LOGGER.info("OTP RETRY COUNT: {}, CUSTOMER OTP COUNT: {}, OTP RETRIES LEFT: {}", otpRetryCount, otpRetries, otpRetriesLeft);
            otpResult.put("customerOtpRetries", String.valueOf(otpRetries));
            otpResult.put("otpRetriesLeft", String.valueOf(otpRetriesLeft));
            LOGGER.info("otpResult: {}", otpResult);

            if (otpRetries < otpRetryCount) {
                spAgencyOtpLog.setRetryCount(otpRetries);
                // also set for agent
                // Increment the storeUserRetry count based on its previous count
                spAgencyOtpLog.setStoreUserRetryCount(spAgencyOtpLog.getStoreUserRetryCount() + 1);
                spAgencyOtpLog.setTimeUpdated(new Timestamp(currenttime.getTimeInMillis()));
                crudTransactions.save(spAgencyOtpLog);
            } else {
                spAgencyOtpLog.setRetryCount(otpRetries);
                // Increment the storeUserRetry count based its previous count
                spAgencyOtpLog.setStoreUserRetryCount(spAgencyOtpLog.getStoreUserRetryCount() + 1);
                spAgencyOtpLog.setTimeUpdated(new Timestamp(currenttime.getTimeInMillis()));
                crudTransactions.save(spAgencyOtpLog);
                LOGGER.info("Otp retries exceeded, blacklisting... msisdn :: {}, nationalId :: {} | retryCount :: {} ", spAgencyOtpLog.getMsisdn(), id, otpRetries);
                crudTransactions.blacklistMsisdnForClient(spAgencyOtpLog.getMsisdn(), spAgencyOtpLog.getClientId(), spAgencyOtpLog.getStoreUserId(), ipAddress);
                clearOTP(id);
                clearKeyClientMap(id);
            }
        } else {
            LOGGER.warn("Agency otp log is missing for nationalId {} & clientId {}...", id, clientId);
        }

        LOGGER.info("CHECKING IF STORE_USER_ID :: {} SHOULD BE BLACKLISTED", storeUserid);
        //Fetch agentData using agentStoreID in order to check if the agent has exceeded the STORE_BLACKLIST_COUNT in table SP_AGENTS
        //blacklist the agent if the limit has been exceeded.
        Optional<SpAgentStores> agentStoreResult = Optional.ofNullable(crudTransactions.fetchAgentWithStoreId(BigInteger.valueOf(agentStoreId)));
        if(agentStoreResult.isPresent()){
            SpAgentStores agentStore = agentStoreResult.get();
            BigInteger agentId = agentStore.getAgentId();

            Optional<SpAgents> spAgentResult = Optional.ofNullable(crudTransactions.fetchAgentWithId(String.valueOf(agentId)));
            if(spAgentResult.isPresent()){
                SpAgents spAgent = spAgentResult.get();
                BigInteger storeBlacklistCount = spAgent.getStoreBlacklistCount();

                //Note: StoreBlacklistTime is stored as Hours in SpAgents
                Double storeBlacklistTimeInHours = spAgent.getStoreBlacklistTime();

                //Fetch all the count for all wrong OTP entries for the agent.
                Optional<SpStoreUsers> storeUserResult = Optional.ofNullable(crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(String.valueOf(agentStoreId)));
                if(storeUserResult.isPresent()){
                    SpStoreUsers spStoreUser = storeUserResult.get();
                    BigInteger storeUserId = spStoreUser.getStoreUserId();

                    //1 hour = 60 minutes = 60 × 60 seconds = 3600 seconds = 3600 × 1000 milliseconds = 3,600,000 ms
                    long storeBlacklistTimeInMs = Math.round(storeBlacklistTimeInHours * 3600 * 1000);

                    Timestamp timePeriod = new Timestamp(currenttime.getTimeInMillis() -  storeBlacklistTimeInMs);

                    int wrongOtpEntries = 0;

                    //Fetch all the wrong OTP entries entered by the agent within the specified period of time.
                    Optional<List<SpAgencyOtpLog>> spAgencyOtpLogResults =
                            crudTransactions.fetchWrongOtpLogEntriesUsingStoreUserId(String.valueOf(storeUserId),timePeriod);

                    if(spAgencyOtpLogResults.isPresent()){
                        List<SpAgencyOtpLog> spAgencyOtpLogs = spAgencyOtpLogResults.get();
                        // Counts all the wrong OTP entries entered by agent
                        for(SpAgencyOtpLog otpLogEntry  : spAgencyOtpLogs){
                            wrongOtpEntries += otpLogEntry.getStoreUserRetryCount();
                        }

                        //Blacklist Agent if the limit has been exceeded.
                        if(wrongOtpEntries >= Integer.parseInt(String.valueOf(storeBlacklistCount))){
                            //Blacklist the Agent
                            LOGGER.info("OTP RETRIES EXCEEDED, BLACKLISTING AGENT WITH... AGENT_ID :: {}, STORE_USER_ID :: {}, | Wrong OTP Entries :: {} ", agentId, storeUserId, wrongOtpEntries);
                            crudTransactions.blacklistStoreUserId(String.valueOf(storeUserId), ipAddress, clientId, spStoreUser.getContactMsisdn());
                            otpResult.put("agentBlacklisted", "true");
                            otpResult.put("agentWrongOtpEntries", String.valueOf(wrongOtpEntries));
                        } else{
                            //No need for blacklisting the agent
                            LOGGER.warn("NO NEED FOR BLACKLISTING STORE USER WITH STORE_USER_ID :: {}, WRONG OTP ENTRIES :: {}, LIMIT SET :: {}, TIME PERIOD IN HOURS :: {}", storeUserId, wrongOtpEntries, storeBlacklistCount, storeBlacklistTimeInHours);
                        }
                    } else{
                        LOGGER.info("THERE ARE NO LOGS FOR WRONG OTP ENTRIES ENTERED BY AGENT_ID :: {}, STORE_USER_ID :: {}, from DATE :: {} AND LATER.", agentId, storeUserId, timePeriod);
                    }
                } else{
                    LOGGER.warn("STORE USER WITH AGENT_STORE_ID :: {}, DOESN'T EXIST  ", agentStoreId);
                }
            } else {
                LOGGER.warn("AGENT WITH AGENT_ID :: {}, DOESN'T EXIST  ", agentId);
            }
        } else {
            LOGGER.warn("AGENT_STORE WITH AGENT_STORE_ID :: {}, DOESN'T EXIST  ", agentStoreId);
        }

        return otpResult;
    }

}
