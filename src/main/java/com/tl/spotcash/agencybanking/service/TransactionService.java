package com.tl.spotcash.agencybanking.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.SpotcashUtilities;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class TransactionService {
    @Autowired
    CrudTransactionController crudTransactions;
    @Autowired
    public OtpService otpService;
    @Autowired
    Environment environment;

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionService.class);
    Calendar currenttime = Calendar.getInstance();

    public Map<String, BigDecimal> getComissionServiceChargeData(BigInteger service_id, BigInteger client_id, BigDecimal amount) throws JsonProcessingException {
        SpServiceSubscriptions service_commissionData = null;
        Map<String, BigDecimal> tariffDetailsData = new HashMap<>();
        try {
            service_commissionData = crudTransactions.fetchServicesubscriptionData(service_id, client_id);
            LOGGER.info("Get commission service data: 2");
            if (service_commissionData == null) {
                LOGGER.info(" TRANSACTION PROCESSORS No tariff details information for service ID >> " + service_id + " & Client ID " + client_id);
                tariffDetailsData.put("commission", new BigDecimal("0"));
                tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
                tariffDetailsData.put("saccoCommission", new BigDecimal("0"));
            }
            else{
                List<SpTariffDetails> tariffDetail = crudTransactions.fetchTariffsDetails(service_commissionData.getTariffId(), amount);
                for (SpTariffDetails spTransData : tariffDetail) {
                    tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                    tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
                    tariffDetailsData.put("saccoCommission", spTransData.getSaccoCommission());
                }
                LOGGER.info("returning tariff details "+new ObjectMapper().writeValueAsString(tariffDetailsData));
                return tariffDetailsData;
            }
        }
        catch (Exception e){
            e.printStackTrace();
            tariffDetailsData.put("commission", new BigDecimal("0"));
            tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
            tariffDetailsData.put("saccoCommission", new BigDecimal("0"));
        }

        return tariffDetailsData;
    }

    public boolean checkSubscription(String serviceCode, String clientID) {
        Map<String,Object> response = new HashMap<>();
        if(serviceCode.equals("AGBAL")){
            return true;
        }else{
            try{
                SpServiceSubscriptions spServiceSubscriptions = crudTransactions.fetchServicesubscriptionData(serviceCode,new BigInteger(clientID));
                if(spServiceSubscriptions != null) {
                    return true;
                } else {
                    return false;
                }
            }catch (Exception e){
                return false;
            }
        }
    }

    //reserve in the amount account id
    public void updateUnclearedBalance(BigDecimal totalAmount,String accountid) {
        LOGGER.info("-------- UPDATING UNCLEARED BALANCES << totalcharge >> " + totalAmount + " << account id >>" + accountid);
        String reserveBalances = "update sp_accounts set avail_bal = (avail_bal - " + totalAmount + "), uncleared_bal = (uncleared_bal - " + totalAmount + "), LAST_UPDATED=systimestamp where id = '" + accountid + "'";
        crudTransactions.updateAccounts(reserveBalances);
    }

    public boolean deviceJourneyIsValidForTransaction(HttpServletRequest request, String requestBody) throws IOException {
        if (!environment.getRequiredProperty("datasource.spotcash.validateIp", Boolean.class)) {
            LOGGER.info("Ip check is off ...");
            return true;
        }
        HttpProcessorRequest requestParams = new SpotcashUtilities().returnMappedXipRequest(requestBody);
        String ipAddress = SharedFunctions.getRemoteIpAddress(request, environment);
        LOGGER.info("Validating device journey for ip :: {}", ipAddress);
        List<SpAgencyRequestLog> previousRequestLogs = crudTransactions
                .fetchPreviousRequestLogsLimitedToMins(ipAddress, 10L);
        LOGGER.info("Fetched previousRequestLogs -> {}", previousRequestLogs.size());
        if (previousRequestLogs.isEmpty()) {
            LOGGER.warn("No previous request logs found - suspicious... ");
            return false;
        } else if (previousRequestLogs.size() < 2) {
            // Exclude Agbal
            List<String> excludedTxnTypes = new ArrayList<>();
            excludedTxnTypes.add("AGBAL");//account balance
            excludedTxnTypes.add("EXTD");//other account deposit
            if (excludedTxnTypes.contains(requestParams.getRequestData().getTxnType())) {
                return true;
            } else {
                LOGGER.warn("Previous request logs are {}, min required are 2 - suspicious", previousRequestLogs.size());
                return false;
            }
        } else {
            LOGGER.info("validate the fetched logs");
            //To be made configurable
            List<String> excludedTxnTypes = new ArrayList<>();
            excludedTxnTypes.add("SDT");
            excludedTxnTypes.add("EXTD");//other account deposit
            excludedTxnTypes.add("AGBAL");//account balance
            if (excludedTxnTypes.contains(requestParams.getRequestData().getTxnType())) {
                // Excluded from pin and otp stages
                return true;
            } else {
                if (!hasGoneThroughOtpOrPinStages(previousRequestLogs)) {
                    LOGGER.warn("Previous request logs do not show OTP or Pin stages - suspicious");
                    return false;
                } else {
                    return true;
                }
            }
        }
    }

    private boolean hasGoneThroughOtpOrPinStages(List<SpAgencyRequestLog> requestLogs) {
        boolean hasGenerateOtp = false;
        boolean hasValidateOtp = false;
        boolean hasPin = false;
        for (SpAgencyRequestLog requestLog : requestLogs) {
            if (requestLog.getUrl().toLowerCase().contains("generateotp") || requestLog.getUrl().toLowerCase().contains("initiateotp")) {
                hasGenerateOtp = true;
            }
            if (requestLog.getUrl().toLowerCase().contains("validateotp") || requestLog.getUrl().toLowerCase().contains("verifyotp")
                    || requestLog.getUrl().toLowerCase().contains("confirmotp")) {
                hasValidateOtp = true;
            }
            if (requestLog.getUrl().toLowerCase().contains("ispincorrect") || requestLog.getUrl().toLowerCase().contains("verifycustomerpin")) {
                hasPin = true;
            }
        }
        return (hasGenerateOtp && hasValidateOtp) || hasPin;
    }

    public boolean agentIsBlacklisted(String requestParams) {
        try {
            LOGGER.info("Checking if agent is blacklisted...");
            HttpProcessorRequest request = new SpotcashUtilities().returnMappedXipRequest(requestParams);

            //This if statement is for compatibility issues in order to support older versions of the Agency app,
            // which don't send the agentStoreId in the request which is used to check if an agent store is blacklisted,
            // Thus it returns false to disable the feature of blacklisting agents as it might throw a null Exception.
            if(request.getRequestData().getAgentSid() == null) return false;

            Long agentStoreId = Long.parseLong(request.getRequestData().getAgentSid());
            LOGGER.info("Blacklist check for agentStoreId :: {}, clientId :: {}", agentStoreId, request.getHeader().getClientId());

            Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(request.getRequestData().getAgentSid()));

            if(optionalStoreUser.isPresent()){
                SpStoreUsers storeUser = optionalStoreUser.get();
                String storeUserId = String.valueOf(storeUser.getStoreUserId());
                return crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId));
            } else{
                LOGGER.error("Failed to fetch an agent with agentStoreId :: {}, in SP_STORE_USERS", agentStoreId);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean agentIsBlacklisted(String agentStoreId, String clientId) {
        LOGGER.warn("CHECKING IF AGENT IS BLACKLISTED, agentStoreId :: {}, clientId :: {}", agentStoreId, clientId);

        Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(agentStoreId));

        if(optionalStoreUser.isPresent()){
            SpStoreUsers storeUser = optionalStoreUser.get();
            String storeUserId = String.valueOf(storeUser.getStoreUserId());
            return crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId));
        } else{
            LOGGER.error("FAILED TO FETCH AN AGENT WITH agentStoreId :: {}, in SP_STORE_USERS", agentStoreId);
        }
        return false;
    }

    public boolean customerIsBlacklisted(String msisdn, String clientId) {
        LOGGER.warn("CHECKING IF CUSTOMER IS BLACKLISTED, MSISDN :: {}, CLIENT_ID :: {}", msisdn, clientId);
        return crudTransactions.isBlacklisted(msisdn, Long.parseLong(clientId));
    }

    public boolean customerIsBlacklisted(String requestParams) {
        try {
            LOGGER.info("Checking if customers is blacklisted...");
            HttpProcessorRequest request = new SpotcashUtilities().returnMappedXipRequest(requestParams);
            LOGGER.info("Blacklist check for msisdn :: {}, clientId :: {}", request.getHeader().getCustomerMsisdn(), request.getHeader().getClientId());
            return crudTransactions.isBlacklisted(request.getHeader().getCustomerMsisdn(), Long.parseLong(request.getHeader().getClientId()));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    public void archiveRequestLogs(String ipAddress) {
        ForkJoinPool.commonPool().execute(() -> {
            crudTransactions.archiveRequestLogs(ipAddress);
        });
    }

    public boolean validateAgentOTP(Map<String, String> request){
        int otp = Integer.parseInt(request.get("otp"));
        String msisdn = request.get("msisdn");
        LOGGER.info("Validating agent OTP. OTP: {}, StoreUser MSISDN: {}",otp,msisdn);
        int serverOtp = otpService.getOtp(msisdn);
        if (otp == serverOtp) {
            otpService.clearOTP(msisdn);
            otpService.clearKeyClientMap(msisdn);
            return true;
        }
        return false;
    }

    public boolean checkUniqueDeviceId(String storeId, String uniqueId){
        SpAgentStores spAgentStores = crudTransactions.fetchAgentWithStoreId(new BigInteger(storeId));
        String appUniqueId = spAgentStores.getUniqueId();
        //If it's first app installation
        if (appUniqueId.equals("0")) {
            crudTransactions.updateAppUniqueId(storeId, uniqueId);
            LOGGER.info("First time installing agent App for this storeId: {}", storeId);
            return false;
        }
        if ((appUniqueId != null) && (appUniqueId.equals(uniqueId))) {
            LOGGER.info("App installation instance has not changed. Unique ID: {}", uniqueId);
            return false;
        }
        LOGGER.info("App installation instance has changed. OLD UniqueID: {}, NEW UniqueID: {}",appUniqueId, uniqueId);
        return true;

    }

    public boolean checkAgentAllowsDeviceCapture(String agentId){
        SpAgents agent = crudTransactions.fetchAgentWithId(agentId);
        LOGGER.info("Checking global device capture config for agent: {}", agent);
        BigInteger deviceCapture = null;
        if(agent != null){
            deviceCapture = agent.getDeviceCapture();
        }
        if(deviceCapture.equals(BigInteger.ONE)){
            LOGGER.info("Device capture is allowed for agent with agent id {}", agentId);
            return true;
        }
        LOGGER.info("Device capture is not allowed for agent with agent id {}", agentId);
        return false;
    }

    public BigDecimal updateLocationLog(SpLocationLog spLocationLog){
        BigDecimal id = null;
        try {
            crudTransactions.updateLocationLog(spLocationLog);
            id = spLocationLog.getId();
        }catch(Exception ex){
            LOGGER.info("Error saving location record: {}", ex.getLocalizedMessage());
        }
        return id;
    }    

    public String generateRandomString() {
        String generatedString = "";
        int position;
        char[] chars = {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'};
        for(int i=0; i< 16; i++){
            int index = ThreadLocalRandom.current().nextInt(0, 52);
            generatedString = generatedString+chars[index];

        }
        return generatedString;
    }

    public boolean withinLocation(double latitudeOne,double longitudeOne,double latitudeTwo,double longitudeTwo, double radius){
        double dLat = deg2rad(latitudeTwo-latitudeOne);
        double earthRadius = 6371;
        double dLon = deg2rad(longitudeTwo-longitudeOne);
        double a = Math.sin(dLat/2) * Math.sin(dLat/2) + Math.cos(deg2rad(latitudeOne)) * Math.cos(deg2rad(latitudeTwo)) * Math.sin(dLon/2) * Math.sin(dLon/2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        double d = (earthRadius * c) * 1000;
        LOGGER.info("Distance from central location: {}", d);
        if(d <= radius ) {
            return true;
        }else{
            return false;
        }
    }

    public double deg2rad(double deg) {
        return deg * (Math.PI/180);
    }

    public HashMap<String, Object> failedSpTransaction(int code) {
        HashMap<String, Object> myMap = new HashMap<>();
        String msg = "";
        String msgtrail = " Please try again later!";
        String saccoTrail = " Please contact your sacco for assistance.";
        switch (code) {
            case 900:
                msg = "Sorry. Your pin change request failed." + msgtrail;
                break;
            default:
                msg = "Request could not be processed." + msgtrail;
        }

        myMap.put("code", "02");
        myMap.put("response_description", msg);
        return myMap;
    }


    public HashMap<String, Integer> updateCustomerPinEntries(String nationalId, Long clientId, Long agentStoreId, String ipAddress) {
        int pinRetryCount = 3; // Default Pin retry count
        int pinRetriesLeft;
        HashMap<String, Integer> pinResult = new HashMap<>();

        // Fetching PIN retries from SP_AGENTS using the agent_store_id
        Optional<SpAgentStores> agentStoreOutcome = Optional.ofNullable(crudTransactions.fetchAgentWithStoreId(BigInteger.valueOf(agentStoreId)));
        if(agentStoreOutcome.isPresent()) {
            SpAgentStores agentStore = agentStoreOutcome.get();
            BigInteger agentId = agentStore.getAgentId();

            Optional<SpAgents> spAgentResult = Optional.ofNullable(crudTransactions.fetchAgentWithId(String.valueOf(agentId)));
            if (spAgentResult.isPresent()) {
                SpAgents spAgent = spAgentResult.get();
                pinRetryCount = spAgent.getCustomerPinRetries(); // update default pin retries with the configured pin retires
                pinResult.put("pinRetryCount", pinRetryCount);
                LOGGER.info("PIN RETRIES COUNT FROM SP_AGENTS: {}", pinRetryCount);
            } else { LOGGER.warn("AGENT WITH AGENT_ID :: {}, DOESN'T EXIST  ", agentId);}

            SpCustomers spCustomer =  crudTransactions.fetchCustomerByClientIdAndNationalId(clientId, nationalId);
            if(spCustomer != null){
                Integer wrongPinEntries = spCustomer.getWrongPinEntries();
                ++wrongPinEntries;

                pinRetriesLeft = Math.max((pinRetryCount - wrongPinEntries), 0);
                LOGGER.info("PIN RETRY COUNT: {}, CUSTOMER PIN COUNT: {}, PIN RETRIES LEFT: {}",
                        pinRetryCount, wrongPinEntries, pinRetriesLeft);
                pinResult.put("wrongPinEntries", wrongPinEntries);
                pinResult.put("pinRetriesLeft", pinRetriesLeft);
                LOGGER.info("pinResult: {}", pinResult);

                if (wrongPinEntries >= pinRetryCount) {
                    //Blacklist the customer
                    SpStoreUsers storeUser = crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(
                            String.valueOf(agentStore.getAgentStoreId())
                    );
                    if(storeUser != null){
                        LOGGER.info("PIN retries exceeded, blacklisting... msisdn :: {}, nationalId :: {} | retryCount :: {} ",
                                spCustomer.getMsisdn(), nationalId, wrongPinEntries);
                        crudTransactions.blacklistMsisdnForClient(
                                spCustomer.getMsisdn(), clientId,
                                storeUser.getStoreUserId().longValue(), ipAddress);

                        //Reset the pin retries to 0 after blacklisting the customer
                        spCustomer.setWrongPinEntries(0);
                    }
                    else {LOGGER.error("STORE USER WITH AGENT_STORE_ID :: {} DOESN'T EXIST  ", agentStore.getAgentStoreId());}
                } else { spCustomer.setWrongPinEntries(wrongPinEntries); }
                crudTransactions.save(spCustomer);
            }
            else {LOGGER.warn("CUSTOMER NATIONAL_ID :: {}, CLIENT_ID :: {} DOESN'T EXIST  ", nationalId, clientId);}
        }
        else {LOGGER.warn("AGENT_STORE WITH AGENT_STORE_ID :: {}, DOESN'T EXIST  ", agentStoreId);}

        return pinResult;
    }

    public boolean isOtpOnLoginRequired(String agentId){
        SpAgents agent = crudTransactions.fetchAgentWithId(agentId);
        LOGGER.info("Checking OTP LOGIN for agent ID: {}", agentId);
        return agent != null && !ObjectUtils.isEmpty(agent.getOtpLogin()) && BigInteger.ONE.equals(agent.getOtpLogin());
    }

    public Float getCurrentVersion(String agentId){
        try {
            Float currentVersion = null;
            SpAgents agent = crudTransactions.fetchAgentWithId(agentId);
            LOGGER.info("GETTING CURRENT APP VERSION for agent ID: {}", agentId);
            if(agent != null && !ObjectUtils.isEmpty(agent.getVersionNew())){
                String versionNewDetails =  agent.getVersionNew();
                String[] str = versionNewDetails.split("[,\\,]");
                String version = "";
                for (int i = 0; i < str.length; i++) version = str[0];
                currentVersion = Float.parseFloat(version);
            }

            return currentVersion;
        }
        catch (Exception e) {
            e.printStackTrace();
            LOGGER.warn("FAILED TO GET CURRENT VERSION. ERROR: {} ", e.getMessage());
            return null;
        }
    }


}
