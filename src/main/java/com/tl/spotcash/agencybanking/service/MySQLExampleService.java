package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.repository.MySQLQueryHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Example service demonstrating MySQL-specific features and syntax usage.
 * This service shows how to use the enhanced CrudService with MySQL-specific methods.
 * 
 * <AUTHOR>
 */
@Service
public class MySQLExampleService {

    private static final Logger LOG = LoggerFactory.getLogger(MySQLExampleService.class);

    @Autowired
    private CrudService crudService;

    @Autowired
    private MySQLQueryHelper mysqlQueryHelper;

    /**
     * Example: Using INSERT...ON DUPLICATE KEY UPDATE for upsert operations.
     * This is a MySQL-specific feature that's very efficient for handling duplicates.
     */
    public void demonstrateUpsertOperation() {
        try {
            // Build upsert query using MySQL helper
            List<String> columns = Arrays.asList("id", "name", "email", "updated_at");
            List<String> updateColumns = Arrays.asList("name", "email", "updated_at");
            
            String upsertQuery = mysqlQueryHelper.buildInsertOnDuplicateKeyUpdate(
                "SP_USERS", columns, updateColumns);
            
            LOG.info("Generated MySQL upsert query: {}", upsertQuery);
            
            // Parameters for the upsert
            Map<String, Object> params = new HashMap<>();
            params.put("id", 1);
            params.put("name", "John Doe");
            params.put("email", "<EMAIL>");
            params.put("updated_at", new Date());
            
            // Execute using MySQL-specific method
            int affectedRows = crudService.executeMySQLNativeQuery(upsertQuery, params);
            LOG.info("Upsert operation affected {} rows", affectedRows);
            
        } catch (Exception e) {
            LOG.error("Error in upsert operation: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using REPLACE INTO for MySQL-specific replace operations.
     */
    public void demonstrateReplaceOperation() {
        try {
            List<String> columns = Arrays.asList("id", "session_token", "user_id", "created_at");
            String replaceQuery = mysqlQueryHelper.buildReplaceInto("SP_USER_SESSIONS", columns);
            
            LOG.info("Generated MySQL replace query: {}", replaceQuery);
            
            Map<String, Object> params = new HashMap<>();
            params.put("id", 1);
            params.put("session_token", "abc123xyz");
            params.put("user_id", 100);
            params.put("created_at", new Date());
            
            int affectedRows = crudService.executeMySQLNativeQuery(replaceQuery, params);
            LOG.info("Replace operation affected {} rows", affectedRows);
            
        } catch (Exception e) {
            LOG.error("Error in replace operation: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL JSON functions for JSON column operations.
     */
    public void demonstrateJsonOperations() {
        try {
            // Extract JSON data
            String jsonExtractQuery = "SELECT id, " + 
                mysqlQueryHelper.buildJsonFunction("metadata", "$.user_preferences", "UNQUOTE") + 
                " as preferences FROM SP_USER_PROFILES WHERE id = :userId";
            
            Map<String, Object> params = new HashMap<>();
            params.put("userId", 1);
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(jsonExtractQuery, params);
            LOG.info("JSON extract query returned {} results", results.size());
            
            // Update JSON data
            String jsonUpdateQuery = "UPDATE SP_USER_PROFILES SET metadata = " +
                mysqlQueryHelper.buildJsonFunction("metadata", "$.last_login", "SET") +
                " WHERE id = :userId";
            
            params.put("userId", 1);
            int affectedRows = crudService.executeMySQLNativeQuery(jsonUpdateQuery, params);
            LOG.info("JSON update operation affected {} rows", affectedRows);
            
        } catch (Exception e) {
            LOG.error("Error in JSON operations: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL full-text search capabilities.
     */
    public void demonstrateFullTextSearch() {
        try {
            List<String> searchColumns = Arrays.asList("title", "description");
            String searchTerm = "banking transaction";
            String fullTextQuery = "SELECT id, title, description, " +
                mysqlQueryHelper.buildFullTextSearch(searchColumns, searchTerm, "NATURAL LANGUAGE") +
                " as relevance FROM SP_ARTICLES WHERE " +
                mysqlQueryHelper.buildFullTextSearch(searchColumns, searchTerm, "NATURAL LANGUAGE") +
                " > 0 ORDER BY relevance DESC";
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(fullTextQuery, Collections.emptyMap());
            LOG.info("Full-text search returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error in full-text search: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL window functions for analytics.
     */
    public void demonstrateWindowFunctions() {
        try {
            List<String> partitionBy = Arrays.asList("department_id");
            List<String> orderBy = Arrays.asList("salary DESC");
            
            String windowQuery = "SELECT employee_id, name, salary, department_id, " +
                mysqlQueryHelper.buildWindowFunction("ROW_NUMBER", partitionBy, orderBy) +
                " as rank_in_dept FROM SP_EMPLOYEES";
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(windowQuery, Collections.emptyMap());
            LOG.info("Window function query returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error in window function query: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL batch operations for bulk inserts.
     */
    public void demonstrateBatchOperations() {
        try {
            String batchInsertQuery = "INSERT INTO SP_AUDIT_LOG (action, user_id, timestamp, details) " +
                "VALUES (:action, :userId, :timestamp, :details)";
            
            List<Map<String, Object>> batchParams = new ArrayList<>();
            
            // Create multiple parameter sets for batch operation
            for (int i = 1; i <= 100; i++) {
                Map<String, Object> params = new HashMap<>();
                params.put("action", "LOGIN");
                params.put("userId", i);
                params.put("timestamp", new Date());
                params.put("details", "User login from mobile app");
                batchParams.add(params);
            }
            
            int[] affectedRows = crudService.executeMySQLBatchQuery(batchInsertQuery, batchParams);
            LOG.info("Batch operation completed, total operations: {}", affectedRows.length);
            
        } catch (Exception e) {
            LOG.error("Error in batch operations: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL stored procedures.
     */
    public void demonstrateStoredProcedure() {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("userId", 1);
            params.put("startDate", "2024-01-01");
            params.put("endDate", "2024-12-31");
            
            List<Object[]> results = crudService.executeMySQLStoredProcedure(
                "GetUserTransactionSummary", params);
            
            LOG.info("Stored procedure returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error executing stored procedure: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL GROUP_CONCAT function.
     */
    public void demonstrateGroupConcat() {
        try {
            String groupConcatQuery = "SELECT user_id, " +
                mysqlQueryHelper.buildGroupConcat("role_name", ", ", "role_name ASC") +
                " as roles FROM SP_USER_ROLES GROUP BY user_id";
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(groupConcatQuery, Collections.emptyMap());
            LOG.info("GROUP_CONCAT query returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error in GROUP_CONCAT query: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL CASE WHEN statements.
     */
    public void demonstrateCaseWhen() {
        try {
            Map<String, String> conditions = new HashMap<>();
            conditions.put("status = 'ACTIVE'", "'User is active'");
            conditions.put("status = 'INACTIVE'", "'User is inactive'");
            conditions.put("status = 'SUSPENDED'", "'User is suspended'");
            
            String caseWhenQuery = "SELECT id, name, status, " +
                mysqlQueryHelper.buildCaseWhen(conditions, "'Unknown status'") +
                " as status_description FROM SP_USERS";
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(caseWhenQuery, Collections.emptyMap());
            LOG.info("CASE WHEN query returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error in CASE WHEN query: {}", e.getMessage(), e);
        }
    }

    /**
     * Example: Using MySQL date functions.
     */
    public void demonstrateDateFunctions() {
        try {
            String dateQuery = "SELECT id, name, created_at, " +
                mysqlQueryHelper.buildDateFunction("NOW") + " as current_time, " +
                "DATEDIFF(" + mysqlQueryHelper.buildDateFunction("NOW") + ", created_at) as days_since_creation " +
                "FROM SP_USERS WHERE created_at >= DATE_SUB(" + 
                mysqlQueryHelper.buildDateFunction("NOW") + ", INTERVAL 30 DAY)";
            
            List<Object[]> results = crudService.fetchWithMySQLNativeQuery(dateQuery, Collections.emptyMap());
            LOG.info("Date function query returned {} results", results.size());
            
        } catch (Exception e) {
            LOG.error("Error in date function query: {}", e.getMessage(), e);
        }
    }
}
