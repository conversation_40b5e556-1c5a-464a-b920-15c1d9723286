package com.tl.spotcash.agencybanking.service;

import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.entity.SpAgentStores;
import com.tl.spotcash.agencybanking.entity.SpAgents;
import com.tl.spotcash.agencybanking.enums.StoreUser;
import com.tl.spotcash.agencybanking.utils.Crypt;
import com.tl.spotcash.agencybanking.utils.JwtTokenUtil;
import io.jsonwebtoken.Claims;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;

@Service
public class WorkingHoursService {
    private final static Logger LOGGER = LoggerFactory.getLogger(WorkingHoursService.class);
    final CrudTransactionController crudTransactions;
    final JwtTokenUtil jwtUtil;


    public WorkingHoursService(CrudTransactionController crudTransactions, JwtTokenUtil jwtUtil) {
        this.crudTransactions = crudTransactions;
        this.jwtUtil = jwtUtil;
    }

    public boolean checkRequestIsWithinWorkingHours(BigInteger storeId, String workingHours) throws JSONException {
        if(checkAgentIsSetToUseWorkingHours(storeId)){
            //String workingHours = fetchAgentWorkingHours(storeId);
            LOGGER.info("Agent Working hours: {}", workingHours);
            JSONObject jsonObject = new JSONObject(workingHours);
            String start = jsonObject.getString("start");
            String end = jsonObject.getString("end");

            return compareTime(start,end);
        }else{
            LOGGER.info("Agent is not set to use working Hours.");
            return true;
        }
    }

    public boolean checkRequestIsWithinWorkingHours(String token) throws JSONException {
        String workingHours = jwtUtil.getClaim(token, StoreUser.WORKING_HOURS.getValue());
        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());

        if(checkAgentIsSetToUseWorkingHours(new BigInteger(agentStoreId))){
            //String workingHours = fetchAgentWorkingHours(storeId);
            LOGGER.info("Agent Working hours: {}", workingHours);
            JSONObject jsonObject = new JSONObject(workingHours);
            String start = jsonObject.getString("start");
            String end = jsonObject.getString("end");

            return compareTime(start,end);
        }else{
            LOGGER.info("Agent is not set to use working Hours.");
            return true;
        }
    }

    public boolean checkAgentIsSetToUseWorkingHours(BigInteger storeId){
        SpAgentStores agentDevice= crudTransactions.fetchAgentWithStoreId(storeId);
        LOGGER.info(" Store{}", agentDevice);
        BigInteger spAgentId = agentDevice.getAgentId();
        SpAgents spAgents = crudTransactions.fetchAgentWithId(String.valueOf(spAgentId));
        LOGGER.info("Checking global config for agent working hours. Agent: {}", spAgents);
        BigInteger val = spAgents != null ? spAgents.getWorkingHours(): null;
        return val != null && val.equals(BigInteger.ONE);
    }

    public String fetchAgentWorkingHours(BigInteger storeId) throws IOException, JSONException {
        Gson gson = new Gson();
        SpAgentStores agentDevice = crudTransactions.fetchAgentWithStoreId(storeId);
        String startTime  = agentDevice.getStartTime();
        String endTime  = agentDevice.getEndTime();
        HashMap<String, String> workingHours = new LinkedHashMap<>();
        workingHours.put("start", startTime);
        workingHours.put("end", endTime);
        String workingHoursRes = gson.toJson(workingHours);
        LOGGER.info("Working hours string: {}",workingHoursRes);
        JSONObject obj = new JSONObject(workingHoursRes);
        return obj.toString();
    }

    public boolean compareTime(String start, String stop){
        try {
            Date StartTime = new SimpleDateFormat("HH:mm:ss").parse(start);
            Calendar calendar1 = Calendar.getInstance();
            calendar1.setTime(StartTime);
            calendar1.add(Calendar.DATE, 1);

            Date StopTime = new SimpleDateFormat("HH:mm:ss").parse(stop);
            Calendar calendar2 = Calendar.getInstance();
            calendar2.setTime(StopTime);
            calendar2.add(Calendar.DATE, 1);

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currentDateTime = simpleDateFormat.format(new Date(System.currentTimeMillis()));
            LocalDateTime dateTime = LocalDateTime.parse(currentDateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String currentTime = DateTimeFormatter.ofPattern("HH:mm:ss").format(dateTime);

            Date d = new SimpleDateFormat("HH:mm:ss").parse(currentTime);
//            Date d = new SimpleDateFormat("HH:mm:ss").parse("00:00:00");
            Calendar calendar3 = Calendar.getInstance();
            calendar3.setTime(d);
            calendar3.add(Calendar.DATE, 1);
            Date x = calendar3.getTime();
            LOGGER.info("Current Time: {} ",currentTime);
            if (x.after(calendar1.getTime()) && x.before(calendar2.getTime())) {

                LOGGER.info("Request is within Working hours: Start {} , Stop {}",start,stop);
                return true;
            }
        } catch (ParseException e) {
            LOGGER.error("Error comparing dates: {}", e.getLocalizedMessage());
            throw new RuntimeException("Unable to compare time");
        }
        return false;
    }

    public boolean requestIsWithinWorkingHours(String agentStoreId) throws JSONException, IOException {
        if(checkAgentIsSetToUseWorkingHours(new BigInteger(agentStoreId))){
            String workingHours = fetchAgentWorkingHours(BigInteger.valueOf(Long.parseLong(agentStoreId)));
            LOGGER.info("Agent Working hours: {}", workingHours);
            JSONObject jsonObject = new JSONObject(workingHours);
            String start = jsonObject.getString("start");
            String end = jsonObject.getString("end");

            return compareTime(start,end);
        }else{
            LOGGER.info("Agent is not set to use working Hours.");
            return true;
        }
    }

}
