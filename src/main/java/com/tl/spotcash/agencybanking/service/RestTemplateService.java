package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import org.springframework.http.ResponseEntity;

import java.util.Map;

public interface RestTemplateService {

    String GETrequest(String URL, String authorizationKey);

    String POSTrequest(String URL, String authorizationKey, Object requestBody);

    String POSTrequestNoAuth(String URL, Object requestBody);

    String POSTRequestWithSession(String URL, Object requestBody, String sessionId);

    String GETrequestNoAuth(String URL);

    String POSTrequestForParams(String URL, String authorizationKey, Map<String,String> requestBody);

    String POSTRequestWithParams(String URL, Map<String,String> requestBody);

    String POSTRequestWithParamsAndSession(String URL, Map<String,String> requestBody, String sessionId);

    ResponseEntity<String> sendSmartlifeSMS(String msisdn, String message);

    CorebankingResponse POSTSpotcashRequestNoAuth(String URL, Object requestBody);
}
