package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.entity.SpAgencyConfigMessages;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class ErrorMessageService {
    @Autowired
    CrudTransactionController crudTransactions;

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorMessageService.class);

    public JSONObject fetchAgencyConfigMessages(String clientId){
        //Fetching agency config error messages
        SpAgencyConfigMessages agencyConfigMessages = crudTransactions.fetchAgencyConfigMessages(clientId);
        if(agencyConfigMessages == null) return null;
        JSONObject errMessages = null;
        try {
            errMessages = new JSONObject(agencyConfigMessages.getConfigMessages());
        } catch (JSONException e) {
            LOGGER.error("FAILED TO FETCH AGENCY CONFIG MESSAGES. ERROR :: {}", e.getMessage());
        }
        return errMessages;
    }

    public String getErrorMessage(String clientId, String messageKey, String defaultMessage){
        //Fetching agency config error messages
        JSONObject agencyErrorMessages = fetchAgencyConfigMessages(clientId);
        String errorMessage = defaultMessage;

        if(agencyErrorMessages != null){
            try{
                errorMessage = agencyErrorMessages.getString(messageKey);
            } catch (JSONException e) {
                LOGGER.error("FAILED TO FETCH CONFIGURED ERROR MESSAGE. USING DEFAULT MESSAGES INSTEAD." +
                        " ERROR :: {}", e.getMessage());
            }
        }

        return errorMessage;
    }

    public static String parseOtpMessage(String message, HashMap<String, String> otpResult){
        if(message.contains("<<")){
            //Replace the <<customerOtpRetries>> tag
            if(message.contains("<<customerOtpRetries>>") && otpResult.containsKey("customerOtpRetries")){
                message = message.replace("<<customerOtpRetries>>",
                        otpResult.get("customerOtpRetries"));
            }

            //Replace the <<otpRetryCount>> tag
            if(message.contains("<<otpRetryCount>>") && otpResult.containsKey("otpRetryCount")){
                message = message.replace("<<otpRetryCount>>",
                        otpResult.get("otpRetryCount"));
            }

            //Replace the <<otpRetriesLeft>> tag
            if(message.contains("<<otpRetriesLeft>>") && otpResult.containsKey("otpRetriesLeft")){
                message = message.replace("<<otpRetriesLeft>>",
                        otpResult.get("otpRetriesLeft"));
            }

            //Replace the <<agentWrongOtpEntries>> tag
            if(message.contains("<<agentWrongOtpEntries>>") && otpResult.containsKey("agentWrongOtpEntries")){
                message = message.replace("<<agentWrongOtpEntries>>",
                        otpResult.get("agentWrongOtpEntries"));
            }

        }

        return message;
    }

}