package com.tl.spotcash.agencybanking.service;

import com.tl.spotcash.agencybanking.custommodels.NewMember;
import com.tl.spotcash.agencybanking.custommodels.ValidateEventCodeRequest;
import com.tl.spotcash.agencybanking.custommodels.ValidateEventsResponse;
import com.tl.spotcash.agencybanking.custommodels.agent.AgentReportResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.custommodels.event.*;
import com.tl.spotcash.agencybanking.custommodels.member.GetMemberRequest;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.entity.SpVpnclientsConfigs;

import java.util.HashMap;

/**
 *
 * <AUTHOR>
 */
public interface SpotcashCbsService {
    
    CbsAccountsResponseMapper fetchCustomerAccounts(String nationalId, String cbs_client_id);

    CbsTransactionsResponseMapper fetchTransactions(String cbs_client_id, String phoneNumber,
                                                    String startDate, String endDate,
                                                    String startTime, String endTime);

    NewMember insertNewMember(HashMap<String, String> requestMap);
    
    AgentReportResponseMapper fetchAgentReport(String deviceId, String phoneNo, String date_from, String date_to, String clientId);
    String POSTRequestWithSession(String URL, Object requestBody, String sessionId);
    ValidateAccountResponseMapper validateCustomerAccounts(String accountNumber, String cbs_client_id);

    FetchImageResponseMapper fetchMemberImage(String idNumber, String cbs_client_id);
    FetchImageResponseMapper fetchMemberImage(String idNumber, String cbs_client_id, String identifier);

    CbsResponseMapper lockCustomerAccount(String accountNumber, String idNumber, String clientId);

    CbsNonMemberAccountsResponseMapper fetchNonMemberAccounts(String cbs_client_id);

    ReversalResponseMapper reverseTransaction(SpTransTempTable trx, SpTransTempTable originalTransaction);

    GetMemberCbsResponse getMember(GetMemberRequest request);

    GetMemberCbsResponse getMemberImage(GetMemberRequest request);

    GetEventsCbsResponse getEvents(GetEventsRequest request);
    GetJobGroupResponse getJobGroups(GetJobGroupRequest request);


    ValidateEventsResponse validateEventCode(ValidateEventCodeRequest request);

    EventRegistrationCbsResponse registerMemberToEvent(RegisterMemberRequest request);

    EventRegistrationCbsResponse registerNonMemberToEvent(RegisterNonMemberRequest request);

    RedeemItemCbsResponse getRedeemItems(RedeemItemsRequest request);

    GetCustomerRewardsCbsResponse insertCustomerRewards(RedeemItem redeemItem, String clientId, String eventId, String customerId, SpVpnclientsConfigs cbsClientConfigData, String transactionId);

    GetRedeemedCustomerRewardsCbsResponse getAllRedeemedRewards(RedeemedCustomerRewardsRequest request);
    FetchMemberByMpesaCbsResponse fetchMemberByMpesaCbsResponse(FetchMpesaMembersRequest request, String clientId);
    FetchTranscationIdResponse fetchTranscationIdResponse(FetchTranscationIdRequest request, String clientId);
    GetServiceChargeCbsResponse getServiceCharge(ServiceChargeRequest request);
}
