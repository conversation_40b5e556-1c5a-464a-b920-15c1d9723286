package com.tl.spotcash.agencybanking.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.IMSIAuthResponseBody;
import com.tl.spotcash.agencybanking.custommodels.IMSIResponseBody;
import com.tl.spotcash.agencybanking.custommodels.ImsiAuthResponse;
import com.tl.spotcash.agencybanking.custommodels.ImsiResponseMessage;
import com.tl.spotcash.agencybanking.entity.SpAgentDeactivations;
import com.tl.spotcash.agencybanking.entity.SpAgents;
import com.tl.spotcash.agencybanking.entity.SpImsiRecords;
import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.repository.GenericCrudService;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class IMSIService {
    private final Logger LOGGER = LoggerFactory.getLogger(IMSIService.class);

    @Autowired
    Environment environment;
    @Autowired
    CrudTransactionController crudTransactions;
    @Autowired
    private GenericCrudService databaseCrudService;

    public String getAuthToken(){
        ObjectMapper objectMapper  =new ObjectMapper();
        LOGGER.info("base url " + environment.getProperty("integrator.base_url"));
        Map<String, String> authData = new HashMap<>();
        authData.put("email", environment.getProperty("integrator.auth_username"));
        authData.put("password", environment.getProperty("integrator.auth_password"));
        LOGGER.info("Authenticating: url " + environment.getProperty("integrator.base_url") + "imsi/authenticate  >> " + authData.get("username"));
        try {
            ResponseEntity<String> authResponse = SharedFunctions.postRequest(environment.getProperty("integrator.base_url")+"imsi/authenticate", authData, null);
            LOGGER.info("Authentication returned " + authResponse);

            if (authResponse.getStatusCode().is2xxSuccessful()) {
                ImsiAuthResponse responseMessage = objectMapper.readValue(authResponse.getBody(), ImsiAuthResponse.class);
                if (responseMessage.getReturn_code().equalsIgnoreCase("00")) {
                    LOGGER.info("Access token: " + responseMessage.getData());
                    return responseMessage.getData();
                } return null;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public IMSIResponseBody checkATI(String customerNumber) throws IOException{
        String token = this.getAuthToken();
        ObjectMapper objectMapper  =new ObjectMapper();
        IMSIResponseBody imsiResponseBody = null;
        try {
            LOGGER.info("Make IMSI checkATI Request " + customerNumber);
            ResponseEntity<String> imsiResponse = SharedFunctions.makeGetRequest(environment.getProperty("integrator.base_url")+"imsi/checkATI/?customerNumber="+customerNumber, null, token);
            ImsiResponseMessage responseMessage = objectMapper.readValue(imsiResponse.getBody(), ImsiResponseMessage.class);

            if (responseMessage != null && responseMessage.getReturn_code().equalsIgnoreCase("00")) {
                imsiResponseBody = responseMessage.getData();
            } else {
                LOGGER.info("IMSI Check respomse message: {}",responseMessage);
            }
        } catch (Exception e) {
            LOGGER.error("Exception processing request: {}", e.getLocalizedMessage());
        }
        return  imsiResponseBody;
    }
    //Check if agent allows imsi check and return imsi check interval if true
    public int checkAgentAllowsImsiCheck(String agentId) throws NoSuchElementException {
        Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentWithId(agentId));
        SpAgents spAgent = null;
        if (optionalAgent.isPresent()) {
            spAgent = optionalAgent.get();
            if((spAgent.getSimBinding()).equals(BigInteger.ONE)){
                LOGGER.info("Agent allows IMSI check. {}", spAgent);
                return spAgent.getImsiInterval();
            }else{
                return -1;
            }
        }else {
            throw new NoSuchElementException("Agent Not Found");
        }
    }
    public int daysSinceLastImsiCheck(String msisdn) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Optional<SpImsiRecords> optionalRecord = Optional.ofNullable(crudTransactions.fetchImsiRecord(msisdn));
        SpImsiRecords imsiRecord = null;
        Date lastImsiCheck = null;
        if (optionalRecord.isPresent()) {
            imsiRecord = optionalRecord.get();
            lastImsiCheck = imsiRecord.getLastImsiCheck();
            Date today = sdf.parse(sdf.format(new Date(System.currentTimeMillis())));
            Date imsiCheck = sdf.parse(sdf.format(lastImsiCheck));
            long diffInMillies = Math.abs(today.getTime() - imsiCheck.getTime());
            long diff = TimeUnit.HOURS.convert(diffInMillies, TimeUnit.MILLISECONDS);
            //long difference_In_TimeMillis = new Date(System.currentTimeMillis()).getTime() - lastImsiCheck.getTime();
            //long difference_In_Hours = (difference_In_TimeMillis / (1000 * 60 * 60)) % 24;

            //long daysBetween = ChronoUnit.DAYS.between(lastImsiCheck, LocalDate.now());
            //LOGGER.info("Days Since last IMSI Check: {}",daysBetween);
            LOGGER.info("Hours Since last IMSI Check: {}",diff);
            return (int) diff;
        }else{
            return -1;
        }
    }
    public boolean isSimSwapped(String msisdn, BigInteger clientId, String agentId, String storeUserId, String storeId) throws ParseException, IOException {
        DateTimeFormatter simpleDateFormat = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        IMSIResponseBody imsiResponseBody = checkATI(msisdn);

        String apiLastSwapTime = imsiResponseBody.getLastSwapDate();

        Optional<SpImsiRecords> optionalRecord = Optional.ofNullable(crudTransactions.fetchImsiRecord(msisdn));
        SpImsiRecords imsiRecord = null;
        LocalDate dbLastSwapTime = null;
        if (optionalRecord.isPresent()) {
            imsiRecord = optionalRecord.get();
            dbLastSwapTime = imsiRecord.getLastSwapTime();
            //Update record on db
            imsiRecord.setLastImsiCheck(new Date(System.currentTimeMillis()));
            imsiRecord.setLastSwapTime(LocalDate.parse(imsiResponseBody.getLastSwapDate(), simpleDateFormat));
            crudTransactions.updateImsiRecord(imsiRecord);
            LOGGER.info("Updating IMSI record to db");
        }else{//First time using agent mobile app. save imsiRecord to db
            SpImsiRecords spImsiRecords = new SpImsiRecords();
            spImsiRecords.setClientId(clientId);
            spImsiRecords.setMsisdn(msisdn);
            spImsiRecords.setLastSwapTime(LocalDate.parse(imsiResponseBody.getLastSwapDate(), simpleDateFormat));
            spImsiRecords.setLastImsiCheck(new Date(System.currentTimeMillis()));
            crudTransactions.save(spImsiRecords);
            LOGGER.info("Saving IMSI record to db");
            return false;
        }
        String dbDate = simpleDateFormat.format(dbLastSwapTime);
        //Swap date, if has changed can only be higher than what we have on the db: not less not equal
        if(apiLastSwapTime.compareTo(dbDate) != 0){
            LOGGER.info("Simcard was swapped! ");
            SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
            spAgentDeactivations.setDateCreated(new Date(System.currentTimeMillis()));
            spAgentDeactivations.setAgentId(new BigInteger(agentId));
            spAgentDeactivations.setClientId(clientId);
            spAgentDeactivations.setOldSwapDate((dbLastSwapTime)); //calculate days past imsi check in hours
            spAgentDeactivations.setNewSwapDate((LocalDate.parse(apiLastSwapTime, simpleDateFormat)));
            spAgentDeactivations.setDeactivationType("Store User Deactivation");
            spAgentDeactivations.setStatus("Pending");
            spAgentDeactivations.setStoreUserId(new BigInteger(storeUserId));
            spAgentDeactivations.setDeactivationReason("Deactivated due to recent sim swap");
            spAgentDeactivations.setAgentStoreId(BigInteger.valueOf(Long.parseLong(storeId)));
            databaseCrudService.save(spAgentDeactivations);
            return true;
        }else{
            return false;
        }
    }
}
