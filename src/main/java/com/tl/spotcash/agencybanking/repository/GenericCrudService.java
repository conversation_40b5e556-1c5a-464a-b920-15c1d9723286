/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.tl.spotcash.agencybanking.repository;

import org.hibernate.*;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManagerFactory;
import javax.persistence.RollbackException;
import javax.validation.ConstraintViolationException;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
public class GenericCrudService implements CrudService {

    private static final Logger LOG = LoggerFactory.getLogger(GenericCrudService.class);

    @Autowired
    @Qualifier(value="primaryFactory")
    private EntityManagerFactory entityManagerFactory;

    /*@Autowired
    @Qualifier(value="secondaryFactory")
    private EntityManagerFactory newEntityManagerFactory;*/

    private SessionFactory sessionFactory;
    private SessionFactory newSessionFactory;

    @PostConstruct
    protected void init() {
        LOG.info("Initializing crud service...");
        sessionFactory = entityManagerFactory.unwrap(SessionFactory.class);
        //newSessionFactory = newEntityManagerFactory.unwrap(SessionFactory.class);

        LOG.info("Crud service initialized. Sessionfactory properties {}", entityManagerFactory.getProperties());
    }

    @Override
    public <T> T findEntity(Serializable primaryKey, Class<T> clazz) throws HibernateException {

        Session session = null;
        try {
            session = sessionFactory.openSession();
            IdentifierLoadAccess loadAccess = session.byId(clazz);
            return (T) loadAccess.load(primaryKey);
        } catch (Exception e) {
            LOG.error("Failed to find entity {} by id {}. {}", clazz, primaryKey, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> fetchWithNamedQuery(String queryName, Map<String, Object> params) {
        return fetchWithNamedQuery(queryName, params, 0, DEFAULT_PAGE_SIZE);
    }

    @Override
    public <T> List<T> fetchWithHibernateQuery(String query, Map<String, Object> params) throws HibernateException {
        return fetchWithHibernateQuery(query, params, 0, DEFAULT_PAGE_SIZE);
    }

    @Override
    public <T> List<T> fetchWithHibernateQueryNewCon(String query, Map<String, Object> params) throws HibernateException {
        return fetchWithHibernateQueryNewCon(query, params, 0, DEFAULT_PAGE_SIZE);
    }

    @Override
    public <T> T fetchWithHibernateQuerySingleResult(String query, Map<String, Object> params) throws HibernateException {
        List<T> results = fetchWithHibernateQuery(query, params, 0, 1);
        return results.isEmpty() ? null : results.iterator().next();
    }

    @Override
    public <T> List<T> fetchWithHibernateQuery(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing Hibernate={}, start={},end={} params=[{}]", query, start, end, params);
        Session session = null;
        try {
            session = sessionFactory.openSession();
            Query q = session.createQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.<T>list();
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", query, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> fetchWithHibernateQueryNewCon(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing Hibernate={}, start={},end={} params=[{}]", query, start, end, params);
        Session session = null;
        try {
            session = newSessionFactory.openSession();
            Query q = session.createQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.<T>list();
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", query, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    public <T> Object fetchWithHibernateQuerySingleResult(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing Hibernate={}, start={},end={} params=[{}]", query, start, end, params);

        Session session = null;
        try {
            session = sessionFactory.openSession();

            Query q = session.createQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            return q.getFirstResult();
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", query, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public int executeHibernateQuery(String queryString, Map<String, Object> params) throws HibernateException {
        LOG.debug("Executing Hibernate={}, params=[{}]", queryString, params);
        Transaction tx = null;
        Session session = null;
        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction();
            Query q = session.createQuery(queryString);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            int executeUpdate = q.executeUpdate();
            tx.commit();
            return executeUpdate;
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", queryString, params, e.getMessage());
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public int executeNativeQuery(String queryString, Map<String, Object> params) throws HibernateException {
        LOG.info("executing query "+queryString);
        LOG.debug("Executing Hibernate={}, params=[{}]", queryString, params);
        Transaction tx = null;

        Session session = null;
        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction();
            Query q = session.createNativeQuery(queryString);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            int executeUpdate = q.executeUpdate();
            tx.commit();
            return executeUpdate;
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", queryString, params, e.getMessage());
            if (tx != null && tx.isActive()) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    /**
     * @param <T>
     * @param query
     * @param start
     * @param end
     * @return
     * @throws HibernateException
     */

    //fetching data using the second connection
    @Override
    public <T> List<T> fetchWithNativeQuerySec(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing nativeQuery={}, start={},end={} params=[{}]", query, start, end, params);

        Session session = null;
        try {
            session = newSessionFactory.openSession();
            Query q = session.createNativeQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.<T>list();
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", query, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> fetchWithNativeQuery(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing nativeQuery={}, start={},end={} params=[{}]", query, start, end, params);

        Session session = null;
        try {
            session = sessionFactory.openSession();
            Query q = session.createNativeQuery(query);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }
            return q.<T>list();
        } catch (Exception e) {
            LOG.error("Failed in executing hibernate query {} with params [{}]. {}.", query, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }


    @Override
    public <T> List<T> fetchWithNamedQuery(String queryName, Map<String, Object> params, int start, int end) {
        LOG.debug("Executing NamedQuery={}, start={},end={} params=[{}]", queryName, start, end, params);

        Session session = null;
        try {
            session = sessionFactory.openSession();
            Query query = session.getNamedQuery(queryName);
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    query.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    query.setParameter(param.getKey(), param.getValue());
                }
            });
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                query.setFirstResult(start);
                query.setMaxResults(end - start);
            }
            return query.<T>list();
        } catch (Exception e) {
            LOG.error("Failed in executing named query {} with params [{}]. {}.", queryName, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> void saveOrUpdate(T entity) throws HibernateException {
        LOG.debug("Perist or merge {}", entity);
        Transaction tx = null;
        Session session = null;
        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction(); // Use beginTransaction() instead of getTransaction().begin()
            session.saveOrUpdate(entity);
            tx.commit();
        } catch (Exception e) {
            LOG.error("Failed to persist or merge {}. {}", entity, e.getMessage());
            if (tx != null && tx.isActive()) {
                try {
                    tx.rollback();
                } catch (Exception rollbackEx) {
                    LOG.error("Failed to rollback transaction: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> void saveOrUpdate(List<T> entities) throws HibernateException {
        LOG.debug("Perist or merge {} entities.", entities.size());
        Transaction tx = null;
        Session session = null;
        try {
            session = sessionFactory.openSession();
            tx = session.getTransaction();
            tx.begin();
            for (T entity : entities) {
                session.saveOrUpdate(entity);
            }
            tx.commit();
        } catch (Exception e) {
            LOG.error("Failed to persist or merge {} entities. {}", entities.size(), e.getMessage(), e);
            if (tx != null) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> Object save(T entity) throws HibernateException, ConstraintViolationException {
        LOG.debug("Perist or merge {}", entity);
        Transaction tx = null;
        Object savedEntity = null;
        Session session = null;
        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction(); // Use beginTransaction() instead of getTransaction().begin()
            savedEntity = session.save(entity);
            tx.commit();
        } catch (ConstraintViolationException e) {
            LOG.error("Constraint validation failed {}. {}", entity, e.getMessage(), e);
            if (tx != null && tx.isActive()) {
                try {
                    tx.rollback();
                } catch (Exception rollbackEx) {
                    LOG.error("Failed to rollback transaction: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } catch (RollbackException e) {
            LOG.error("RollbackException validation failed {}. {}", entity, e.getMessage(), e);
            if (tx != null && tx.isActive()) {
                try {
                    tx.rollback();
                } catch (Exception rollbackEx) {
                    LOG.error("Failed to rollback transaction: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } catch (Exception e) {
            LOG.error("Failed to persist or merge {}. {}", entity, e.getMessage(), e);
            if (tx != null && tx.isActive()) {
                try {
                    tx.rollback();
                } catch (Exception rollbackEx) {
                    LOG.error("Failed to rollback transaction: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }

        return savedEntity;
    }

    @Override
    public <T> void remove(T entity) throws HibernateException {
        LOG.debug("Deleting {}", entity);
        Session session = null;
        Transaction tx = null;
        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction(); // Use beginTransaction() instead of getTransaction().begin()
            session.delete(entity);
            tx.commit();
        } catch (Exception e) {
            LOG.error("Failed to delete {}. {}", entity, e.getMessage());
            if (tx != null && tx.isActive()) {
                try {
                    tx.rollback();
                } catch (Exception rollbackEx) {
                    LOG.error("Failed to rollback transaction: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    @Override
    public int executeMySQLNativeQuery(String queryString, Map<String, Object> params) throws HibernateException {
        LOG.info("Executing MySQL native query: {}", queryString);
        LOG.debug("Executing MySQL query with params: [{}]", params);
        Transaction tx = null;
        Session session = null;

        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction();

            // Create native query with MySQL-specific optimizations
            Query q = session.createNativeQuery(queryString);

            // Set parameters with MySQL-specific handling
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            int executeUpdate = q.executeUpdate();
            tx.commit();
            LOG.debug("MySQL query executed successfully, affected rows: {}", executeUpdate);
            return executeUpdate;
        } catch (Exception e) {
            LOG.error("Failed to execute MySQL native query {} with params [{}]. Error: {}", queryString, params, e.getMessage());
            if (tx != null && tx.isActive()) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> fetchWithMySQLNativeQuery(String query, Map<String, Object> params, int start, int end) throws HibernateException {
        LOG.debug("Executing MySQL native query: {}, start: {}, end: {}, params: [{}]", query, start, end, params);
        Session session = null;

        try {
            session = sessionFactory.openSession();

            // Create native query with MySQL-specific optimizations
            Query q = session.createNativeQuery(query);

            // Set parameters with MySQL-specific handling
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            // Apply pagination
            start = start < 0 ? 0 : start;
            if (end >= start && start >= 0) {
                q.setFirstResult(start);
                q.setMaxResults(end - start);
            }

            List<T> results = q.list();
            LOG.debug("MySQL query executed successfully, returned {} results", results.size());
            return results;
        } catch (Exception e) {
            LOG.error("Failed to execute MySQL native query {} with params [{}]. Error: {}", query, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> fetchWithMySQLNativeQuery(String query, Map<String, Object> params) throws HibernateException {
        return fetchWithMySQLNativeQuery(query, params, 0, DEFAULT_PAGE_SIZE);
    }

    @Override
    public int[] executeMySQLBatchQuery(String queryString, List<Map<String, Object>> paramsList) {
        LOG.info("Executing MySQL batch query: {}", queryString);
        LOG.debug("Batch size: {}", paramsList.size());
        Transaction tx = null;
        Session session = null;

        try {
            session = sessionFactory.openSession();
            tx = session.beginTransaction();

            int[] results = new int[paramsList.size()];

            for (int i = 0; i < paramsList.size(); i++) {
                Query q = session.createNativeQuery(queryString);
                Map<String, Object> params = paramsList.get(i);

                // Set parameters for each batch item
                params.entrySet().forEach((param) -> {
                    if (param.getValue() instanceof Collection) {
                        q.setParameterList(param.getKey(), (Collection) param.getValue());
                    } else {
                        q.setParameter(param.getKey(), param.getValue());
                    }
                });

                results[i] = q.executeUpdate();

                // Flush every 50 operations for better memory management
                if (i % 50 == 0) {
                    session.flush();
                    session.clear();
                }
            }

            tx.commit();
            LOG.debug("MySQL batch query executed successfully, total operations: {}", paramsList.size());
            return results;
        } catch (Exception e) {
            LOG.error("Failed to execute MySQL batch query {}. Error: {}", queryString, e.getMessage());
            if (tx != null && tx.isActive()) {
                tx.rollback();
            }
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }

    @Override
    public <T> List<T> executeMySQLStoredProcedure(String procedureName, Map<String, Object> params) {
        LOG.info("Executing MySQL stored procedure: {}", procedureName);
        LOG.debug("Procedure params: [{}]", params);
        Session session = null;

        try {
            session = sessionFactory.openSession();

            // Build procedure call syntax
            StringBuilder procedureCall = new StringBuilder("CALL ");
            procedureCall.append(procedureName).append("(");

            if (!params.isEmpty()) {
                params.keySet().forEach(key -> procedureCall.append(":").append(key).append(","));
                procedureCall.setLength(procedureCall.length() - 1); // Remove last comma
            }
            procedureCall.append(")");

            Query q = session.createNativeQuery(procedureCall.toString());

            // Set parameters
            params.entrySet().forEach((param) -> {
                if (param.getValue() instanceof Collection) {
                    q.setParameterList(param.getKey(), (Collection) param.getValue());
                } else {
                    q.setParameter(param.getKey(), param.getValue());
                }
            });

            List<T> results = q.list();
            LOG.debug("MySQL stored procedure executed successfully, returned {} results", results.size());
            return results;
        } catch (Exception e) {
            LOG.error("Failed to execute MySQL stored procedure {} with params [{}]. Error: {}", procedureName, params, e.getMessage());
            throw e;
        } finally {
            if (session != null) {
                session.close();
            }
        }
    }
}
