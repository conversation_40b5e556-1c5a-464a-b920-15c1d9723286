package com.tl.spotcash.agencybanking.repository;

import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MySQL-specific query helper utility class.
 * Provides methods to build MySQL-specific queries and handle MySQL syntax features.
 * 
 * <AUTHOR>
 */
@Component
public class MySQLQueryHelper {

    /**
     * Build INSERT...ON DUPLICATE KEY UPDATE query for MySQL.
     * This is a MySQL-specific feature for handling duplicate key conflicts.
     * 
     * @param tableName target table name
     * @param columns list of column names
     * @param updateColumns columns to update on duplicate key (if null, updates all columns)
     * @return MySQL INSERT...ON DUPLICATE KEY UPDATE query string
     */
    public String buildInsertOnDuplicateKeyUpdate(String tableName, List<String> columns, List<String> updateColumns) {
        StringBuilder query = new StringBuilder();
        query.append("INSERT INTO ").append(tableName).append(" (");
        
        // Add column names
        query.append(String.join(", ", columns));
        query.append(") VALUES (");
        
        // Add parameter placeholders
        String placeholders = columns.stream()
                .map(col -> ":" + col)
                .collect(Collectors.joining(", "));
        query.append(placeholders);
        
        query.append(") ON DUPLICATE KEY UPDATE ");
        
        // Add update clause
        List<String> columnsToUpdate = updateColumns != null ? updateColumns : columns;
        String updateClause = columnsToUpdate.stream()
                .map(col -> col + " = VALUES(" + col + ")")
                .collect(Collectors.joining(", "));
        query.append(updateClause);
        
        return query.toString();
    }

    /**
     * Build REPLACE INTO query for MySQL.
     * REPLACE is a MySQL-specific extension to SQL.
     * 
     * @param tableName target table name
     * @param columns list of column names
     * @return MySQL REPLACE INTO query string
     */
    public String buildReplaceInto(String tableName, List<String> columns) {
        StringBuilder query = new StringBuilder();
        query.append("REPLACE INTO ").append(tableName).append(" (");
        
        // Add column names
        query.append(String.join(", ", columns));
        query.append(") VALUES (");
        
        // Add parameter placeholders
        String placeholders = columns.stream()
                .map(col -> ":" + col)
                .collect(Collectors.joining(", "));
        query.append(placeholders);
        query.append(")");
        
        return query.toString();
    }

    /**
     * Build MySQL LIMIT clause with offset.
     * 
     * @param offset starting position (0-based)
     * @param limit maximum number of rows
     * @return MySQL LIMIT clause
     */
    public String buildLimitClause(int offset, int limit) {
        if (offset > 0) {
            return " LIMIT " + offset + ", " + limit;
        } else {
            return " LIMIT " + limit;
        }
    }

    /**
     * Build MySQL date/time function queries.
     * 
     * @param functionName MySQL date function (NOW, CURDATE, CURTIME, etc.)
     * @return MySQL date function call
     */
    public String buildDateFunction(String functionName) {
        return functionName.toUpperCase() + "()";
    }

    /**
     * Build MySQL CASE WHEN statement.
     * 
     * @param conditions map of condition -> result pairs
     * @param elseResult default result if no conditions match
     * @return MySQL CASE WHEN statement
     */
    public String buildCaseWhen(Map<String, String> conditions, String elseResult) {
        StringBuilder caseStmt = new StringBuilder("CASE ");
        
        conditions.entrySet().forEach(entry -> {
            caseStmt.append("WHEN ").append(entry.getKey())
                   .append(" THEN ").append(entry.getValue()).append(" ");
        });
        
        if (elseResult != null) {
            caseStmt.append("ELSE ").append(elseResult).append(" ");
        }
        
        caseStmt.append("END");
        return caseStmt.toString();
    }

    /**
     * Build MySQL GROUP_CONCAT function.
     * 
     * @param column column to concatenate
     * @param separator separator string (default is comma)
     * @param orderBy optional ORDER BY clause
     * @return MySQL GROUP_CONCAT function call
     */
    public String buildGroupConcat(String column, String separator, String orderBy) {
        StringBuilder groupConcat = new StringBuilder("GROUP_CONCAT(");
        
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            groupConcat.append(column).append(" ORDER BY ").append(orderBy);
        } else {
            groupConcat.append(column);
        }
        
        if (separator != null && !separator.equals(",")) {
            groupConcat.append(" SEPARATOR '").append(separator).append("'");
        }
        
        groupConcat.append(")");
        return groupConcat.toString();
    }

    /**
     * Build MySQL JSON functions for JSON column operations.
     * 
     * @param jsonColumn JSON column name
     * @param jsonPath JSON path expression
     * @param operation JSON operation (EXTRACT, SET, REMOVE, etc.)
     * @return MySQL JSON function call
     */
    public String buildJsonFunction(String jsonColumn, String jsonPath, String operation) {
        switch (operation.toUpperCase()) {
            case "EXTRACT":
                return "JSON_EXTRACT(" + jsonColumn + ", '" + jsonPath + "')";
            case "UNQUOTE":
                return "JSON_UNQUOTE(JSON_EXTRACT(" + jsonColumn + ", '" + jsonPath + "'))";
            case "SET":
                return "JSON_SET(" + jsonColumn + ", '" + jsonPath + "', ?)";
            case "REMOVE":
                return "JSON_REMOVE(" + jsonColumn + ", '" + jsonPath + "')";
            case "VALID":
                return "JSON_VALID(" + jsonColumn + ")";
            default:
                throw new IllegalArgumentException("Unsupported JSON operation: " + operation);
        }
    }

    /**
     * Build MySQL full-text search query.
     * 
     * @param columns columns to search in
     * @param searchTerm search term
     * @param mode search mode (NATURAL LANGUAGE, BOOLEAN, etc.)
     * @return MySQL MATCH...AGAINST clause
     */
    public String buildFullTextSearch(List<String> columns, String searchTerm, String mode) {
        StringBuilder match = new StringBuilder("MATCH(");
        match.append(String.join(", ", columns));
        match.append(") AGAINST ('").append(searchTerm).append("'");
        
        if (mode != null && !mode.trim().isEmpty()) {
            match.append(" IN ").append(mode.toUpperCase()).append(" MODE");
        }
        
        match.append(")");
        return match.toString();
    }

    /**
     * Build MySQL window function query.
     * 
     * @param function window function (ROW_NUMBER, RANK, DENSE_RANK, etc.)
     * @param partitionBy partition columns
     * @param orderBy order by columns
     * @return MySQL window function clause
     */
    public String buildWindowFunction(String function, List<String> partitionBy, List<String> orderBy) {
        StringBuilder windowFunc = new StringBuilder(function.toUpperCase()).append("() OVER (");
        
        if (partitionBy != null && !partitionBy.isEmpty()) {
            windowFunc.append("PARTITION BY ").append(String.join(", ", partitionBy));
        }
        
        if (orderBy != null && !orderBy.isEmpty()) {
            if (partitionBy != null && !partitionBy.isEmpty()) {
                windowFunc.append(" ");
            }
            windowFunc.append("ORDER BY ").append(String.join(", ", orderBy));
        }
        
        windowFunc.append(")");
        return windowFunc.toString();
    }

    /**
     * Escape MySQL special characters in string values.
     * 
     * @param value string value to escape
     * @return escaped string value
     */
    public String escapeString(String value) {
        if (value == null) {
            return null;
        }
        
        return value.replace("\\", "\\\\")
                   .replace("'", "\\'")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t")
                   .replace("\b", "\\b")
                   .replace("\f", "\\f");
    }

    /**
     * Build MySQL UPSERT (INSERT...ON DUPLICATE KEY UPDATE) with specific update values.
     * 
     * @param tableName target table name
     * @param insertColumns columns for insert
     * @param updateValues specific values for update (column -> value)
     * @return MySQL UPSERT query string
     */
    public String buildUpsertWithValues(String tableName, List<String> insertColumns, Map<String, String> updateValues) {
        StringBuilder query = new StringBuilder();
        query.append("INSERT INTO ").append(tableName).append(" (");
        
        // Add column names
        query.append(String.join(", ", insertColumns));
        query.append(") VALUES (");
        
        // Add parameter placeholders
        String placeholders = insertColumns.stream()
                .map(col -> ":" + col)
                .collect(Collectors.joining(", "));
        query.append(placeholders);
        
        query.append(") ON DUPLICATE KEY UPDATE ");
        
        // Add specific update values
        String updateClause = updateValues.entrySet().stream()
                .map(entry -> entry.getKey() + " = " + entry.getValue())
                .collect(Collectors.joining(", "));
        query.append(updateClause);
        
        return query.toString();
    }
}
