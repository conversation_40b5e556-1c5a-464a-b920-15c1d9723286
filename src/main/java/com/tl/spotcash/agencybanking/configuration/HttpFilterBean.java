package com.tl.spotcash.agencybanking.configuration;

import com.google.common.io.CharStreams;
import com.tl.spotcash.agencybanking.entity.SpAgencyRequestLog;
import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;

@Component
public class HttpFilterBean extends GenericFilterBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpFilterBean.class);

    @Autowired
    CrudService crudService;

    @Autowired
    Environment environment;

    @PostConstruct
    private void onLoad() {
        LOGGER.info("datasource.spotcash.validateIp ? {}", environment.getRequiredProperty("datasource.spotcash.validateIp", Boolean.class));
        LOGGER.info("datasource.spotcash.proxyIp {}", environment.getRequiredProperty("datasource.spotcash.proxyIp"));
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain)
            throws IOException, ServletException {
        final HttpServletRequest currentRequest = (HttpServletRequest) servletRequest;
        final HttpServletResponse currentResponse = (HttpServletResponse) servletResponse;

        StringBuffer requestURL = currentRequest.getRequestURL();
        LOGGER.info("Request URL: {}, remoteAddress :: {}, forwardedFor :: {}, actualIp :: {}",
                requestURL, servletRequest.getRemoteAddr(),
                ((HttpServletRequest) servletRequest).getHeader("X-FORWARDED-FOR"),
                SharedFunctions.getRemoteIpAddress((HttpServletRequest) servletRequest, environment));

        CachedBodyHttpServletRequest cachedBodyHttpServletRequest =
                new CachedBodyHttpServletRequest(currentRequest);
        LOGGER.info("Request Body: {}", CharStreams.toString(cachedBodyHttpServletRequest.getReader()));
        SpAgencyRequestLog requestLog = new SpAgencyRequestLog();
        requestLog.setSourceIp(SharedFunctions.getRemoteIpAddress((HttpServletRequest) servletRequest, environment));
        requestLog.setUrl(((HttpServletRequest) servletRequest).getRequestURI());
        requestLog.setRequestBody(CharStreams.toString(cachedBodyHttpServletRequest.getReader()));
        requestLog.setTime(LocalDateTime.now());
        requestLog.setThread(Thread.currentThread().getName());
        crudService.save(requestLog);
        try {
            chain.doFilter(cachedBodyHttpServletRequest, servletResponse);
        } finally {
            int status = currentResponse.getStatus();
            LOGGER.info("Response status: {}", status);
        }
    }
}
