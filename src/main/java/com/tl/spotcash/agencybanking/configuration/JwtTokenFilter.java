package com.tl.spotcash.agencybanking.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.spotcash.agencybanking.controller.api.ApiRequestController;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.entity.SpAgencyEncryptionKey;
import com.tl.spotcash.agencybanking.entity.SpAgentStores;
import com.tl.spotcash.agencybanking.entity.SpStoreUsers;
import com.tl.spotcash.agencybanking.entity.SpTokenKey;
import com.tl.spotcash.agencybanking.enums.ResponseCodes;
import com.tl.spotcash.agencybanking.enums.StoreUser;
import com.tl.spotcash.agencybanking.service.TransactionService;
import com.tl.spotcash.agencybanking.utils.Crypt;
import com.tl.spotcash.agencybanking.utils.JwtTokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.util.Date;
import java.util.Objects;

@Component
public class JwtTokenFilter extends OncePerRequestFilter {
    private final JwtTokenUtil jwtUtil;
    private final CrudTransactionController crudTransactions;
    private final Environment environment;
    private final TransactionService transactionService;
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtTokenFilter.class);

    public JwtTokenFilter(JwtTokenUtil jwtUtil, CrudTransactionController crudTransactions,
                          Environment environment, TransactionService transactionService) {
        this.jwtUtil = jwtUtil;
        this.crudTransactions = crudTransactions;
        this.environment = environment;
        this.transactionService = transactionService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (!hasAuthorizationBearer(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        String token = getAccessToken(request);

        //Get the token headers from the request and add them to the response
        response.setHeader("WatchDog", request.getHeader("WatchDog"));
        response.setHeader("Refresh-Token", request.getHeader("Refresh-Token"));

        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        boolean includeDeviceId = false;

        //If the deviceId is not empty, then the token belongs to APK's version 1.9+ and thus you need
        // to fetch the dynamic encryption key that signed the token and also encrypted its claims.
        if(!deviceId.isEmpty()){
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
                includeDeviceId = true;
            }
        }

        if (!jwtUtil.validateAccessToken(token)) {
            //If the refresh-Token is still valid generate a new Token
            String refreshToken = getRefreshToken(request);
            if(jwtUtil.validateAccessToken(refreshToken)){
                try {
                    //First check if the Token wasn't invalidated from the DB
                    SpTokenKey spToken = crudTransactions.fetchTokenAndKeyWithToken(token);
                    if(Objects.equals(spToken.getValidToken(), BigInteger.ONE)){
                        //NOTE: Reuse the secretKey of the expired token inorder to be able,
                        // to decrypt the body of the request as it was encrypted using that secretKey
                        String secretKey =  spToken.getDataKey();

                        String agentId = jwtUtil.getClaim(refreshToken, StoreUser.AGENT_ID.getValue());
                        String storeUserId = jwtUtil.getClaim(refreshToken, StoreUser.STORE_USER_ID.getValue());
                        SpStoreUsers storeUser = crudTransactions.fetchStoreUserWithId(storeUserId);

                        // Update the token with the new generated token
                        token = jwtUtil.generateAccessToken(storeUser, agentId, secretKey, encryptionKey, includeDeviceId);

                        //Generate a Refresh-Token
                        String newRefreshToken = jwtUtil.generateRefreshToken(storeUser, agentId);

                        //Save the new token to the DB
                        SpTokenKey spTokenKey = new SpTokenKey();
                        spTokenKey.setStoreUserId(storeUser.getStoreUserId());
                        spTokenKey.setJwtToken(token);
                        spTokenKey.setDataKey(secretKey);
                        spTokenKey.setValidToken(BigInteger.ONE);
                        spTokenKey.setRequestTime(new Date(System.currentTimeMillis()));
                        crudTransactions.saveUpdateTokenAndKey(spTokenKey);

                        //Update the token response headers with the updated tokens
                        response.setHeader("WatchDog", "Bearer " + token);
                        response.setHeader("Refresh-Token", "Bearer " + newRefreshToken);
                        LOGGER.info("A NEW JWT HAS BEEN CREATED!");
                    }
                    else{
                        // The token was invalidated from the DB
                        filterChain.doFilter(request, response);
                        return;
                    }
                }
                catch (Exception e) {
                    LOGGER.error("ERROR GENERATING A NEW JWT :: {}", e.getMessage());
                    filterChain.doFilter(request, response);
                    return;
                }
            }
            else {
                filterChain.doFilter(request, response);
                return;
            }
        }

        setAuthenticationContext(token, request, encryptionKey);
        filterChain.doFilter(request, response);
    }

    private boolean hasAuthorizationBearer(HttpServletRequest request) {
        String header = request.getHeader("WatchDog");

        if (ObjectUtils.isEmpty(header) || !header.startsWith("Bearer")) {
            return false;
        }

        return true;
    }

    public boolean hasRefreshTokenBearer(HttpServletRequest request) {
        String header = request.getHeader("Refresh-Token");

        if (ObjectUtils.isEmpty(header) || !header.startsWith("Bearer")) {
            return false;
        }

        return true;
    }

    private String getAccessToken(HttpServletRequest request) {
        return request.getHeader("WatchDog").split(" ")[1].trim();
    }

    public String getRefreshToken(HttpServletRequest request) {
        return request.getHeader("Refresh-Token").split(" ")[1].trim();
    }

    public void setAuthenticationContext(String token, HttpServletRequest request, String encryptionKey) {
        UserDetails userDetails = getUserDetails(token, encryptionKey);

        UsernamePasswordAuthenticationToken
                authentication = new UsernamePasswordAuthenticationToken(userDetails, null, null);

        authentication.setDetails(
                new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    public UserDetails getUserDetails(String token, String encryptionKey) {
        //String[] jwtSubject = jwtUtil.getSubject(token).split(",");

        String jwtSubject = jwtUtil.getSubject(token);
        String storeUserId = Crypt.decrypt(jwtSubject.replace("\n",""), encryptionKey);
        //Fetch the user from the DB.
        return crudTransactions.fetchStoreUserWithId(storeUserId.trim());
    }
}
