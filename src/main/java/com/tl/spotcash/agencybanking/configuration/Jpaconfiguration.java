package com.tl.spotcash.agencybanking.configuration;

import com.tl.spotcash.agencybanking.custommodels.SwitchConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.naming.NamingException;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@Configuration
@EnableJpaRepositories(basePackages = "com.tl.spotcash.agencybanking.repository",
        entityManagerFactoryRef = "entityManagerFactory",
        transactionManagerRef = "transactionManager")
@EnableTransactionManagement
public class Jpaconfiguration {

    @Autowired
    private Environment environment;

    @Value("${datasource.spotcash.fetchMemberImages}")
    private String fetchImagesString;
//    @Value("#{'${datasource.spotcash.fetchMemberImages}'.split(',')}")
//    private List<String> fetchImages;

    @Value("${datasource.spotcash.maxPoolSize:10}")
    private int maxPoolSize;

    /*
     * Populate SpringBoot DataSourceProperties object directly from application.yml
     * based on prefix.Thanks to .yml, Hierachical data is mapped out of the box with matching-name
     * properties of DataSourceProperties object].
     */
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "datasource.spotcash")
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }

    //add new datascource properties
    /*@Bean
    @ConfigurationProperties("datasource.secondary")
    public DataSourceProperties secondaryDataSourceProperties() {
        return new DataSourceProperties();
    }*/

    /*
     * Configure HikariCP pooled DataSource.
     */
    @Primary
    @Bean(name = "primaryDS")
    public DataSource dataSource() {
        DataSourceProperties dataSourceProperties = dataSourceProperties();
        HikariDataSource dataSource = (HikariDataSource) DataSourceBuilder
                .create(dataSourceProperties.getClassLoader())
                .driverClassName(dataSourceProperties.getDriverClassName())
                .url(dataSourceProperties.getUrl())
                .username(dataSourceProperties.getUsername())
                .password(dataSourceProperties.getPassword())
                .type(HikariDataSource.class)
                .build();
        dataSource.setMaximumPoolSize(maxPoolSize);

        // MySQL-specific optimizations
        dataSource.addDataSourceProperty("cachePrepStmts", true);
        dataSource.addDataSourceProperty("prepStmtCacheSize", 250);
        dataSource.addDataSourceProperty("prepStmtCacheSqlLimit", 2048);
        dataSource.addDataSourceProperty("useServerPrepStmts", true);
        dataSource.addDataSourceProperty("useLocalSessionState", true);
        dataSource.addDataSourceProperty("rewriteBatchedStatements", true);
        dataSource.addDataSourceProperty("cacheResultSetMetadata", true);
        dataSource.addDataSourceProperty("cacheServerConfiguration", true);
        dataSource.addDataSourceProperty("elideSetAutoCommits", true);
        dataSource.addDataSourceProperty("maintainTimeStats", false);

        // MySQL character encoding for proper UTF-8 support
        // Use 'utf8' instead of 'utf8mb4' for compatibility with older MySQL connectors
        dataSource.addDataSourceProperty("characterEncoding", "utf8");
        dataSource.addDataSourceProperty("useUnicode", true);

        // MySQL SSL and security settings
        dataSource.addDataSourceProperty("useSSL", false);
        dataSource.addDataSourceProperty("allowPublicKeyRetrieval", true);

        // MySQL timezone handling
        dataSource.addDataSourceProperty("serverTimezone", "UTC");
        dataSource.addDataSourceProperty("useLegacyDatetimeCode", false);

        // MySQL connection reliability
        dataSource.addDataSourceProperty("autoReconnect", true);
        dataSource.addDataSourceProperty("failOverReadOnly", false);
        dataSource.addDataSourceProperty("maxReconnects", 3);
        dataSource.addDataSourceProperty("initialTimeout", 2);
        dataSource.setMaxLifetime(environment
                .getProperty("datasource.spotcash.maxLifetime", Integer.class, 1800000));
        dataSource.setIdleTimeout(environment
                .getProperty("datasource.spotcash.idleTimeout", Integer.class, 600000));//in seconds
        dataSource.setConnectionTimeout(environment
                .getProperty("datasource.spotcash.connectionTimeout", Integer.class, 300000));
        
        return dataSource;
    }

    /*@Bean(name = "secondaryDS")
    public DataSource secondaryDS() {
        DataSourceProperties dataSourceProperties = secondaryDataSourceProperties();
        HikariDataSource dataSource = (HikariDataSource) DataSourceBuilder
                .create(dataSourceProperties.getClassLoader())
                .driverClassName(dataSourceProperties.getDriverClassName())
                .url(dataSourceProperties.getUrl())
                .username(dataSourceProperties.getUsername())
                .password(dataSourceProperties.getPassword())
                .type(HikariDataSource.class)
                .build();
        dataSource.setMaximumPoolSize(maxPoolSize);
        dataSource.addDataSourceProperty("cachePrepStmts", true);
        dataSource.addDataSourceProperty("prepStmtCacheSize", 250);
        dataSource.addDataSourceProperty("prepStmtCacheSqlLimit", 2048);
        dataSource.addDataSourceProperty("useServerPrepStmts", true);
        dataSource.addDataSourceProperty("cacheResultSetMetadata", true);
        dataSource.setMaxLifetime(environment
                .getProperty("datasource.secondary.maxLifetime", Integer.class, 1800000));
        dataSource.setIdleTimeout(environment
                .getProperty("datasource.secondary.idleTimeout", Integer.class, 600000));//in seconds
        dataSource.setConnectionTimeout(environment
                .getProperty("datasource.secondary.connectionTimeout", Integer.class, 300000));

        return dataSource;
    }*/

    /*
     * Entity Manager Factory setup.
     */
    @Primary
    @Bean(name = "primaryFactory")
    public LocalContainerEntityManagerFactoryBean primaryEntityManagerFactory() throws NamingException {
        LocalContainerEntityManagerFactoryBean factoryBean = new LocalContainerEntityManagerFactoryBean();
        factoryBean.setDataSource(dataSource());
        factoryBean.setPackagesToScan(new String[]{"com.tl.spotcash.agencybanking.entity"});
        factoryBean.setJpaVendorAdapter(jpaVendorAdapter());
        factoryBean.setJpaProperties(jpaProperties());
        return factoryBean;
    }

    /*@Bean(name = "secondaryFactory")
    public LocalContainerEntityManagerFactoryBean secondaryEntityManagerFactory() throws NamingException {
        LocalContainerEntityManagerFactoryBean factoryBean = new LocalContainerEntityManagerFactoryBean();
        factoryBean.setDataSource(secondaryDS());
        factoryBean.setPackagesToScan(new String[]{"com.tl.spotcash.agencybanking.entity"});
        factoryBean.setJpaVendorAdapter(jpaVendorAdapter());
        factoryBean.setJpaProperties(secJpaProperties());
        return factoryBean;
    }*/

    /*
     * Provider specific adapter.
     */
    @Bean
    public JpaVendorAdapter jpaVendorAdapter() {
        HibernateJpaVendorAdapter hibernateJpaVendorAdapter = new HibernateJpaVendorAdapter();
        return hibernateJpaVendorAdapter;
    }
    /*
     * Here you can specify any provider specific properties.
     * Enhanced with MySQL-specific optimizations.
     */
    private Properties jpaProperties() {
        Properties properties = new Properties();

        // Basic Hibernate configuration
        properties.put("hibernate.dialect", environment.getRequiredProperty("datasource.spotcash.hibernate.dialect"));
//        properties.put("hibernate.hbm2ddl.auto", environment.getRequiredProperty("datasource.spotcash.hibernate.hbm2ddl.method"));
        if (!environment.getRequiredProperty("datasource.spotcash.defaultSchema").isEmpty()) {
            properties.put("hibernate.default_schema", environment.getRequiredProperty("datasource.spotcash.defaultSchema"));
        }

        // MySQL-specific Hibernate optimizations
        // Use 'utf8' instead of 'utf8mb4' for compatibility
        properties.put("hibernate.connection.characterEncoding", "utf8");
        properties.put("hibernate.connection.useUnicode", "true");
        properties.put("hibernate.connection.charSet", "UTF-8");

        // MySQL performance optimizations
        properties.put("hibernate.jdbc.batch_size", "50");
        properties.put("hibernate.jdbc.fetch_size", "100");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.jdbc.batch_versioned_data", "true");

        // MySQL connection optimizations
        properties.put("hibernate.connection.provider_disables_autocommit", "true");
        properties.put("hibernate.connection.autocommit", "false");

        // MySQL query optimizations
        properties.put("hibernate.query.plan_cache_max_size", "2048");
        properties.put("hibernate.query.plan_parameter_metadata_max_size", "128");

        // MySQL-specific SQL generation optimizations
        properties.put("hibernate.use_sql_comments", "false");
        properties.put("hibernate.format_sql", environment.getProperty("datasource.spotcash.hibernate.format_sql", "false"));
        properties.put("hibernate.show_sql", environment.getProperty("datasource.spotcash.hibernate.show_sql", "false"));
        properties.put("hibernate.generate_statistics", "false");

        // MySQL session management
        properties.put("hibernate.current_session_context_class", "thread");

        return properties;
    }


    /*private Properties secJpaProperties() {
        Properties properties = new Properties();
        properties.put("hibernate.dialect", environment.getRequiredProperty("datasource.secondary.hibernate.dialect"));
        if (!environment.getRequiredProperty("datasource.secondary.defaultSchema").isEmpty()) {
            properties.put("hibernate.default_schema", environment.getRequiredProperty("datasource.secondary.defaultSchema"));
        }
        return properties;
    }*/

    @Bean
    @Autowired
    public PlatformTransactionManager transactionManager(EntityManagerFactory emf) {
        JpaTransactionManager txManager = new JpaTransactionManager();
        txManager.setEntityManagerFactory(emf);
        return txManager;
    }

    @Bean(name = "switchConfig")
    public SwitchConfig switchConfig() {
        SwitchConfig config = new SwitchConfig();
        List<String> fetchImages=Arrays.asList(fetchImagesString.split(","));
        config.setImage(fetchImages);
        return config;
    }
    
 
}
