package com.tl.spotcash.agencybanking.configuration;

import com.tl.spotcash.agencybanking.custommodels.SpotcashUtilities;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class CustomPasswordEncoder implements PasswordEncoder {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomPasswordEncoder.class);

    @Override
    public String encode(CharSequence rawPassword) {

        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update( String.valueOf(rawPassword).getBytes());
            byte byteData[] = md.digest();
            //convert the byte to hex format method 1
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < byteData.length; i++) {
                sb.append(Integer.toString((byteData[i] & 0xff) + 0x100, 16).substring(1));
            }
            return sb.toString();
        }
        catch (NoSuchAlgorithmException ex) {
            LOGGER.info("ERROR CREATING HASHED PASSWORD " + ex.toString());
            throw new IllegalStateException("ERROR CREATING HASHED PASSWORD " + ex);
        }
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        String hashedPassword = new SpotcashUtilities().md5password(String.valueOf(rawPassword));

        LOGGER.debug("" +
                "FETCHED HASHED PASSWORD: " + hashedPassword);
        LOGGER.debug("STORED HASHED PASSWORD: " + encodedPassword);
        return hashedPassword.equals(encodedPassword);
    }

}
