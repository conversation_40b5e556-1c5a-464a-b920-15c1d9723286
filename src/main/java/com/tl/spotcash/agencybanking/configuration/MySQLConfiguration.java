package com.tl.spotcash.agencybanking.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * MySQL-specific configuration class.
 * Provides MySQL-optimized settings and configurations.
 * 
 * <AUTHOR>
 */
@Configuration
public class MySQLConfiguration {

    @Autowired
    private Environment environment;

    /**
     * MySQL-optimized JdbcTemplate configuration.
     * 
     * @param dataSource the primary data source
     * @return configured JdbcTemplate with MySQL optimizations
     */
    @Bean(name = "mysqlJdbcTemplate")
    public JdbcTemplate mysqlJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        
        // Set MySQL-specific optimizations
        jdbcTemplate.setFetchSize(1000); // Optimize fetch size for MySQL
        jdbcTemplate.setMaxRows(0); // No limit on max rows
        jdbcTemplate.setQueryTimeout(30); // 30 seconds timeout
        
        return jdbcTemplate;
    }

    /**
     * Get MySQL-specific Hibernate properties.
     * 
     * @return Properties object with MySQL optimizations
     */
    public Properties getMySQLHibernateProperties() {
        Properties properties = new Properties();
        
        // MySQL-specific Hibernate properties
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQL8Dialect");
        properties.put("hibernate.connection.characterEncoding", "utf8mb4");
        properties.put("hibernate.connection.useUnicode", "true");
        properties.put("hibernate.connection.charSet", "UTF-8");
        
        // MySQL performance optimizations
        properties.put("hibernate.jdbc.batch_size", "50");
        properties.put("hibernate.jdbc.fetch_size", "100");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.jdbc.batch_versioned_data", "true");
        
        // MySQL-specific connection properties
        properties.put("hibernate.connection.provider_disables_autocommit", "true");
        properties.put("hibernate.connection.autocommit", "false");
        
        // MySQL query optimizations
        properties.put("hibernate.query.plan_cache_max_size", "2048");
        properties.put("hibernate.query.plan_parameter_metadata_max_size", "128");
        
        // MySQL-specific SQL generation
        properties.put("hibernate.use_sql_comments", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hibernate.generate_statistics", "false");
        
        // MySQL transaction settings
        properties.put("hibernate.transaction.coordinator_class", "jta");
        properties.put("hibernate.current_session_context_class", "thread");
        
        return properties;
    }

    /**
     * Get MySQL-specific DataSource properties for HikariCP.
     * 
     * @return Properties object with MySQL-optimized HikariCP settings
     */
    public Properties getMySQLDataSourceProperties() {
        Properties properties = new Properties();
        
        // MySQL-specific connection properties
        properties.put("cachePrepStmts", "true");
        properties.put("prepStmtCacheSize", "250");
        properties.put("prepStmtCacheSqlLimit", "2048");
        properties.put("useServerPrepStmts", "true");
        properties.put("useLocalSessionState", "true");
        properties.put("rewriteBatchedStatements", "true");
        properties.put("cacheResultSetMetadata", "true");
        properties.put("cacheServerConfiguration", "true");
        properties.put("elideSetAutoCommits", "true");
        properties.put("maintainTimeStats", "false");
        
        // MySQL character encoding - use utf8 for compatibility
        properties.put("characterEncoding", "utf8");
        properties.put("useUnicode", "true");
        
        // MySQL SSL and security
        properties.put("useSSL", "false");
        properties.put("allowPublicKeyRetrieval", "true");
        
        // MySQL timezone handling
        properties.put("serverTimezone", "UTC");
        properties.put("useLegacyDatetimeCode", "false");
        
        // MySQL performance settings
        properties.put("autoReconnect", "true");
        properties.put("failOverReadOnly", "false");
        properties.put("maxReconnects", "3");
        properties.put("initialTimeout", "2");
        
        return properties;
    }

    /**
     * Get MySQL-specific query hints and optimizations.
     * 
     * @return Properties object with MySQL query hints
     */
    public Properties getMySQLQueryHints() {
        Properties hints = new Properties();
        
        // MySQL index hints
        hints.put("use_index_hint", "USE INDEX");
        hints.put("force_index_hint", "FORCE INDEX");
        hints.put("ignore_index_hint", "IGNORE INDEX");
        
        // MySQL join hints
        hints.put("straight_join", "STRAIGHT_JOIN");
        hints.put("sql_buffer_result", "SQL_BUFFER_RESULT");
        hints.put("sql_cache", "SQL_CACHE");
        hints.put("sql_no_cache", "SQL_NO_CACHE");
        
        // MySQL optimizer hints
        hints.put("high_priority", "HIGH_PRIORITY");
        hints.put("low_priority", "LOW_PRIORITY");
        hints.put("quick", "QUICK");
        
        return hints;
    }

    /**
     * Get MySQL-specific storage engine recommendations.
     * 
     * @return Properties object with storage engine settings
     */
    public Properties getMySQLStorageEngineSettings() {
        Properties settings = new Properties();
        
        // InnoDB settings (recommended for transactional tables)
        settings.put("default_storage_engine", "InnoDB");
        settings.put("innodb_buffer_pool_size", "128M");
        settings.put("innodb_log_file_size", "64M");
        settings.put("innodb_flush_log_at_trx_commit", "1");
        settings.put("innodb_file_per_table", "ON");
        
        // MyISAM settings (for read-heavy tables)
        settings.put("key_buffer_size", "32M");
        settings.put("myisam_sort_buffer_size", "8M");
        
        return settings;
    }

    /**
     * Get MySQL-specific data type mappings.
     * 
     * @return Properties object with MySQL data type recommendations
     */
    public Properties getMySQLDataTypeMappings() {
        Properties mappings = new Properties();
        
        // String types
        mappings.put("short_string", "VARCHAR(255)");
        mappings.put("medium_string", "VARCHAR(1000)");
        mappings.put("long_string", "TEXT");
        mappings.put("very_long_string", "LONGTEXT");
        
        // Numeric types
        mappings.put("small_int", "TINYINT");
        mappings.put("medium_int", "INT");
        mappings.put("big_int", "BIGINT");
        mappings.put("decimal", "DECIMAL(10,2)");
        mappings.put("money", "DECIMAL(15,2)");
        
        // Date/Time types
        mappings.put("date_only", "DATE");
        mappings.put("time_only", "TIME");
        mappings.put("datetime", "DATETIME");
        mappings.put("timestamp", "TIMESTAMP");
        
        // Binary types
        mappings.put("small_binary", "VARBINARY(255)");
        mappings.put("medium_binary", "BLOB");
        mappings.put("large_binary", "LONGBLOB");
        
        // JSON type (MySQL 5.7+)
        mappings.put("json_data", "JSON");
        
        // Boolean type
        mappings.put("boolean", "BOOLEAN");
        
        return mappings;
    }

    /**
     * Get MySQL-specific index recommendations.
     * 
     * @return Properties object with MySQL index best practices
     */
    public Properties getMySQLIndexRecommendations() {
        Properties recommendations = new Properties();
        
        // Index types
        recommendations.put("primary_key", "PRIMARY KEY");
        recommendations.put("unique_index", "UNIQUE INDEX");
        recommendations.put("regular_index", "INDEX");
        recommendations.put("fulltext_index", "FULLTEXT INDEX");
        recommendations.put("spatial_index", "SPATIAL INDEX");
        
        // Index algorithms
        recommendations.put("btree_algorithm", "USING BTREE");
        recommendations.put("hash_algorithm", "USING HASH");
        
        // Index hints
        recommendations.put("max_index_length", "767"); // For utf8mb4
        recommendations.put("recommended_key_length", "255");
        
        return recommendations;
    }
}
