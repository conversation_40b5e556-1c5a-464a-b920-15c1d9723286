package com.tl.spotcash.agencybanking.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 *
 * <AUTHOR>
 */
@Configuration
@EnableSwagger2
public class SwaggerConfiguration {

    @Autowired
    private Environment environment;

    @Bean
    public Docket api() {
        boolean swaggerswitch = environment.getRequiredProperty("datasource.spotcash.swaggerEnable") != null
                && environment.getRequiredProperty("datasource.spotcash.swaggerEnable").equals("true");

        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tl.spotcash.agencybanking.controller"))
                .paths(PathSelectors.any())
                .build()
                .apiInfo(apiInfo())
                .apiInfo(apiInfo()).enable(swaggerswitch);

    }

    private ApiInfo apiInfo() {
        ApiInfo apiInfo = new ApiInfo(
                "Spotcash Agency microservice API",
                "Spotcash Agency microservice rest API.",
                "v1.0.0",
                "Terms of service",
                "<EMAIL>",
                "License of API",
                "#");
        return apiInfo;
    }
}
