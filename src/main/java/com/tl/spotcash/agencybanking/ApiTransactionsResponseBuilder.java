package com.tl.spotcash.agencybanking;

import com.tl.spotcash.agencybanking.custommodels.SpotcashUtilities;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposIntegratorResponse;
import com.tl.spotcash.agencybanking.entity.SpAirtimeVouchers;
import com.tl.spotcash.agencybanking.utils.AesEncryption;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 *
 * <AUTHOR>
 */
public class ApiTransactionsResponseBuilder {

	private static final Logger LOGGER = LoggerFactory.getLogger(ApiTransactionsResponseBuilder.class);

	/**
	 *
	 * @param voucherList
	 * @return
	 */
	public ApiResponseModel airtimeVoucher(List<SpAirtimeVouchers> voucherList) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		if (null != voucherList) {
			for (SpAirtimeVouchers thevoucherdata : voucherList) {
				if (thevoucherdata.getId().compareTo(BigDecimal.ZERO) > 0) {
					apiresponse.setResponseheader_sc("00");
					apiresponse.setSd("Transaction Success");
					apiresponse.setSt("10007");
					apiresponse.setResponsedata_sc("0000|Transaction Success");
					apiresponse.setSod(thevoucherdata.getSerialNumber());
					apiresponse.setXid(new SpotcashUtilities().decode(thevoucherdata.getPin()));
					apiresponse.setAmt(thevoucherdata.getAmount().toString());
					apiresponse.setResponseheader_sc("00");
					apiresponse.setFeeamt(thevoucherdata.getExpiryDate());
					apiresponse.setEod(thevoucherdata.getPurchaseOn().toString());
					apiresponse.setTransaction_type("Airtime");
				} else {
					return failed_SV_AirtimePurchase();
				}
			}
			return apiresponse;
		} else {
			return failed_SV_AirtimePurchase();
		}
	}


	public ApiResponseModel AgentBalanceResponse(HttpProcessorRequest requestData, String balance, String agentId, Environment env) throws Exception {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("00");
		apiresponse.setSd("Transaction Success");
		apiresponse.setTrx_id("ABSABE001");
		apiresponse.setSt("10007");
		apiresponse.setResponsedata_sc(AesEncryption.encrypt("BALANCE : " + balance + " KES", AesEncryption.encodeKey(env.getRequiredProperty("secret.key"))));
		apiresponse.setMsm(requestData.getRequestData().getAgentPhoneNumber());
		apiresponse.setTransaction_type("SABE");
		apiresponse.setAgent_id(agentId);
		return apiresponse;
	}

	/**
	 *
	 * @return
	 */
	public ApiResponseModel failed_SV_AirtimePurchase() {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("1008");
		apiresponse.setSd("Transaction Failed");
		apiresponse.setTrx_id("00000");
		apiresponse.setSt("10008");
		apiresponse.setResponsedata_sc("9542|Insufficient float");
		apiresponse.setMsm("0000");
		apiresponse.setTransaction_type("FailedAirtime");
		return apiresponse;
	}

    public ApiResponseModel failedAgentBalance() {
        ApiResponseModel apiresponse = new ApiResponseModel();
        apiresponse.setResponseheader_sc("05");
        apiresponse.setSd("Insufficient Float Balance");
        apiresponse.setTrx_id("00000");
        apiresponse.setSt("10008");
        apiresponse.setResponsedata_sc("9542|Insufficient float");
        apiresponse.setMsm("0000");
        apiresponse.setTransaction_type("FailedAirtime");
        return apiresponse;
    }


	/**
	 *
	 * @param requestData
	 * @param balance
	 * @return
	 */
	public ApiResponseModel process_SABE(HttpProcessorRequest requestData, BigDecimal balance) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("00");
		apiresponse.setSd("Transaction Success");
		apiresponse.setTrx_id("ABSABE001");
		apiresponse.setSt("10007");
		apiresponse.setResponsedata_sc("0000|BALANCE : " + balance + " KES");
		apiresponse.setMsm(requestData.getHeader().getMsisdn());
		apiresponse.setTransaction_type("SABE");
		return apiresponse;
	}

	/**
	 *
	 * @param requestData
	 * @param balance
	 * @return
	 */
	public ApiResponseModel process_AgentBalanceResponse(HttpProcessorRequest requestData, String balance) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("00");
		apiresponse.setSd("Transaction Success");
		apiresponse.setTrx_id("ABSABE001");
		apiresponse.setSt("10007");
		apiresponse.setResponsedata_sc("BALANCE : " + balance + " KES");
		apiresponse.setMsm(requestData.getRequestData().getAgentPhoneNumber());
		apiresponse.setTransaction_type("SABE");
		return apiresponse;
	}

	/**
	 *
	 * @param requestData
	 * @param balance
	 * @param agentId
	 * @return
	 */
	public ApiResponseModel process_AgentBalanceResponse(HttpProcessorRequest requestData, String balance, String agentId) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("00");
		apiresponse.setSd("Transaction Success");
		apiresponse.setTrx_id("ABSABE001");
		apiresponse.setSt("10007");
		apiresponse.setResponsedata_sc("BALANCE : " + balance + " KES");
		apiresponse.setMsm(requestData.getRequestData().getAgentPhoneNumber());
		apiresponse.setTransaction_type("SABE");
		apiresponse.setAgent_id(agentId);
		return apiresponse;
	}

	/**
	 *
	 * @return
	 */
	protected ApiResponseModel failedAuthResponse() {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("9999");
		apiresponse.setSd("Authentication Failed");
		apiresponse.setTrx_id("00000");
		apiresponse.setSt("10008");
		apiresponse.setResponsedata_sc("9292|Customer authentication failed");
		apiresponse.setMsm("0000");
		apiresponse.setTransaction_type("FailedAuthResponse");
		return apiresponse;
	}

	/**
	 *
	 */
	protected ApiResponseModel missingTransactionDetails(String missingInfo) {
		ApiResponseModel apiResponse = new ApiResponseModel();
		apiResponse.setSd(missingInfo);
		apiResponse.setHttpStatus("400");
		return apiResponse;
	}

	/**
	 *
	 * @param requestData
	 * @param trxresponse
	 * @return
	 */
	public ApiResponseModel process_SLOG(HttpProcessorRequest requestData, Map<String, Object> trxresponse) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		if (trxresponse != null) {
			apiresponse.setResponseheader_sc("00");
			apiresponse.setSd("Transaction Success");
			apiresponse.setTrx_id(requestData.getRequestData().getTrxId());
			apiresponse.setSt("10007");
			apiresponse.setTransaction_type("SLOG");
			apiresponse.setTransactionData(trxresponse);
		} else {
			apiresponse.setResponseheader_sc("00");
			apiresponse.setSd("Transaction Failed");
			apiresponse.setTrx_id("SP00ZYB46"); //Failed transaction id static value, mandatory field, fed it with arbitrary value
			apiresponse.setSt("10008");
			apiresponse.setResponsedata_sc("8866|Unable to print statement");
			apiresponse.setMsm("00000"); //5 zeros for a failed transaction
			apiresponse.setTransaction_type("SLOGP");
			apiresponse.setTransactionData(trxresponse);
		}
		return apiresponse;
	}

	/**
	 *
	 * @param isoresponseData
	 * @param msisdn
	 * @return
	 */
	protected ApiResponseModel transTempPaymentResponse(CorebankingResponse isoresponseData, String msisdn) {

		ApiResponseModel apiresponse = new ApiResponseModel();
		if (isoresponseData != null) {
			if (!"00".equalsIgnoreCase(isoresponseData.getCorebankingResponseCode())) {
				//return transactionFailureResponse();
				return customFailureResponse(isoresponseData);
//				return transactionFailureResponseWithReason(isoresponseData.getCorebankingResponseMessage()+" "+isoresponseData.getCorebankingResponseDescription());
			} else {
				apiresponse.setResponseheader_sc("00");
				apiresponse.setSd("Transaction Success");
				LOGGER.info("TRANSACTION ID"+isoresponseData.getSpotcash_transaction_id());
				apiresponse.setTrx_id(isoresponseData.getSpotcash_transaction_id());
				apiresponse.setSt("10007");
				apiresponse.setTransaction_type("TRANSACTIONAL");
				apiresponse.setTrx_balance(isoresponseData.getCorebankingBalance());

				if (isoresponseData.isIsPrintable() == true) {
					apiresponse.setMsm(isoresponseData.getCorebankingResponseMessage());
				} else {
					apiresponse.setMsm(msisdn);
				}
			}
		} else {
			return transactionFailureResponse();
//			return transactionFailureResponseWithReason(isoresponseData.getCorebankingResponseMessage()+" "+isoresponseData.getCorebankingResponseDescription());
		}
		return apiresponse;
	}

	protected ApiResponseModel customFailureResponse(CorebankingResponse response){
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc(response.getCorebankingResponseCode());
		apiresponse.setSd(response.getCorebankingResponseMessage());
		apiresponse.setTrx_id("SP00ZYB46"); //Failed transaction id static value, mandatory field, fed it with arbitrary value
		apiresponse.setSt("10008");
		//apiresponse.setResponsedata_sc("Transaction processing failure");
		apiresponse.setMsm("00000"); //5 zeros for a failed transaction
		apiresponse.setTransaction_type("TRANSACTIONAL");
		apiresponse.setResponsedata_sc(response.getCorebankingResponseMessage());
		//apiresponse.setTrx_id(isoresponseData.getSpotcash_transaction_id());
		LOGGER.info("COREBANKING RESPONSE MESSAGE "+ response.toString());
		apiresponse.setSt("10007");
		apiresponse.setTransaction_type("TRANSACTIONAL");
		return apiresponse;
	}

	/**
	 *
	 * @return
	 */
	protected ApiResponseModel transactionFailureResponse() {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("01");
		apiresponse.setSd("Transaction Failed");
		apiresponse.setTrx_id("SP00ZYB46"); //Failed transaction id static value, mandatory field, fed it with arbitrary value
		apiresponse.setSt("10008");
		apiresponse.setResponsedata_sc("Transaction processing failure");
		apiresponse.setMsm("00000"); //5 zeros for a failed transaction
		apiresponse.setTransaction_type("TRANSACTIONAL");
		return apiresponse;
	}

	protected ApiResponseModel transactionFailureResponseWithReason(String reason) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setResponseheader_sc("00");
		apiresponse.setSd("Transaction Failed");
		apiresponse.setTrx_id("SP00ZYB46"); //Failed transaction id static value, mandatory field, fed it with arbitrary value
		apiresponse.setSt("10008");
		apiresponse.setReason(reason);
		apiresponse.setResponsedata_sc("Transaction processing failure");
		apiresponse.setMsm("00000"); //5 zeros for a failed transaction
		apiresponse.setTransaction_type("TRANSACTIONAL");
		return apiresponse;
	}

	public ApiResponseModel failedUtilityPayment(final String failureReason) {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setSd("Utility payment failed");
		apiresponse.setResponsedata_sc(failureReason);
		apiresponse.setHttpStatus("400");
		apiresponse.setTransaction_type("FailedUtilityPayment");
		return apiresponse;
	}

	public ApiResponseModel successfulUtilityPayment(final ZposIntegratorResponse zposIntegratorResponse) {
		ApiResponseModel apiResponse = new ApiResponseModel();
		apiResponse.setUtilityPaymentResponse(zposIntegratorResponse);
		return apiResponse;
	}

	public ApiResponseModel failedPaymentAuthentication() {
		ApiResponseModel apiresponse = new ApiResponseModel();
		apiresponse.setSd("Authentication Failed");
		apiresponse.setHttpStatus(String.valueOf(HttpStatus.UNAUTHORIZED.value()));
		return apiresponse;
	}

}
