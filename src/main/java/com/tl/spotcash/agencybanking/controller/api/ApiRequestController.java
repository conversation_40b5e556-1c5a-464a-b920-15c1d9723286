package com.tl.spotcash.agencybanking.controller.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.ApiRequestProcessor;
import com.tl.spotcash.agencybanking.ApiResponseCodes;
import com.tl.spotcash.agencybanking.adapter.CorebankingSystemInvokerImpl;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.*;
import com.tl.spotcash.agencybanking.custommodels.agent.Agent;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.ResponseCodes;
import com.tl.spotcash.agencybanking.custommodels.event.*;
import com.tl.spotcash.agencybanking.custommodels.member.GetMemberRequest;
import com.tl.spotcash.agencybanking.custommodels.member.GetMemberResponse;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.enums.StoreUser;
import com.tl.spotcash.agencybanking.pojo.AuthResponse;
import com.tl.spotcash.agencybanking.pojo.EncryptedData;
import com.tl.spotcash.agencybanking.service.*;
import com.tl.spotcash.agencybanking.utils.AesEncryption;
import com.tl.spotcash.agencybanking.utils.Crypt;
import com.tl.spotcash.agencybanking.utils.JwtTokenUtil;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import com.tl.spotcash.agencybanking.xiputils.SpotcashTrxDetails;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.sql.Date;
import java.util.*;

@RestController
@RequestMapping("spotcash/mobile")
public class ApiRequestController {
    private final CrudTransactionController crudTransactions;
    private final TransactionService transactionService;
    private final SpotcashCbsService cbsService;
    public final OtpService otpService;
    public final JwtTokenUtil jwtUtil;
    public final Environment environment;
    private final WorkingHoursService workingHoursService;
    private final ErrorMessageService errorMessageService;
    private String validationResponseMessage;
    private Boolean cbsAuth = false;
    private HttpServletRequest request;
    int ChainedStatus = 0;
    SharedFunctions sharedFunctions1 = new SharedFunctions();
    ReserveFundsConfiguration reserveFundsConfigurations;


    private static final Logger LOGGER = LoggerFactory.getLogger(ApiRequestController.class);
    SharedFunctions sharedFunctions = new SharedFunctions();

    public ApiRequestController(CrudTransactionController crudTransactions, TransactionService transactionService,
                                SpotcashCbsService cbsService, OtpService otpService, JwtTokenUtil jwtUtil,
                                Environment environment, WorkingHoursService workingHoursService,
                                ErrorMessageService errorMessageService,ReserveFundsConfiguration reserveFundsConfigurations) {
        this.crudTransactions = crudTransactions;
        this.transactionService = transactionService;
        this.cbsService = cbsService;
        this.otpService = otpService;
        this.jwtUtil = jwtUtil;
        this.environment = environment;
        this.workingHoursService = workingHoursService;
        this.errorMessageService = errorMessageService;
        this.reserveFundsConfigurations = reserveFundsConfigurations;
    }

    /**
     * This endpoint handles all transactions performed by an Agent excluding utility transactions.
     * Utility transactions are handled by the '/utilities' endpoint.
     *
     * @param payload
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/service", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> processTransaction(@RequestBody String payload, HttpServletRequest request,
                                                HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);
        String token = resp.getHeader("WatchDog").split(" ")[1];

        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        EncryptedData encryptedData = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedData.getTransTemp();

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response is encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // checks if the request is within working hours
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Transaction Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "transactionOutsideWorkingHours",
                    "Transaction is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        String requestParams = Crypt.decrypt(encryptedPayload.replace("\n", ""), secretKey);
        LOGGER.info("REQUEST FROM DEVICE " + requestParams);
        HttpProcessorRequest requestBody = new SpotcashUtilities().returnMappedXipRequest(requestParams);
        String storeId = requestBody.getRequestData().getAgentSid();

        String clientID = requestBody.getHeader().getClientId();
        String serviceCode = requestBody.getRequestData().getTxnType();
        String amount = requestBody.getRequestData().getAmount();
        boolean clientSubscribed = transactionService.checkSubscription(serviceCode, clientID);

        if (crudTransactions.fetchAgentWithId(agentId).getFence().equals(BigInteger.ONE)) {
            Optional<SpAgentStores> optionalAgentStore = Optional.ofNullable(
                    crudTransactions.fetchAgentWithStoreId(BigInteger.valueOf(Long.parseLong(agentStoreId))));
            if (optionalAgentStore.isPresent()) {
                SpAgentStores spAgentStore = optionalAgentStore.get();
                String centralLocationJson = spAgentStore.getCentralLocation();
                JSONObject centralLocation = new JSONObject(centralLocationJson);
                double latitude = Double.parseDouble(centralLocation.getString("latitude"));
                double longitude = Double.parseDouble(centralLocation.getString("longitude"));

                String receivedLatitude = requestBody.getRequestData().getLatitude();
                String receivedLongitude = requestBody.getRequestData().getLongitude();

                if (!transactionService.withinLocation(Double.parseDouble(receivedLatitude),
                        Double.parseDouble(receivedLongitude), latitude, longitude, spAgentStore.getStoreRadius())) {
                    // Transaction is outside working Area.
                    LOGGER.error("Transaction Request is outside working area!");
                    String errorNarration = errorMessageService.getErrorMessage(
                            clientId, "outsideWorkingArea", "Transaction is outside working area");
                    AuthResponse response = new AuthResponse("01", errorNarration);

                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
                    return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
                }
            }
        }

        if (clientSubscribed) {
            JSONObject errorMessageObjects= new JSONObject();
            LOGGER.info(" -------- AGENCY START TRANSACTION ------- SERVICE CODE ----- " + serviceCode);
            if(crudTransactions.isAgentServiceSubscriptionEnabled(agentId, clientID)){
                //CHeck store user service subscription
                boolean isStoreUserSubscribed = crudTransactions.isStoreUserSubscribedToService(
                        serviceCode,
                        Long.parseLong(clientID),
                        Long.parseLong(storeUserId)
                );
                if(!isStoreUserSubscribed){
                    LOGGER.error("STORE USER WITH ID " + storeUserId + " IS NOT SUBSCRIBED TO SERVICE WITH CODE " + serviceCode);
                    PosResponseMapper isoresponseData = new PosResponseMapper();
                    isoresponseData.setResponseCode("01");
                    isoresponseData.setResponseData(Collections.singletonMap("message", "You are not subscribed to this service"));
                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                    return ResponseEntity.status(HttpStatus.FORBIDDEN).headers(responseHeaders).body(encryptedRes);
                }
            }

            try {
                boolean chargeableService = crudTransactions.isServiceChargeable(serviceCode);
                JSONObject errorMessageObject = new JSONObject();

                SpServiceSubscriptions spServiceSubscriptions;
                BigDecimal transactionAmt = new BigDecimal(amount);
                BigDecimal minAmount = BigDecimal.ZERO;
                BigDecimal maxAmount = BigDecimal.ZERO;
                BigDecimal maxDaily = BigDecimal.ZERO;
                BigDecimal totalTrxAmount = BigDecimal.ZERO;

                //Fetch subscription data only when it is a chargeable service
                if (chargeableService) {
                    spServiceSubscriptions = crudTransactions.fetchServicesubscriptionData(
                            serviceCode, new BigInteger(clientID));

                    try {
                        errorMessageObject = new JSONObject(spServiceSubscriptions.getAgencyMessages());
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    minAmount = spServiceSubscriptions.getMinAmount() == null
                            ? BigDecimal.ZERO : spServiceSubscriptions.getMinAmount();
                    maxAmount = spServiceSubscriptions.getMaxAmount() == null
                            ? BigDecimal.ZERO : spServiceSubscriptions.getMaxAmount();
                    maxDaily = spServiceSubscriptions.getMaxDaily() == null
                            ? BigDecimal.ZERO : BigDecimal.valueOf(spServiceSubscriptions.getMaxDaily().longValue());
                    totalTrxAmount = crudTransactions.getMaximumDailyAmount(
                            clientID, requestBody.getHeader().getMsisdn(), serviceCode).add(transactionAmt);
                }

                if (!chargeableService || (transactionAmt.compareTo(maxAmount) < 1 // Less than or equal to max Amount
                        && totalTrxAmount.compareTo(maxDaily) < 1 // Less than or equal to max Daily
                        && transactionAmt.compareTo(minAmount) >= 0)) { // Greater than or equal to min amount

                    if (!transactionService.deviceJourneyIsValidForTransaction(request, requestParams)) {
                        PosResponseMapper isoresponseData = new PosResponseMapper();
                        isoresponseData.setResponseCode("01");
                        String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).headers(responseHeaders).body(encryptedRes);
                    }

                    List<String> blacklistExemptedServices = Arrays.asList("EXTD", "SDT", "ZRPT", "AGBAL");

                    boolean shouldCheckIfBlacklisted = blacklistExemptedServices
                            .stream()
                            .noneMatch(serviceCode::equalsIgnoreCase);

                    //checks if Agent is Blacklisted.
                    if (shouldCheckIfBlacklisted) {
                        if (transactionService.agentIsBlacklisted(requestParams)) {
                            LOGGER.warn("Agent blacklisted - requestParams :: {}", requestParams);
                            String errorNarration = errorMessageService.getErrorMessage(
                                    requestBody.getHeader().getClientId(), "agentBlacklisted",
                                    "Dear Agent, your account is locked, please contact sacco for activation");

                            PosResponseMapper isoresponseData = new PosResponseMapper();
                            isoresponseData.setResponseCode("02");
                            Map<String, String> header = new HashMap<String, String>() {{
                                put("sc", "02");
                                put("sd", errorNarration);
                            }};
                            isoresponseData.setHeader(header);
                            Map<String, Object> responseData = new HashMap<String, Object>() {{
                                put("msm", errorNarration);
                                put("message", errorNarration);
                            }};
                            isoresponseData.setResponseData(responseData);
                            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                        } else {
                            LOGGER.info("Agent is not blacklisted, proceeding to check if customer is blacklisted...");
                        }
                    } else {
                        LOGGER.info("Service Code :: {} exempted from agent blacklist check, proceeding to check if customer is blacklisted...", serviceCode);
                    }

                    //checks if customer is Blacklisted.
                    if (shouldCheckIfBlacklisted) {
                        if (transactionService.customerIsBlacklisted(requestParams)) {
                            LOGGER.warn("Customer blacklisted - requestParams :: {}", requestParams);
                            String errorNarration = errorMessageService.getErrorMessage(
                                    requestBody.getHeader().getClientId(), "customerBlacklisted",
                                    "Transaction Incomplete. Customer account blocked from transacting through agency banking. Contact sacco for activation.");
                            PosResponseMapper isoresponseData = new PosResponseMapper();
                            isoresponseData.setResponseCode("02");
                            Map<String, String> header = new HashMap<String, String>() {{
                                put("sc", "02");
                                put("sd", errorNarration);
                            }};
                            isoresponseData.setHeader(header);
                            Map<String, Object> responseData = new HashMap<String, Object>() {{
                                put("msm", errorNarration);
                                put("message", errorNarration);
                            }};
                            isoresponseData.setResponseData(responseData);
                            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                            //encryptedResponse.setTransTemp(encryptedRes);
                            //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                        } else {
                            LOGGER.info("Customer is not blacklisted, proceeding to process...");
                        }
                    } else {
                        LOGGER.info("Service Code :: {} exempted from customer blacklist check, proceeding to process ...", serviceCode);
                    }

                    //checking for a duplicate transaction before invoking the CBS.
                    List<String> doubleTransactionExemptedServices = Arrays.asList("ZRPT", "AGBAL");
                    boolean exemptedServicesStatus = doubleTransactionExemptedServices
                            .stream()
                            .noneMatch(serviceCode::equalsIgnoreCase);

                    if (exemptedServicesStatus) {
                        LOGGER.info("<><><>CHECKING FOR A DUPLICATE TRANSACTION BEFORE INVOKING THE CBS<><><>");
                        if (crudTransactions.checkForDuplicateTransactions(requestBody)) {
                            PosResponseMapper isoresponseData = new PosResponseMapper();
                            isoresponseData.setResponseCode("03");
                            String errorNarration = errorMessageService.getErrorMessage(
                                    requestBody.getHeader().getClientId(), "duplicateTransaction",
                                    "A similar transaction is currently being processed. Please wait as we complete the transaction");

                            Map<String, String> header = new HashMap<String, String>() {{
                                put("sc", "03");
                                put("sd", errorNarration);
                            }};
                            isoresponseData.setHeader(header);
                            LOGGER.info("----------------- AGENCY END TRANSACTION -----------------");
                            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                            //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                        }
                    }


                    PosResponseMapper isoresponseData = new ApiRequestProcessor(crudTransactions, requestParams, environment, request,reserveFundsConfigurations).call();
                    LOGGER.info("----------------- RESPONSE \n" + isoresponseData.getResponseData());
                    LOGGER.info("Archiving request logs...");
                    transactionService.archiveRequestLogs(SharedFunctions.getRemoteIpAddress(request, environment));

                    LOGGER.info("----------------- AGENCY END TRANSACTION -----------------");
                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                    //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                }
                else {
                    LOGGER.info("Error: Transaction out of the limits set in service subscription");
                    PosResponseMapper isoresponseData = new PosResponseMapper();
                    Map<String, String> header = new HashMap<>();
                    header.put("sc", "01");
                    try {
                        header.put("sd", errorMessageObject.getString("limits"));
                    } catch (Exception xx) {
                        header.put("sd", "Transaction amount out of the set limits");
                    }
                    isoresponseData.setHeader(header);

                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                    //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                }
            }
            catch (Exception e) {
                LOGGER.error("TRANSACTION EXCEPTION CAUGHT " + e.getMessage());
                e.printStackTrace();
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders).build();
                //return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);00
            }
        }
        else {
            LOGGER.error("CLIENT WITH ID " + clientID + " IS NOT SUBSCRIBED TO SERVICE WITH CODE " + serviceCode);
            PosResponseMapper isoresponseData = new PosResponseMapper();
            isoresponseData.setResponseCode("01");
            isoresponseData.setResponseData(Collections.singletonMap("message", "You are not subscribed to this service"));
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).headers(responseHeaders).body(encryptedRes);
            //return new ResponseEntity<>(encryptedRes, HttpStatus.FORBIDDEN);
        }
    }


    /**
     * This endpoint handles all utility transactions including buyAirtime.
     *
     * @param payload
     * @param request
     * @return String
     * @throws Exception
     */
    @RequestMapping(value = "/utilities", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> processUtilityTransaction(@RequestBody String payload, HttpServletRequest request,
                                                       HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Transaction Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "transactionOutsideWorkingHours",
                    "Transaction is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        PosResponseMapper isoresponseData;

        EncryptedData encryptedData = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedData.getTransTemp();
        String requestParams = Crypt.decrypt(encryptedPayload.replace("\n", ""), secretKey);

        HttpProcessorRequest requestBody = new SpotcashUtilities().returnMappedXipRequest(requestParams);

        if (crudTransactions.fetchAgentWithId(agentId).getFence().equals(BigInteger.ONE)) {
            Optional<SpAgentStores> optionalAgentStore = Optional.ofNullable(
                    crudTransactions.fetchAgentWithStoreId(BigInteger.valueOf(Long.parseLong(agentStoreId))));
            if (optionalAgentStore.isPresent()) {
                SpAgentStores spAgentStore = optionalAgentStore.get();
                String centralLocationJson = spAgentStore.getCentralLocation();
                JSONObject centralLocation = new JSONObject(centralLocationJson);
                double latitude = Double.parseDouble(centralLocation.getString("latitude"));
                double longitude = Double.parseDouble(centralLocation.getString("longitude"));

                String receivedLatitude = requestBody.getRequestData().getLatitude();
                String receivedLongitude = requestBody.getRequestData().getLongitude();

                if (!transactionService.withinLocation(Double.parseDouble(receivedLatitude),
                        Double.parseDouble(receivedLongitude), latitude, longitude, spAgentStore.getStoreRadius())) {
                    // Transaction is outside working Area.
                    LOGGER.error("Transaction Request is outside working area!");
                    String errorNarration = errorMessageService.getErrorMessage(clientId,
                            "outsideWorkingArea", "Transaction is outside working area");
                    AuthResponse response = new AuthResponse("01", errorNarration);

                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
                    return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
                }
            }
        }

        //check subscriptions here
        //validate the device journeys
        ApiResponse apiResponse = new ApiResponse();
        LOGGER.info("RECEIVED DATA " + requestParams);
        int httpStatusCode = 200;
        String clientID = requestBody.getHeader().getClientId();
        String serviceCode = requestBody.getRequestData().getTxnType();
        String amount = requestBody.getRequestData().getAmount();
        if (serviceCode.equalsIgnoreCase("KP") || serviceCode.equalsIgnoreCase("KT")) serviceCode = "UTIL";
        boolean clientSubscribed = transactionService.checkSubscription(serviceCode, clientID);
        JSONObject errorMessageObject = new JSONObject();
        if (clientSubscribed) {
            LOGGER.info(" -------- AGENCY START TRANSACTION ------- SERVICE CODE -----" + serviceCode + " ");
            BigDecimal transactionAmt = new BigDecimal(amount);
            SpServiceSubscriptions spServiceSubscriptions = crudTransactions
                    .fetchServicesubscriptionData(serviceCode, new BigInteger(clientID));
            try {
                errorMessageObject = new JSONObject(spServiceSubscriptions.getAgencyMessages());
            } catch (JSONException e) {
                e.printStackTrace();
            }

            BigDecimal minAmount = spServiceSubscriptions.getMinAmount() == null
                    ? BigDecimal.ZERO : spServiceSubscriptions.getMinAmount();
            BigDecimal maxAmount = spServiceSubscriptions.getMaxAmount() == null
                    ? BigDecimal.ZERO : spServiceSubscriptions.getMaxAmount();
            BigDecimal maxDaily = spServiceSubscriptions.getMaxDaily() == null
                    ? BigDecimal.ZERO : BigDecimal.valueOf(spServiceSubscriptions.getMaxDaily().longValue());
            BigDecimal totalTrxAmount = crudTransactions.getMaximumDailyAmount(
                    clientID, requestBody.getHeader().getMsisdn(), serviceCode).add(transactionAmt);

            //check whether there is some float in the service subscription account
            //using the amount account id for the floats
            SpAccounts spAccount = crudTransactions.getAccountDetails(
                    String.valueOf(spServiceSubscriptions.getAmountAccId()));
            //validate the amount against min,max,daily in service subscriptions
            BigDecimal commission;
            BigDecimal saccoCommission;
            BigDecimal serviceCharge;
            //get the commission
            Map<String, BigDecimal> comissionChargeData = transactionService.getComissionServiceChargeData(
                    spServiceSubscriptions.getServiceId(), new BigInteger(clientID), transactionAmt);
            LOGGER.info("COMMISSION MAP RETURNED ## " + comissionChargeData);
            if (comissionChargeData != null) {
                //Check using service id type
                commission = comissionChargeData.get("commission");
                serviceCharge = comissionChargeData.get("serviceCharge");
                saccoCommission = comissionChargeData.get("saccoCommission");
            } else {
                //Non commission transaction
                commission = BigDecimal.ZERO;
                serviceCharge = BigDecimal.ZERO;
                saccoCommission = BigDecimal.ZERO;
            }
            BigDecimal totalCharge = (serviceCharge != null ? serviceCharge : new BigDecimal("0")).add(
                    (commission != null ? commission : new BigDecimal("0"))).add(
                    (saccoCommission != null ? saccoCommission : new BigDecimal("0")));
            BigDecimal totalAmount = transactionAmt.add(totalCharge);
            LOGGER.info("amount reserved ..." + totalAmount);
            LOGGER.info(new ObjectMapper().writeValueAsString(spAccount));
            BigDecimal availableBalance = spAccount.getAvailBal() != null ? spAccount.getAvailBal() : new BigDecimal("0.00");
            if ((totalAmount).compareTo(availableBalance) < 1) {
                if (transactionAmt.compareTo(maxAmount) < 1 &&
                        totalTrxAmount.compareTo(maxDaily) < 1
                        && transactionAmt.compareTo(minAmount) >= 0) {
                    transactionService.updateUnclearedBalance(totalAmount, String.valueOf(spAccount.getId()));
                    LOGGER.info(" ---------------- AGENCY START TRANSACTION ------------------- ");
                    try {
                        if (!transactionService.deviceJourneyIsValidForTransaction(request, requestParams)) {
                            String errorNarration = errorMessageService.getErrorMessage(
                                    requestBody.getHeader().getClientId(), "invalidDeviceJourney",
                                    "Your activity has been flagged. Please try again or contact support");

                            apiResponse.setMessage("ERROR");
                            apiResponse.setNarration(errorNarration);
                            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(apiResponse), secretKey);
                            return ResponseEntity.status(HttpStatus.FORBIDDEN).headers(responseHeaders).body(encryptedRes);
                            //return new ResponseEntity<>(encryptedRes, HttpStatus.FORBIDDEN);
                        }

                        List<String> blacklistExemptedServices = Arrays.asList("EXTD", "SDT", "ZRPT", "AGBAL");

                        boolean shouldCheckIfBlacklisted = blacklistExemptedServices
                                .stream()
                                .noneMatch(serviceCode::equalsIgnoreCase);

                        //checks if Agent is Blacklisted.
                        if (shouldCheckIfBlacklisted) {
                            if (transactionService.agentIsBlacklisted(requestParams)) {
                                LOGGER.warn("Agent blacklisted - requestParams :: {}", requestParams);
                                String errorNarration = errorMessageService.getErrorMessage(
                                        requestBody.getHeader().getClientId(), "agentBlacklisted",
                                        "Dear Agent, your account is locked, please contact sacco for activation");

                                isoresponseData = new PosResponseMapper();
                                isoresponseData.setResponseCode("02");
                                Map<String, String> header = new HashMap<String, String>() {{
                                    put("sc", "02");
                                    put("sd", errorNarration);
                                }};
                                isoresponseData.setHeader(header);
                                Map<String, Object> responseData = new HashMap<String, Object>() {{
                                    put("msm", errorNarration);
                                    put("message", errorNarration);
                                }};
                                isoresponseData.setResponseData(responseData);
                                String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                                //encryptedResponse.setTransTemp(encryptedRes);
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                                //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                            } else {
                                LOGGER.info("Agent is not blacklisted, proceeding to check if customer is blacklisted...");
                            }
                        } else {
                            LOGGER.info("Service Code :: {} exempted from agent blacklist check, proceeding to check if customer is blacklisted...", serviceCode);
                        }

                        //checks if customer is Blacklisted.
                        if (shouldCheckIfBlacklisted) {
                            if (transactionService.customerIsBlacklisted(requestParams)) {
                                LOGGER.warn("Customer blacklisted - requestParams :: {}", requestParams);
                                String errorNarration = errorMessageService.getErrorMessage(
                                        requestBody.getHeader().getClientId(), "customerBlacklisted",
                                        "Transaction Incomplete. Customer account blocked from transacting through agency banking. Contact sacco for activation.");

                                isoresponseData = new PosResponseMapper();
                                isoresponseData.setResponseCode("02");
                                Map<String, String> header = new HashMap<String, String>() {{
                                    put("sc", "02");
                                    put("sd", errorNarration);
                                }};
                                isoresponseData.setHeader(header);
                                Map<String, Object> responseData = new HashMap<String, Object>() {{
                                    put("msm", errorNarration);
                                    put("message", errorNarration);
                                }};
                                isoresponseData.setResponseData(responseData);
                                String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                                //encryptedResponse.setTransTemp(encryptedRes);
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                                //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                            } else {
                                LOGGER.info("Customer is not blacklisted, proceeding to process...");
                            }
                        } else {
                            LOGGER.info("Service Code :: {} exempted from customer blacklist check, proceeding to process ...", serviceCode);
                        }

                        //checking for a duplicate transaction before invoking the CBS.
                        List<String> doubleTransactionExemptedServices = Arrays.asList("ZRPT", "AGBAL");
                        boolean exemptedServicesStatus = doubleTransactionExemptedServices
                                .stream()
                                .noneMatch(serviceCode::equalsIgnoreCase);

                        if (exemptedServicesStatus) {
                            LOGGER.info("<><><>CHECKING FOR A DUPLICATE TRANSACTION BEFORE INVOKING THE CBS<><><>");
                            if (crudTransactions.checkForDuplicateTransactions(requestBody)) {
                                String errorNarration = errorMessageService.getErrorMessage(
                                        requestBody.getHeader().getClientId(), "duplicateTransaction",
                                        "A similar transaction is currently being processed. Please wait as we complete the transaction");

                                isoresponseData = new PosResponseMapper();
                                isoresponseData.setResponseCode("03");
                                Map<String, String> header = new HashMap<String, String>() {{
                                    put("sc", "03");
                                    put("sd", errorNarration);
                                }};
                                isoresponseData.setHeader(header);
                                LOGGER.info("----------------- AGENCY END TRANSACTION -----------------");
                                String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                                //encryptedResponse.setTransTemp(encryptedRes);
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                                //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                            }
                        }

                        isoresponseData = new ApiRequestProcessor(crudTransactions, requestParams, environment, request,reserveFundsConfigurations).call();
                        LOGGER.info("----------------- RESPONSE \n" + isoresponseData.getResponseData());
                        if (isoresponseData.getResponseCode() != null) {
                            httpStatusCode = Integer.parseInt(isoresponseData.getResponseCode());
                        }
                        if (HttpStatus.valueOf(httpStatusCode).is2xxSuccessful()) {
                            LOGGER.info("Archiving request logs...");
                            transactionService.archiveRequestLogs(SharedFunctions.getRemoteIpAddress(request, environment));
                        }
                        LOGGER.info("----------------- AGENCY END TRANSACTION -----------------");
                    } catch (Exception e) {
                        LOGGER.error("TRANSACTION EXCEPTION CAUGHT " + e.getMessage());
                        e.printStackTrace();
                        apiResponse.setMessage("ERROR");
                        apiResponse.setNarration(e.getMessage());
                        String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(apiResponse), secretKey);
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                        //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                    }

                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                    return ResponseEntity.status(HttpStatus.valueOf(httpStatusCode)).headers(responseHeaders).body(encryptedRes);
                    //return new ResponseEntity<>(encryptedRes, HttpStatus.valueOf(httpStatusCode));
                } else {
                    LOGGER.info("error : transaction out of the limits set in service subscription");
                    isoresponseData = new PosResponseMapper();
                    Map<String, String> header = new HashMap<>();
                    header.put("sc", "01");
                    try {
                        header.put("sd", errorMessageObject.getString("limits"));
                    } catch (Exception xx) {
                        header.put("sd", "Transaction amount out of the set limits");
                    }
                    isoresponseData.setHeader(header);
                    String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                    //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
                }
            } else {
                isoresponseData = new PosResponseMapper();
                Map<String, String> header = new HashMap<>();
                header.put("sc", "01");
                try {
                    header.put("sd", errorMessageObject.getString("balance"));
                } catch (Exception xx) {
                    header.put("sd", "Float account does not have sufficient funds");
                }
                isoresponseData.setHeader(header);
                String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedRes);
                //return new ResponseEntity<>(encryptedRes, HttpStatus.OK);
            }
        } else {
            LOGGER.error("CLIENT WITH ID " + clientID + " IS NOT SUBSCRIBED TO SERVICE WITH CODE " + serviceCode);
            isoresponseData = new PosResponseMapper();
            isoresponseData.setResponseCode("01");
            isoresponseData.setResponseData(Collections.singletonMap("message", "You are not subscribed to this service"));
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(isoresponseData), secretKey);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).headers(responseHeaders).body(encryptedRes);
            //return new ResponseEntity<>(encryptedRes, HttpStatus.FORBIDDEN);
        }
    }
    @PostMapping("/checksubscription")
    public ResponseEntity<?> getserrvicesubscription(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, IS BLACKLISTED!", storeUserId);
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                secretKey);
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        Map<String, Object> response = new HashMap<>();
        response.put("message", "You are not subscribed to this service.");
        response.put("code", "01");

        try {
            //deal with the status
            SpServiceSubscriptions spServiceSubscriptions = crudTransactions.fetchServicesubscriptionData1(requestMap.get("serviceID"),
                    new BigInteger(requestMap.get("clientID")));
            System.out.println(new ObjectMapper().writeValueAsString(spServiceSubscriptions));
            JSONObject errorMessageObject = new JSONObject();
            if (spServiceSubscriptions != null) {
                //check the subscription status
                try {
                    errorMessageObject = new JSONObject(spServiceSubscriptions.getAgencyMessages());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                if (spServiceSubscriptions.getStatus().equalsIgnoreCase("1")) {
                    if(crudTransactions.isAgentServiceSubscriptionEnabled(agentId, requestMap.get("serviceID"))){
                        //CHeck store user service subscription
                        boolean isStoreUserSubscribed = crudTransactions.isStoreUserSubscribedToService(
                                requestMap.get("serviceID"),
                                Long.parseLong(requestMap.get("clientID")),
                                Long.parseLong(storeUserId)
                        );

                        if(isStoreUserSubscribed){
                            response.put("message", "subscription found");
                            response.put("code", "00");
                            response.put("entity", spServiceSubscriptions);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        }
                        else {
                            try {
                                response.put("message", errorMessageObject.getString("subscription"));
                            } catch (Exception ex) {
                                response.put("message", "You are not subscribed to this service.");
                            }
                            //response.put("code","01");
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                            //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                        }
                    }
                    else{
                        response.put("message", "subscription found");
                        response.put("code", "00");
                        response.put("entity", spServiceSubscriptions);
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                    }
                }
                else {
                    try {
                        response.put("message", errorMessageObject.getString("subscription"));
                    } catch (Exception ex) {
                        response.put("message", "You are not subscribed to this service.");
                    }
                    //response.put("code","01");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                    //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                }
            }
            else {
                //response.put("message","You are not subscribed to this service..");
                //response.put("code","01");
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
            }
        } catch (Exception e) {
            response.put("message", "You are not subscribed to this service...");
            response.put("code", "01");
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
            //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
        }
    }



    /**
     * Get customer data for local authentication endpoint
     */
    @RequestMapping(value = "/customer", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getSpCustomerData(@RequestBody String payload, HttpServletRequest request,
                                               HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        ApiResponse error = new ApiResponse();
        error.setMessage("Not found");
        error.setNarration("Customer Details Not Found");

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> customer = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        LOGGER.info("customer ...................................................");
        LOGGER.info("CUSTOMER :: {}", customer.toString());

        SpCustomers spCustomer = null;
        try {
            //List<String> fetchMemberImages = switchConfig.fetchImage();
            int clientId = 0;
            int cbsAuth = 0;
            if (customer.containsKey("client_id")) {
                clientId = Integer.parseInt(customer.get("client_id"));
            }
            if (customer.containsKey("cbs_auth")) {
                cbsAuth = Integer.parseInt(customer.get("cbs_auth"));
            }
            LOGGER.info("cbs_auth ::: " + cbsAuth);
            if (cbsAuth == 1) {//CBS Auth - Yes
                //Optional<FetchImageResponseMapper> optionalCbsMemberDetails = Optional.ofNullable(cbsService.fetchMemberImage(customer.get("value"), Integer.toString(clientId)));
                Optional<FetchImageResponseMapper> optionalCbsMemberDetails = Optional.ofNullable(cbsService.fetchMemberImage(customer.get("value"), Integer.toString(clientId), customer.get("identifier")));
//                LOGGER.info(" ---------------- Ccbs_auth :::BS VERIFICATION Response:::: ---------------- " + optionalCbsMemberDetails.get());
//                LOGGER.info(" ---------------- CBS VERIFICATION Response:::: ---------------- " + new ObjectMapper().writeValueAsString(optionalCbsMemberDetails.get()));
                if (optionalCbsMemberDetails.isPresent()) {
                    spCustomer = new SpCustomers();
                    FetchImageResponseMapper cbsMemberResponseMapper = optionalCbsMemberDetails.get();
                    if (cbsMemberResponseMapper.getResponseCode() == null) {
                        LOGGER.info(" ---------------- Error on Authenticate Customer ---------------- ");
                        String errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "invalidCbsResponse",
                                "Invalid Response from CBS");

                        error.setMessage("Error Occurred while trying to authenticate Customer");
                        error.setNarration(errorNarration);
                        return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(
                                new ObjectMapper().writeValueAsString(error),
                                secretKey), HttpStatus.EXPECTATION_FAILED);*/
                    }
                    if (cbsMemberResponseMapper.getResponseCode().matches(ResponseCodes.SUCCESS.getResponseCode()) || cbsMemberResponseMapper.getResponseCode().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
                        Member cbsMemberDetails = cbsMemberResponseMapper.getMemberDetails().getMember();
                        if ((!cbsMemberDetails.getMemberNo().equals("")) && (!cbsMemberDetails.getNationalId().equals(""))
                                && (!cbsMemberDetails.getPhoneNo().equals(""))) {
                            if (!cbsMemberDetails.getMemberImage().equals("")) {
                                spCustomer.setPotrait(cbsMemberDetails.getMemberImage());
                            }
                            if (!cbsMemberDetails.getName().equals("")) {
                                spCustomer.setCustomerName(cbsMemberDetails.getName());
                            }
                            spCustomer.setMobileWebservice(cbsMemberDetails.isMobileWebservice());
                            spCustomer.setSpCustId(cbsMemberDetails.getMemberNo());
                            spCustomer.setNationalId(cbsMemberDetails.getNationalId());
                            spCustomer.setMsisdn(cbsMemberDetails.getPhoneNo());
                            spCustomer.setClientId(new BigInteger(customer.get("client_id")));
                            spCustomer.setSubscriptionStatus(new BigInteger("1"));//todo  subject to confirmation from the cbs.. or verify whether its only active accounts that are returned
                            LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
//                            LOGGER.info("the customer now "+spCustomer);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                            /*return new ResponseEntity<>(Crypt.encrypt(
                                    new ObjectMapper().writeValueAsString(spCustomer),
                                    secretKey), HttpStatus.OK);*/
                        } else {
                            LOGGER.error(" ---------------- SPOTCASH CLIENT VERIFICATION FAILED ----------------3 ");
                            String errorNarration = errorMessageService.getErrorMessage(
                                    String.valueOf(clientId), "missingRegistrationDetails",
                                    "Required Registration Details Are Missing");
                            error.setMessage("Details Missing");
                            error.setNarration(errorNarration);
                            return ResponseEntity.status(HttpStatus.NOT_FOUND).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                            /*return new ResponseEntity<>(Crypt.encrypt(
                                    new ObjectMapper().writeValueAsString(error),
                                    secretKey), HttpStatus.NOT_FOUND);*/
                        }
                    } else {
                        LOGGER.error(" CBS CLIENT VERIFICATION Error:  " + cbsMemberResponseMapper.getErrorString());
                        String errorNarration = errorMessageService.getErrorMessage(
                                String.valueOf(clientId), "invalidCbsResponse",
                                "Invalid Response from CBS");
                        error.setMessage(cbsMemberResponseMapper.getErrorString());
                        error.setNarration(errorNarration);
                        return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(
                                new ObjectMapper().writeValueAsString(error),
                                secretKey), HttpStatus.EXPECTATION_FAILED);*/
                    }
                } else {
                    LOGGER.error(" ---------------- CBS CLIENT VERIFICATION FAILED ---------------- ");
                    String errorNarration = errorMessageService.getErrorMessage(
                            String.valueOf(clientId), "invalidCbsResponse",
                            "Invalid Response from CBS");
                    error.setMessage("No Response");
                    error.setNarration(errorNarration);
                    return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(
                            new ObjectMapper().writeValueAsString(error),
                            secretKey), HttpStatus.EXPECTATION_FAILED);*/
                }
            } else {
                //cbs auth =0
                Optional<SpCustomers> optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerData(customer.get("identifier"), customer.get("value")));
                if (optionalSpCustomer.isPresent()) {
                    LOGGER.info("getting the data from the primary connection......");
                    spCustomer = optionalSpCustomer.get();
                    String fetchMemberImage = "0";
                    Optional<SpAgents> optionAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(customer.get("client_id"))));
                    if (optionAgent.isPresent()) {
                        SpAgents spAgent = optionAgent.get();
                        if (spAgent.getMemberImage() != null) {
                            fetchMemberImage = spAgent.getMemberImage().toString();
                        }
                    }
                    if (Integer.parseInt(fetchMemberImage) == 1) {//Fetch Member Image - Yes
                        LOGGER.info("FETCH CUSTOMER IMAGE ==>" + spCustomer.getClientId().toString());
                        Optional<FetchImageResponseMapper> optionalMemberDetails = Optional.ofNullable(cbsService.fetchMemberImage(spCustomer.getNationalId(), spCustomer.getClientId().toString()));
                        if (optionalMemberDetails.isPresent()) {
                            FetchImageResponseMapper memberResponseMapper = optionalMemberDetails.get();
                            Member memberDetails = memberResponseMapper.getMemberDetails().getMember();
                            if (!memberDetails.getMemberImage().equals("")) {
                                spCustomer.setPotrait(memberDetails.getMemberImage());
                            }
                        } else {
                            if (spCustomer.getSubscriptionStatus() == BigInteger.ONE) {//if customer is active
                                LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                                /*return new ResponseEntity<>(Crypt.encrypt(
                                        new ObjectMapper().writeValueAsString(spCustomer),
                                        secretKey), HttpStatus.OK);*/
                            } else if (spCustomer.getSubscriptionStatus() == BigInteger.ZERO) {//if customer is active
                                LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                                /*return new ResponseEntity<>(Crypt.encrypt(
                                        new ObjectMapper().writeValueAsString(spCustomer),
                                        secretKey), HttpStatus.OK);*/
                            } else {
                                LOGGER.error(" ---------------- SPOTCASH CLIENT VERIFICATION FAILED ---------------- 4");
                                String errorNarration = errorMessageService.getErrorMessage(
                                        String.valueOf(clientId), "customerInactive",
                                        "Customer is Inactive in Spotcash");
                                error.setMessage("Inactive");
                                error.setNarration(errorNarration);
                                return ResponseEntity.status(HttpStatus.NOT_FOUND).headers(responseHeaders)
                                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                                /*return new ResponseEntity<>(Crypt.encrypt(
                                        new ObjectMapper().writeValueAsString(error),
                                        secretKey), HttpStatus.NOT_FOUND);*/
                            }
                        }
                    } else {
                        LOGGER.info("NO NEED TO FETCH CUSTOMER IMAGE ");
                    }
                } else {
                    //get the data from the secondary database
                    LOGGER.info("getting the data from the secondary connection......");
                    optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerDataNewCon(customer.get("identifier"), customer.get("value")));
                    if (optionalSpCustomer.isPresent()) {
                        spCustomer = optionalSpCustomer.get();
                        LOGGER.info("customer details new conn ...... " + spCustomer.getCustomerName() + " " + spCustomer.getAccountNumber() + " " + spCustomer.getNationalId());

                        String fetchMemberImage = "0";
                        Optional<SpAgents> optionAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(customer.get("client_id"))));
                        if (optionAgent.isPresent()) {
                            SpAgents spAgent = optionAgent.get();
                            if (spAgent.getMemberImage() != null) {
                                fetchMemberImage = spAgent.getMemberImage().toString();
                            }
                        }
                        if (Integer.parseInt(fetchMemberImage) == 1) {//Fetch Member Image - Yes
                            LOGGER.info("FETCH CUSTOMER IMAGE ==>" + spCustomer.getClientId().toString());
                            Optional<FetchImageResponseMapper> optionalMemberDetails = Optional.ofNullable(cbsService.fetchMemberImage(spCustomer.getNationalId(), spCustomer.getClientId().toString()));
                            if (optionalMemberDetails.isPresent()) {
                                FetchImageResponseMapper memberResponseMapper = optionalMemberDetails.get();
                                Member memberDetails = memberResponseMapper.getMemberDetails().getMember();
                                if (!memberDetails.getMemberImage().equals("")) {
                                    spCustomer.setPotrait(memberDetails.getMemberImage());
                                }
                            } else {
                                if (spCustomer.getSubscriptionStatus() == BigInteger.ONE) {//if customer is active
                                    LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                                    /*return new ResponseEntity<>(Crypt.encrypt(
                                            new ObjectMapper().writeValueAsString(spCustomer),
                                            secretKey), HttpStatus.OK);*/
                                } else if (spCustomer.getSubscriptionStatus() == BigInteger.ZERO) {//if customer is active
                                    LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                                    /*return new ResponseEntity<>(Crypt.encrypt(
                                            new ObjectMapper().writeValueAsString(spCustomer),
                                            secretKey), HttpStatus.OK);*/
                                } else {
                                    LOGGER.error(" ---------------- SPOTCASH CLIENT VERIFICATION FAILED ----------------1 ");
                                    String errorNarration = errorMessageService.getErrorMessage(
                                            String.valueOf(clientId), "customerInactive",
                                            "Customer is Inactive in Spotcash");
                                    error.setMessage("Inactive");
                                    error.setNarration(errorNarration);
                                    return ResponseEntity.status(HttpStatus.NOT_FOUND).headers(responseHeaders)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                                    /*return new ResponseEntity<>(Crypt.encrypt(
                                            new ObjectMapper().writeValueAsString(error),
                                            secretKey), HttpStatus.NOT_FOUND);*/
                                }
                            }
                        } else {
                            LOGGER.info("NO NEED TO FETCH CUSTOMER IMAGE ");
                        }
                    } else {
                        error.setMessage("Missing");
                        error.setNarration("Customer with the Id Number was not found in Spotcash");
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(
                                new ObjectMapper().writeValueAsString(error),
                                secretKey), HttpStatus.BAD_REQUEST);*/
                    }
                }
                LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION ---------------- ");
                if (spCustomer.getSubscriptionStatus() == BigInteger.ONE) {//if customer is active
                    LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(
                            new ObjectMapper().writeValueAsString(spCustomer),
                            secretKey), HttpStatus.OK);*/
                } else if (spCustomer.getSubscriptionStatus() == BigInteger.ZERO) {//if customer is active
                    LOGGER.info(" ---------------- SPOTCASH CLIENT VERIFICATION SUCCESSFUL ---------------- ");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(spCustomer), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(
                            new ObjectMapper().writeValueAsString(spCustomer),
                            secretKey), HttpStatus.OK);*/
                } else {

                }
            }
            LOGGER.error(" ---------------- SPOTCASH CLIENT VERIFICATION FAILED ----------------2 ");
        } catch (Exception e) {
            LOGGER.error("FETCHING CUSTOMER EXCEPTION CAUGHT " + e.getMessage());
            e.printStackTrace();
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        /*return new ResponseEntity<>(Crypt.encrypt(
                new ObjectMapper().writeValueAsString(error),
                secretKey), HttpStatus.NOT_FOUND);*/
    }


    /**
     * This is endpoint is used for initiating OTP Authentication before an event registration is processed.
     *
     * @param payload
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/initiateEventsOtp", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> initiateEventsOtp(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        // Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        // Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            // Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            // Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Event registration is outside working hours.
            LOGGER.error("Event Registration Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "eventregistrationRequestOutsideWorkingHours",
                    "Event Registration request is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> otpRequest = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String msisdn = otpRequest.get("msisdn");
        String id = otpRequest.get("id");

        String smsUrl = environment.getRequiredProperty("datasource.spotcash.smsSendingUrl");
        String otpMessage = "OTP NOT SENT";

        Map<String, String> response = new HashMap<>();
        String otpFailed = errorMessageService.getErrorMessage(clientId,
                "otpAuthFailed", "Sorry, OTP Authentication failed. Please Try again later");
        response.put("message", otpMessage);
        response.put("narration", otpFailed);

        try {
            Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(clientId)));
            Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(agentStoreId));
            if (optionalAgent.isPresent()) {
                SpAgents spAgent = optionalAgent.get();
                if (spAgent.getSmsUrl() != null) {
                    if (!spAgent.getSmsUrl().equals("")) {
                        smsUrl = spAgent.getSmsUrl();
                    }
                }
            }

            if (optionalStoreUser.isPresent()) {
                SpStoreUsers storeUser = optionalStoreUser.get();
                int otp = otpService.generateOTP(id, clientId);

                if (SharedFunctions.validPhoneNumber(msisdn)) {
                    String otpMsg = "Your Event Registration OTP is " + otp;

                    SpMsgTemplates messageTemplate = crudTransactions.fetchMessageTemplate(
                            BigInteger.valueOf(Long.parseLong(clientId)), "MENTOR_EVENTS_REG_OTP");
                    if (null != messageTemplate) {
                        HashMap<String, String> templatePayload = new HashMap<>();
                        templatePayload.put("otp", String.valueOf(otp));
                        templatePayload.put("msisdn", msisdn);

                        String formattedMsg = sharedFunctions.messageTemplateFormatter(messageTemplate, templatePayload);
                        if (!ObjectUtils.isEmpty(formattedMsg)) otpMsg = formattedMsg;
                    }

                    String msg = smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode(otpMsg, "UTF-8");

                    LOGGER.debug(msg);
                    if (SharedFunctions.makeGetRequest(msg).contains("Success")) {
                        LOGGER.info("SMS Successfully");
                    } else {
                        LOGGER.info("SMS Sending Failed");
                    }
                    otpMessage = "OTP SENT";
                    response.put("message", otpMessage);
                    response.put("narration", otpMessage);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                } else {
                    LOGGER.info("Invalid Phone: " + msisdn + " for Incoming OTP Message.");

                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                }
            } else {
                LOGGER.info("SpStoreUser with agentStoreID " + agentStoreId + " not Found.");
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("GENERATE OTP EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }

        LOGGER.info(otpMessage);
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
    }


    /**
     * This endpoint handles OTP verification for registration of events.
     *
     * @param payload
     * @param servletRequest
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/eventsconfirmOtp", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> eventsconfirmOtp(@RequestBody String payload, HttpServletRequest servletRequest,
                                              HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            LOGGER.error("Event Registration Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "eventRegistrationOutsideWorkingHours",
                    "Event Registration is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> otpRequest = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String otpString = otpRequest.get("otp");
        String idNumber = otpRequest.get("customerId");
        //String agentStoreId = otpRequest.get("agentStoreId");
        LOGGER.info("CONFIRM_OTP PAYLOAD: OTP :: {}, CUSTOMER_ID :: {}", otpString, idNumber);

        Map<String, String> response = new HashMap<>();

        try {
            int otp = Integer.parseInt(otpString);
            String clientIdString = otpService.getClientId(idNumber);
            Long clientId = StringUtils.hasText(clientIdString) ? Long.valueOf(clientIdString) : null;
            if (otp > 0) {
                int serverOtp = otpService.getOtp(idNumber);
                LOGGER.debug("*** ENTRIES IN OTP_CACHE AS MAP :: {} ***", otpService.getAllEntries());
                LOGGER.debug("*** OTP RETRIEVED FROM CACHE : {} ***", serverOtp);
                if (serverOtp > 0) {
                    if (otp == serverOtp) {
                        otpService.clearOTP(idNumber);
                        otpService.clearKeyClientMap(idNumber);
                        LOGGER.info("Resetting OTP retries for customer... nationalId :: {}, clientId :: {}", idNumber, clientId);
                        crudTransactions.resetOtpLog(idNumber);
                        response.put("message", "VALID");
                        response.put("narration", "VALID");
                    } else {

                        response.put("message", "INVALID");
                        response.put("narration", "INVALID");
                    }

                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));

                } else {
                    LOGGER.error("OTP SERVICE >>>>  OTP HAS EXPIRED: {} <<<<", serverOtp);
                    response.put("message", "OTP_EXPIRED");
                    // Note: The narration is the message that is displayed to the user, thus,
                    // you MUST include a user-friendly narration for "OTP_EXPIRED".
                    String clientID = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
                    String errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientID), "otpExpired",
                            "Sorry, OTP has expired. Please Initiate a new request");
                    response.put("narration", errorNarration);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                }
            } else {

                HashMap<String, String> otpResult = otpService.updateOtpEntries(idNumber, clientId, Long.valueOf(agentStoreId),
                        SharedFunctions.getRemoteIpAddress(servletRequest, environment));

                String errorNarration = "";

                //OTP Retries

                /*if(otpResult.get("otpRetriesLeft").equals("0")){
                    errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "otpCustomerBlacklisted",
                            "Event Registration Incomplete. Wrong Otp entry retries exceeded.");
                }
                else {
                    errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "invalidOtp",
                            otpResult.get("otpRetriesLeft") +" OTP Retries left");
                }*/

                // Parse the tags if there is any
                errorNarration = ErrorMessageService.parseOtpMessage(errorNarration, otpResult);

                response.put("message", "INVALID");
                //response.put("otpRetryCount", otpResult.get("otpRetryCount"));
                //response.put("customerOtpRetries", otpResult.get("customerOtpRetries"));
                //response.put("otpRetriesLeft", otpResult.get("otpRetriesLeft"));
                response.put("narration", errorNarration);
                LOGGER.info("OTP RESPONSE >>>> {} <<<<", response);

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));

            }
        } catch (Exception e) {
            LOGGER.error("CONFIRMING OTP EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
            response.put("message", "OTP SERVICE ERROR");
            // Note: The narration is the message that is displayed to the user, thus,
            // you MUST include a user-friendly narration for "OTP SERVICE ERROR".
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId),
                    "otpAuthFailed", "Sorry, OTP Authentication failed. Please Try again later");
            response.put("narration", errorNarration);
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));

        }
    }


    /**
     * This is endpoint is used for initiating OTP Authentication before a transaction is processed.
     *
     * @param payload
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/initiateOTP", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> initiateOtp(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Transaction Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "transactionOutsideWorkingHours",
                    "Transaction is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> otpRequest = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String msisdn = otpRequest.get("msisdn");
        String id = otpRequest.get("id");
        String cbs_auth = otpRequest.get("cbs_auth");
        String client_id = otpRequest.get("clientId");
        String message = otpRequest.getOrDefault("message", "");
        String msg = "";
        //String agentStoreId  = otpRequest.get("agentStoreId");

        LOGGER.info("initiateOtp/{id}/{msisdn}/{cbs_auth}/{client_id}/{store_user_id}");
        String smsUrl = environment.getRequiredProperty("datasource.spotcash.smsSendingUrl");
        String otpMessage = "OTP NOT SENT";

        Map<String, String> response = new HashMap<>();
        String otpFailed = errorMessageService.getErrorMessage(client_id,
                "otpAuthFailed", "Sorry, OTP Authentication failed. Please Try again later");
        response.put("message", otpMessage);
        response.put("narration", otpFailed);

        //Todo: Generate a new access token if it is about to expire

        try {
            Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(client_id)));
            Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(agentStoreId));
            if (optionalAgent.isPresent()) {
                SpAgents spAgent = optionalAgent.get();
                if (spAgent.getSmsUrl() != null) {
                    if (!spAgent.getSmsUrl().equals("")) {
                        smsUrl = spAgent.getSmsUrl();
                    }
                }
            }
            //checks if either the agent or customer is blacklisted
            Boolean agentBlacklisted = transactionService.agentIsBlacklisted(agentStoreId, client_id);
            Boolean customerBlacklisted = transactionService.customerIsBlacklisted(msisdn, client_id);

            if (agentBlacklisted || customerBlacklisted) {
                otpMessage = agentBlacklisted ? "Agent Blacklisted" : "Customer Blacklisted";
                LOGGER.warn(otpMessage.toUpperCase() + " : NO NEED FOR SENDING AN OTP.");
                String errorNarration = agentBlacklisted
                        ? errorMessageService.getErrorMessage(client_id,
                        "agentBlacklisted", "Dear Agent, your account is locked, please contact sacco for activation")
                        : errorMessageService.getErrorMessage(client_id,
                        "customerBlacklisted", "Transaction Incomplete. Customer account blocked from transacting through agency banking. Contact sacco for activation.");
                response.put("message", otpMessage);
                response.put("narration", errorNarration);
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                        secretKey), HttpStatus.OK);*/
            } else {
                // Generate the OTP
                if (Integer.parseInt(cbs_auth) == 1) {//CBS Auth - Yes
                    if (optionalStoreUser.isPresent()) {
                        SpStoreUsers storeUser = optionalStoreUser.get();
                        int otp = otpService.generateOTP(id, client_id);
                        crudTransactions.createOtpLog(msisdn, id, client_id, String.valueOf(storeUser.getStoreUserId()), String.valueOf(otp));
                        if (SharedFunctions.validPhoneNumber(msisdn)) {
                            if (message != null && !message.isEmpty()) {
                                LOGGER.info("overdraft message", message.toString());
                                msg = "" + smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode(message + "\nAgency Banking OTP: " + otp, "UTF-8");
                            } else {
                                msg = "" + smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode("Agency Banking OTP: " + otp, "UTF-8");

                            }
                            LOGGER.debug("Final SMS URL: {}", msg);  // This is safe now

                            if (SharedFunctions.makeGetRequest(msg).contains("Success")) {
                                LOGGER.info("SMS Successfully");
                            } else {
                                LOGGER.info("SMS Sending Failed");
                            }
                            otpMessage = "OTP SENT";
                            //return otpMessage;
                            response.put("message", otpMessage);
                            response.put("narration", otpMessage);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                            /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    secretKey), HttpStatus.OK);*/
                        } else {
                            LOGGER.info("Invalid Phone: " + msisdn + " for Incoming OTP Message.");
                            //return otpMessage;
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                            /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    secretKey), HttpStatus.OK);*/
                        }
                    } else {
                        LOGGER.info("SpStoreUser with agentStoreID " + agentStoreId + " not Found.");
                        //return otpMessage;
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                secretKey), HttpStatus.OK);*/
                    }
                } else {
                    Optional<SpCustomers> optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerData("nationalId", id));
                    if (optionalSpCustomer.isPresent()) {
                        SpCustomers spCustomer = optionalSpCustomer.get();
                        if (spCustomer.getSubscriptionStatus().toString().trim().equals("1")) {
                            if (sharedFunctions.validPhoneNumber(spCustomer.getMsisdn())) {
                                if (optionalStoreUser.isPresent()) {
                                    SpStoreUsers storeUser = optionalStoreUser.get();
                                    int otp = otpService.generateOTP(id, client_id);
                                    crudTransactions.createOtpLog(msisdn, id, client_id, String.valueOf(storeUser.getStoreUserId()), String.valueOf(otp));
                                    if (message != null && !message.isEmpty()) {
                                        msg = "" + smsUrl + "&dest=" + spCustomer.getMsisdn() + "&msg=" + URLEncoder.encode(message + "\n Agency Banking OTP: " + otp + message, "UTF-8");
                                    } else {
                                        msg = "" + smsUrl + "&dest=" + spCustomer.getMsisdn() + "&msg=" + URLEncoder.encode("Agency Banking OTP: " + otp, "UTF-8");
                                    }
                                    LOGGER.debug("Final SMS URL: {}", msg);  // This is safe now
                                    if (sharedFunctions.makeGetRequest(msg).contains("Success")) {
                                        LOGGER.info("SMS Successfully");
                                    } else {
                                        LOGGER.info("SMS Sending Failed");
                                    }
                                    otpMessage = "OTP SENT";
                                    //return otpMessage;
                                    response.put("message", otpMessage);
                                    response.put("narration", otpMessage);
                                    /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                            secretKey), HttpStatus.OK);*/
                                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                                } else {
                                    LOGGER.info("SpStoreUser with agentStoreID " + agentStoreId + " not Found.");
                                    //return otpMessage;
                                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                                    /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                            secretKey), HttpStatus.OK);*/
                                }
                            } else {
                                LOGGER.info("Invalid Phone: " + spCustomer.getMsisdn() + " for Incoming OTP Message.");
                                //return otpMessage;
                                response.put("message", otpMessage);
                                //response.put("narration", errorNarration);
                                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                                /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                        secretKey), HttpStatus.OK);*/
                            }
                        } else {
                            otpMessage = "Customer Locked";
                            String errorNarration = errorMessageService.getErrorMessage(client_id,
                                    "customerLocked", "Customer Locked");
                            //return otpMessage;
                            response.put("message", otpMessage);
                            response.put("narration", errorNarration);
                            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                            /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    secretKey), HttpStatus.OK);*/
                        }
                    } else {
                        otpMessage = "Customer with the id was not found";
                        //return otpMessage;
                        response.put("message", otpMessage);
                        response.put("narration", "Customer not Found");
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                secretKey), HttpStatus.OK);*/
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("GENERATE OTP EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        LOGGER.info(otpMessage);
        //return otpMessage;
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
        /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                secretKey), HttpStatus.OK);*/
    }

    /**
     * This endpoint handles OTP verification for transactions.
     *
     * @param payload
     * @param servletRequest
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/confirmOtp", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> confirmOtp(@RequestBody String payload, HttpServletRequest servletRequest,
                                        HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            LOGGER.error("Transaction Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "transactionOutsideWorkingHours",
                    "Transaction is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> otpRequest = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String otpString = otpRequest.get("otp");
        String idNumber = otpRequest.get("customerId");
        //String agentStoreId = otpRequest.get("agentStoreId");
        LOGGER.info("CONFIRM_OTP PAYLOAD: OTP :: {}, CUSTOMER_ID :: {}", otpString, idNumber);

        Map<String, String> response = new HashMap<>();

        //Todo: Generate a new access token if it is about to expire

        try {
            int otp = Integer.parseInt(otpString);
            String clientIdString = otpService.getClientId(idNumber);
            Long clientId = StringUtils.hasText(clientIdString) ? Long.valueOf(clientIdString) : null;
            if (otp > 0) {
                int serverOtp = otpService.getOtp(idNumber);
                LOGGER.debug("*** ENTRIES IN OTP_CACHE AS MAP :: {} ***", otpService.getAllEntries());
                LOGGER.debug("*** OTP RETRIEVED FROM CACHE : {} ***", serverOtp);
                if (serverOtp > 0) {
                    if (otp == serverOtp) {
                        otpService.clearOTP(idNumber);
                        otpService.clearKeyClientMap(idNumber);
                        LOGGER.info("Resetting OTP retries for customer... nationalId :: {}, clientId :: {}", idNumber, clientId);
                        crudTransactions.resetOtpLog(idNumber);
                        response.put("message", "VALID");
                        response.put("narration", "VALID");
                    } else {
                        HashMap<String, String> otpResult = otpService.updateOtpEntries(idNumber, clientId, Long.valueOf(agentStoreId),
                                SharedFunctions.getRemoteIpAddress(servletRequest, environment));

                        String errorNarration = "";
                        if (otpResult.containsKey("agentBlacklisted") && otpResult.containsKey("agentWrongOtpEntries")) {
                            //Agent has been blacklisted due to <<agentWrongOtpEntries>> wrong OTP entries
                            errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "otpAgentBlacklisted",
                                    "Transaction Incomplete. Wrong Otp entry retries exceeded. Agent Account locked. Contact sacco for assistance.");
                            //Set the response to indicate that the agent has been blacklisted
                            response.put("agentBlacklisted", otpResult.get("agentBlacklisted"));
                            response.put("agentWrongOtpEntries", otpResult.get("agentWrongOtpEntries"));
                        } else {
                            if (otpResult.get("otpRetriesLeft").equals("0")) {
                                errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "otpCustomerBlacklisted",
                                        "Transaction Incomplete. Wrong Otp entry retries exceeded. Customer Account locked. Contact sacco for assistance.");
                            } else {
                                errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "invalidOtp",
                                        otpResult.get("otpRetriesLeft") + " OTP Retries left");
                            }
                        }

                        // Parse the tags if there is any
                        errorNarration = ErrorMessageService.parseOtpMessage(errorNarration, otpResult);

                        response.put("message", "INVALID");
                        response.put("otpRetryCount", otpResult.get("otpRetryCount"));
                        response.put("customerOtpRetries", otpResult.get("customerOtpRetries"));
                        response.put("otpRetriesLeft", otpResult.get("otpRetriesLeft"));
                        response.put("narration", errorNarration);
                        LOGGER.info("OTP RESPONSE >>>> {} <<<<", response);
                    }

                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                            secretKey), HttpStatus.OK);*/
                } else {
//                    int otpRetriesLeft = updateOtpEntries(idNumber, clientId, Long.valueOf(agentStoreId),
//                            SharedFunctions.getRemoteIpAddress(servletRequest, environment));

                    LOGGER.error("OTP SERVICE >>>>  OTP HAS EXPIRED: {} <<<<", serverOtp);
                    response.put("message", "OTP_EXPIRED");
                    // Note: The narration is the message that is displayed to the user, thus,
                    // you MUST include a user-friendly narration for "OTP_EXPIRED".
                    String clientID = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
                    String errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientID), "otpExpired",
                            "Sorry, OTP has expired. Please Initiate a new request");
                    response.put("narration", errorNarration);
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                            secretKey), HttpStatus.OK);*/
                }
            } else {
                HashMap<String, String> otpResult = otpService.updateOtpEntries(idNumber, clientId, Long.valueOf(agentStoreId),
                        SharedFunctions.getRemoteIpAddress(servletRequest, environment));

                String errorNarration = "";
                if (otpResult.containsKey("agentBlacklisted") && otpResult.containsKey("agentWrongOtpEntries")) {
                    //Agent has been blacklisted due to <<agentWrongOtpEntries>> wrong OTP entries
                    errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "otpAgentBlacklisted",
                            "Transaction Incomplete. Wrong Otp entry retries exceeded. Agent Account locked. Contact sacco for assistance.");
                    //Set the response
                    response.put("agentBlacklisted", otpResult.get("agentBlacklisted"));
                    response.put("agentWrongOtpEntries", otpResult.get("agentWrongOtpEntries"));
                } else {
                    if (otpResult.get("otpRetriesLeft").equals("0")) {
                        errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "otpCustomerBlacklisted",
                                "Transaction Incomplete. Wrong Otp entry retries exceeded. Customer Account locked. Contact sacco for assistance.");
                    } else {
                        errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId), "invalidOtp",
                                otpResult.get("otpRetriesLeft") + " OTP Retries left");
                    }
                }

                // Parse the tags if there is any
                errorNarration = ErrorMessageService.parseOtpMessage(errorNarration, otpResult);

                response.put("message", "INVALID");
                response.put("otpRetryCount", otpResult.get("otpRetryCount"));
                response.put("customerOtpRetries", otpResult.get("customerOtpRetries"));
                response.put("otpRetriesLeft", otpResult.get("otpRetriesLeft"));
                response.put("narration", errorNarration);
                LOGGER.info("OTP RESPONSE >>>> {} <<<<", response);

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                        secretKey), HttpStatus.OK);*/
            }
        } catch (Exception e) {
            LOGGER.error("CONFIRMING OTP EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
            response.put("message", "OTP SERVICE ERROR");
            // Note: The narration is the message that is displayed to the user, thus,
            // you MUST include a user-friendly narration for "OTP SERVICE ERROR".
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(String.valueOf(clientId),
                    "otpAuthFailed", "Sorry, OTP Authentication failed. Please Try again later");
            response.put("narration", errorNarration);
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
            /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                    secretKey), HttpStatus.OK);*/
        }
    }

    /**
     * Lock customer's accounts as a security measure
     */
    @RequestMapping(value = "/lockaccount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> lockAccount(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        ApiResponse error = new ApiResponse();
        error.setMessage("Error");
        error.setNarration("Invalid Response from CBS During Lock Account");
        CbsResponseMapper cbsResponseMapper;
        String accountNumber = requestMap.get("accountNumber");
        String idNumber = requestMap.get("idNumber");

        try {
            Optional<CbsResponseMapper> optionalResponseDetails = Optional.ofNullable(cbsService.lockCustomerAccount(accountNumber, idNumber, requestMap.get("clientId")));
            if (optionalResponseDetails.isPresent()) {
                cbsResponseMapper = optionalResponseDetails.get();
            } else {
                LOGGER.info(" ---------------- Error on Lock Account ---------------- ");
                error.setMessage("Lock Account Failed");
                error.setNarration("Error Occurred while trying to Lock Account");
                return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error),
                        secretKey), HttpStatus.EXPECTATION_FAILED);*/
            }

            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(cbsResponseMapper), secretKey));
            //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(cbsResponseMapper), secretKey), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("LOCK ACCOUNT EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.EXPECTATION_FAILED);
    }

    /**
     * trying to authenticate the agent using their phone number and pin
     */
    @Deprecated
    @RequestMapping(value = "/pinauthenticationnew", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> authenticateWithPhoneAndPinNew(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());

        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        String msisdn = requestMap.get("msisdn");
        String pin = requestMap.get("pin");

        ApiResponse error = new ApiResponse();
        error.setMessage("Agent does not Exist!");
        error.setNarration("Invalid Agent Phone Number or Pin."); //Todo: correct msg
        error.setSuccessStatus("01");
        SpAgentStores spAgentStore;
        ConfigurationResponseMapper configurationResponseMapper = new ConfigurationResponseMapper();

        try {
            SpStoreUsers agentStoreUser = crudTransactions.fetchAgentStoreUserDataWithMsiSdnAndPin(msisdn.trim(), pin.trim());
            if (agentStoreUser == null) {
                LOGGER.error(" ---------------- SPOTCASH AGENT USER VERIFICATION FAILED ---------------- ");
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
            } else {
                configurationResponseMapper.setAgentStoreUsers(agentStoreUser);
                Optional<SpAgentStores> optionalAgentStore = Optional.ofNullable(
                        crudTransactions.fetchAgentWithStoreId(agentStoreUser.getAgentStoreId()));
                if (optionalAgentStore.isPresent()) {
                    spAgentStore = optionalAgentStore.get();
                    if (crudTransactions.fetchforceUpdateStatus(spAgentStore.getClientId()).equals("1")) {
                        crudTransactions.sendSmswithLink(spAgentStore.getClientId(), agentStoreUser.getContactMsisdn());
                        LOGGER.error(" ---------------- AGENT STORE APP VERSION DEPRECATED ---------------- ");
                        String errorNarration = errorMessageService.getErrorMessage(clientId, "appVersionDeprecated",
                                "Agent app you're using is of a lower version,Kindly check Sms with link to download the new app!");
                        error.setMessage("Agent app version deprecated");
                        error.setSuccessStatus("01");
                        error.setNarration(errorNarration);
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
                    } else {
                        LOGGER.info(" ---------------- AGENT STORE SUCCESSFUL LOGIN ---------------- ");
                        spAgentStore.setContactMsisdn(agentStoreUser.getContactMsisdn());
                        spAgentStore.setContactName(agentStoreUser.getContactName());
                        configurationResponseMapper.setAgentStores(spAgentStore);
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(configurationResponseMapper), secretKey));

                        /*return new ResponseEntity<>(Crypt.encrypt(
                                new ObjectMapper().writeValueAsString(configurationResponseMapper),
                                secretKey), HttpStatus.OK);*/
                    }
                } else {
                    LOGGER.error(" ---------------- AGENT STORE CONFIGURATION MISSING ---------------- ");
                    error.setMessage("Agent Store Details for Logged In User does not Exist!");
                    error.setNarration("Invalid Agent.");
                    error.setSuccessStatus("01");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                    //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
                }
            }
        } catch (Exception e) {
            LOGGER.error("PIN AUTHENTICATION EXCEPTION CAUGHT " + e.getMessage());
            e.printStackTrace();
            error.setNarration("An error occurred during authentication");
            error.setSuccessStatus("01");
        }
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
    }

    /**
     * Endpoint to validate the agent's pin before processing any transaction.
     *
     * @param payload
     * @return Validating the agent's pin before processing any transaction.
     */
    @PostMapping(value = "/verifyAgentPin", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> verifyAgentPin(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        LOGGER.info("..............AGENT PIN VERIFICATION ...............");
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");

            AuthResponse response = new AuthResponse("01", errorNarration);
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(encryptedPayload.replace("\n", ""), secretKey);

        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        String pin = requestMap.get("pin");
        String msisdn = requestMap.get("msisdn");

        //00(Pass), 01(Invalid Agent/Pin), 02(Agent is blacklisted/Deactivated)
        final String responseCodeKey = "responseCode";
        final String errorMessageKey = "errorMessage";

        HashMap<String, String> response = new HashMap<>();
        response.put(responseCodeKey, "00");
        //ResponseEntity<HashMap<String,String>> responseEntity = new ResponseEntity<>(response, HttpStatus.OK);

        if (ObjectUtils.isEmpty(pin) || ObjectUtils.isEmpty(msisdn)) {
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(
                    new AuthResponse("01", "Sorry, an error occurred during pin verification. Please try again later")), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.BAD_REQUEST);
        } else {
            try {
                //First check if the store user is deactivated
                SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUserWithId(storeUserId);
                if (spStoreUsers != null) {
                    boolean storeUserActive = spStoreUsers.getActiveStatus() != null
                            && spStoreUsers.getActiveStatus().equals(BigInteger.ONE);

                    if (storeUserActive) {
                        SpStoreUsers agentStoreUser = crudTransactions.fetchAgentStoreUserDataWithMsiSdnAndPin(msisdn.trim(), pin.trim());
                        if (agentStoreUser == null) {
                            LOGGER.error(" ---------------- SPOTCASH AGENT USER VERIFICATION FAILED ---------------- ");
                            //INVALID PIN
                            //SpStoreUsers storeUser = crudTransactions.fetchStoreUser(msisdn, clientId);

                            int pinRetries = spStoreUsers.getPinRetries() != null ? spStoreUsers.getPinRetries() : 0;
                            //BigInteger storeUserId = storeUser != null ? storeUser.getStoreUserId() : BigInteger.ZERO;

                            SpAgents spAgent = crudTransactions.fetchAgentData(BigInteger.valueOf(Long.parseLong(clientId)));
                            int retryCount = spAgent != null ? spAgent.getLockCount().intValue() : 3;
                            String spAgentId = spAgent != null ? String.valueOf(spAgent.getAgentId()) : "0";

                            //update store user pin retries
                            crudTransactions.updateStoreUserPinRetry(msisdn, String.valueOf(++pinRetries), clientId);

                            if (pinRetries >= retryCount) {
                                //Deactivate the store user
                                crudTransactions.deactivatedStoreUser(msisdn, clientId);
                                SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
                                spAgentDeactivations.setDateCreated(new java.util.Date(System.currentTimeMillis()));
                                spAgentDeactivations.setAgentId(BigInteger.valueOf(Long.parseLong(spAgentId)));
                                spAgentDeactivations.setClientId(BigInteger.valueOf(Long.parseLong(clientId)));
                                spAgentDeactivations.setDeactivationType("Store User Deactivation");
                                spAgentDeactivations.setStatus("Pending");
                                spAgentDeactivations.setStoreUserId(BigInteger.valueOf(Long.parseLong(storeUserId)));
                                spAgentDeactivations.setDeactivationReason("Deactivated after exceeding PIN retries.");
                                spAgentDeactivations.setAgentStoreId(spStoreUsers.getAgentStoreId());
                                crudTransactions.save(spAgentDeactivations);
                                LOGGER.info("Deactivated store user(Exceeded Pin retries): " +
                                                "msisdn :: {}, clientId :: {}, pinCount :: {}, storeUser pin retries :: {}",
                                        msisdn, clientId, pinRetries, retryCount);

                                //Reset the retry count to 0
                                crudTransactions.resetStoreUserPinRetry(msisdn, clientId);
                                response.put(responseCodeKey, "02");
                                response.put(errorMessageKey, "Pin retries exceeded. Your account has been deactivated!");
                            } else {
                                response.put(responseCodeKey, "01");
                                response.put(errorMessageKey, String.format("Invalid Pin. %s pin retries left!",
                                        Integer.max(retryCount - pinRetries, 0)));
                            }
                        } else {
                            //long storeUserId = agentStoreUser.getStoreUserId().longValue();
                            if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
                                LOGGER.info("STORE USER WITH ID :: {}, IS BLACKLISTED!", storeUserId);

                                String errorNarration = errorMessageService.getErrorMessage(
                                        clientId, "agentBlacklisted",
                                        "Dear Agent, your account is locked, please contact sacco for activation!");

                                response.put(responseCodeKey, "02");
                                response.put(errorMessageKey, errorNarration);
                            } else {
                                Optional<SpAgentStores> optionalAgentStore = Optional.ofNullable(crudTransactions.fetchAgentWithStoreId(agentStoreUser.getAgentStoreId()));
                                if (optionalAgentStore.isPresent()) {
                                    SpAgentStores spAgentStore = optionalAgentStore.get();
                                    //Reset the pin retry count to 0 after successful authentication
                                    crudTransactions.resetStoreUserPinRetry(msisdn, clientId);
                                    if (crudTransactions.fetchforceUpdateStatus(spAgentStore.getClientId()).equals("1")) {
                                        crudTransactions.sendSmswithLink(spAgentStore.getClientId(), agentStoreUser.getContactMsisdn());
                                        LOGGER.error(" ---------------- AGENT STORE APP VERSION DEPRECATED ---------------- ");
                                        response.put(responseCodeKey, "01");
                                        response.put(errorMessageKey, "Agent app you're using is of a lower version, " +
                                                "Kindly check Sms with link to download the new app!");
                                    } else {
                                        LOGGER.info(" ---------------- AGENT STORE SUCCESSFUL LOGIN ---------------- ");
                                        response.put("storeUser", new Gson().toJson(agentStoreUser));
                                        response.put("agentStore", new Gson().toJson(spAgentStore));
                                    }
                                } else {
                                    LOGGER.error(" ---------------- AGENT STORE CONFIGURATION MISSING ---------------- ");
                                    response.put(responseCodeKey, "01");
                                    response.put(errorMessageKey, "Invalid Agent or Pin");
                                }
                            }
                        }
                    } else {
                        String errorNarration = errorMessageService.getErrorMessage(
                                clientId, "agentDeactivated",
                                "Dear Agent, your account is deactivated, please contact sacco for activation");

                        response.put(responseCodeKey, "02");
                        response.put(errorMessageKey, errorNarration);
                    }
                } else {
                    response.put(responseCodeKey, "01");
                    response.put(errorMessageKey, "Invalid Agent");
                }
            } catch (Exception e) {
                LOGGER.error("PIN AUTHENTICATION EXCEPTION CAUGHT " + e.getMessage());
                e.printStackTrace();
                //response.put(responseCodeKey, "01");
                //response.put(errorMessageKey, "Sorry, an error occurred during Pin verification. Please try again later");
                AuthResponse error = new AuthResponse("01",
                        "Sorry, an error occurred during Pin verification. Please try again later");

                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                //responseEntity = new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        //return responseEntity;
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
    }


    /**
     * @return Validating the customer's pin before processing any transaction.
     */
    @Deprecated
    @RequestMapping(value = "/isPinCorrect", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> isPinCorrect(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String pin = requestMap.get("pin");
        String nationalId = requestMap.get("nationalId");
        String clientid = requestMap.get("clientid");
        LOGGER.info("validating the pin............");
        ApiResponse response = new ApiResponse();
        response.setNarration("WRONG PIN");
        response.setSuccessStatus("01");

        if (clientid != null) {
            try {
                Optional<SpCustomers> optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerByPin(pin, nationalId, clientid));
                if (optionalSpCustomer.isPresent()) {
                    LOGGER.info("..............PIN VERIFICATION SUCCESSFUL...............");
                    response.setSuccessStatus("00");
                    //response.setMessage("PIN CORRECT");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                    //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                } else {
                    LOGGER.info("getting pin from secondary connection");
                    optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerByPinNewConn(pin, nationalId, clientid));
                    if (optionalSpCustomer.isPresent()) {
                        LOGGER.info("..............PIN VERIFICATION SUCCESSFUL...............");
                        response.setSuccessStatus("00");
                        //response.setMessage("PIN CORRECT");
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                    } else {
                        LOGGER.info("...............CUSTOMER ENTERED THE WRONG PIN NUMBER...............");
                        response.setSuccessStatus("01");
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("CONFIRM PIN EXCEPTION CAUGHT {}", e.getMessage(), e);
                e.printStackTrace();
            }
        } else {
            try {
                Optional<SpCustomers> optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerByPinNoClient(pin, nationalId));
                if (optionalSpCustomer.isPresent()) {
                    LOGGER.info("..............PIN VERIFICATION SUCCESSFUL...............");
                    //response.setMessage("PIN CORRIspinCorrectECT");
                    response.setSuccessStatus("00");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                    //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                } else {
                    LOGGER.info("getting pin from secondary connection");
                    optionalSpCustomer = Optional.ofNullable(crudTransactions.fetchCustomerByPinNewConnNoClient(pin, nationalId));
                    if (optionalSpCustomer.isPresent()) {
                        LOGGER.info("..............PIN VERIFICATION SUCCESSFUL...............");
                        //response.setMessage("PIN CORRECT");
                        response.setSuccessStatus("00");
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
                    } else {
                        LOGGER.info("...............CUSTOMER ENTERED THE WRONG PIN NUMBER...............");
                        response.setSuccessStatus("01");
                        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
                        /*return new ResponseEntity<>(Crypt.encrypt(
                                new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);*/
                    }
                }
            } catch (Exception e) {
                LOGGER.error("CONFIRM PIN EXCEPTION CAUGHT {}", e.getMessage(), e);
                e.printStackTrace();
            }
        }
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey), HttpStatus.OK);
    }

    /**
     * @param payload
     * @return Validating the customer's pin before processing any transaction.
     */
    @PostMapping(value = "/verifyCustomerPin", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> verifyCustomerPin(@RequestBody String payload, HttpServletResponse resp, HttpServletRequest servletRequest) throws Exception {
        LOGGER.info("..............CUSTOMER PIN VERIFICATION ...............");
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        String pin = requestMap.get("pin");
        String nationalId = requestMap.get("nationalId");
        //String clientId = requestMap.get("clientid");
        //String agentStoreId = requestMap.get("agentStoreId");
        String customerMsisdn = requestMap.get("msisdn");

        final String responseCodeKey = "responseCode"; //00(Pass), 01(fail), 02(customer blacklisted)
        final String errorMessageKey = "errorMessage";

        HashMap<String, String> response = new HashMap<>();
        response.put(responseCodeKey, "00");

        if (ObjectUtils.isEmpty(pin) || ObjectUtils.isEmpty(nationalId) || ObjectUtils.isEmpty(clientId)) {
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(
                    new AuthResponse("01",
                            "Sorry, an error occurred during pin verification. Please try again later")), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.BAD_REQUEST);
        } else {
            SpCustomers spCustomer = crudTransactions.fetchCustomerByClientIdAndNationalId(
                    Long.parseLong(clientId), nationalId);

            if (spCustomer != null) {
                if (crudTransactions.isBlacklisted(spCustomer.getMsisdn(), Long.parseLong(clientId))) {
                    response.put(responseCodeKey, "02");
                    response.put(errorMessageKey, "Customer is blacklisted!");
                } else {
                    String hashedPin = new SpotcashUtilities().md5password(pin);
                    if (!spCustomer.getPinNo().equals(hashedPin)) {
                        HashMap<String, Integer> pinResult = transactionService.updateCustomerPinEntries(
                                nationalId.trim(), Long.parseLong(clientId),
                                Long.parseLong(agentStoreId), SharedFunctions.getRemoteIpAddress(servletRequest, environment));

                        int pinRetriesLeft = pinResult.get("pinRetriesLeft");
                        response.put(responseCodeKey, "01");
                        response.put("pinRetriesLeft", String.valueOf(pinRetriesLeft));
                        String errMsg = pinRetriesLeft == 0
                                ? "Pin Retries exceeded. Customer has been blacklisted!"
                                : String.format("Invalid Pin. %s Pin retries left", pinRetriesLeft);
                        response.put(errorMessageKey, errMsg);
                        LOGGER.info("PIN RESPONSE >>>> {} <<<<", response);
                    } else {
                        spCustomer.setWrongPinEntries(0); //Return the wrong pin entries to 0
                        crudTransactions.save(spCustomer);
                        LOGGER.info("..............CUSTOMER PIN VERIFICATION SUCCESSFUL...............");
                    }
                }
            } else {
                response.put(responseCodeKey, "01");
                response.put(errorMessageKey, "Invalid Pin");
                response.put("pinRetriesLeft", "0");
            }
        }

        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey));
    }

    /**
     * Fetching the customer's accounts from the Core Banking System.
     */
    @RequestMapping(value = "/accounts", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getSpCustomerAccounts(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());

        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        LOGGER.info("fetching customer accounts...");
        LOGGER.info(requestMap.toString());
        ApiResponse error = new ApiResponse();
        error.setSuccessStatus("01");
        error.setNarration("Invalid Response from CBS");
        String clientId = requestMap.get("clientId");
        String idNumber = requestMap.get("idNumber");

        try {
            CbsAccountsResponseMapper accountsResponseMapper;
            Optional<CbsAccountsResponseMapper> optionalCustomerAccounts = Optional.ofNullable(
                    cbsService.fetchCustomerAccounts(idNumber, clientId));
            if (optionalCustomerAccounts.isPresent()) {
                accountsResponseMapper = optionalCustomerAccounts.get();
            } else {
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt
                        (new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
            }

            LOGGER.info(" ---------------- CBS RESPONSE RECEIVED ---------------- ");
            LOGGER.info("accountsResponseMapper " + accountsResponseMapper);
            LOGGER.info("accountsResponseMapper " + new ObjectMapper().writeValueAsString(accountsResponseMapper));

            if (!accountsResponseMapper.getResponseCode().trim().matches(
                    ResponseCodes.SUCCESS.getResponseCode())
                    && !accountsResponseMapper.getResponseCode().trim().matches(
                    ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
                LOGGER.info(" ---------------- CBS RESPONSE FAIL ---------------- ");
                error.setSuccessStatus("01");
                error.setNarration(accountsResponseMapper.getErrorString());
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
            }
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(accountsResponseMapper), secretKey));
            //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(accountsResponseMapper), secretKey), HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("FETCHING ACCOUNTS EXCEPTION CAUGHT " + e.getMessage());
            e.printStackTrace();
        }
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
    }


    /**
     * Fetching the customer's accounts for local verification
     */
    @RequestMapping(value = "/verifyaccount", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> verifyAccountDetails(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            LOGGER.error("Action Request is outside working hours!");
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        ApiResponse error = new ApiResponse();
        String clientId = requestMap.get("clientId");
        String errorNarration = errorMessageService.getErrorMessage(clientId, "invalidCbsResponse",
                "Invalid Response from CBS");
        error.setSuccessStatus("01");
        error.setNarration(errorNarration);

        //Todo: Generate a new access token if it is about to expire

        ValidateAccountResponseMapper accountsResponseMapper;
        try {
            Optional<ValidateAccountResponseMapper> optionalAccountDetails = Optional.ofNullable(
                    cbsService.validateCustomerAccounts(requestMap.get("accountNumber"), requestMap.get("clientId")));
            if (optionalAccountDetails.isPresent()) {
                accountsResponseMapper = optionalAccountDetails.get();
                if (accountsResponseMapper.getResponseCode() == null) {
                    LOGGER.info(" ---------------- Error on Validate Account ---------------- ");
                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                    /*return new ResponseEntity<>(Crypt.encrypt(
                            new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
                }
            } else {
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(
                        new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
            }

            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(accountsResponseMapper), secretKey));
            /*return new ResponseEntity<>(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(accountsResponseMapper), secretKey), HttpStatus.OK);*/
        } catch (Exception e) {
            LOGGER.error("VERIFY ACCOUNT EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        //return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);
    }

    /**
     * Fetching the agent's transactions Z-Report from the Core Banking System.
     */
    @RequestMapping(value = "/transactions", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getTransactions(@RequestBody String payload, HttpServletRequest request,
                                             HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        String clientId = requestMap.get("clientId");

        LOGGER.info("Create the record in the SpLocationLog for Geo Mapping");
        try {
            Gson gson = new Gson();
            String locality = !ObjectUtils.isEmpty(requestMap.get("locality")) ? requestMap.get("locality").split(",")[0] : "";
            HashMap<String, String> latLong = new LinkedHashMap<>();
            latLong.put("Longitude", requestMap.get("longitude"));
            latLong.put("Latitude", requestMap.get("latitude"));

            HashMap<String, String> location = new HashMap<>();
            location.put(locality, gson.toJson(latLong));
            String ipAddress = SharedFunctions.getRemoteIpAddress(request, environment);
            SpLocationLog spLocationLog = new SpLocationLog();
            spLocationLog.setRequestTime(new Date(System.currentTimeMillis()));
            spLocationLog.setClientId(new BigInteger(requestMap.get("clientId")));
            spLocationLog.setLocation((new ObjectMapper().writeValueAsString(latLong)));
            spLocationLog.setAgentId(new BigDecimal(jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue())));
            spLocationLog.setAgentStoreId(new BigInteger(jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue())));
            spLocationLog.setStoreUserId(new BigInteger(jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue())));
            spLocationLog.setIpAddress(ipAddress);
            spLocationLog.setTransactionType("Z-Report");
            crudTransactions.updateLocationLog(spLocationLog);
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Error saving Location record: {}", ex.getMessage());
        }
        ApiResponse error = new ApiResponse();
        String errorNarration = errorMessageService.getErrorMessage(clientId, "invalidCbsResponse",
                "Invalid Response from CBS");
        error.setSuccessStatus("01");
        error.setNarration(errorNarration);
        CbsTransactionsResponseMapper transactionsResponseMapper;
        try {
            Optional<CbsTransactionsResponseMapper> optionalTransactions
                    = Optional.ofNullable(
                    cbsService.fetchTransactions(
                            requestMap.get("clientId"), requestMap.get("agentPhoneNo"),
                            requestMap.get("startDate"), requestMap.get("endDate"),
                            requestMap.get("startTime"), requestMap.get("endTime")
                    )

            );
            if (optionalTransactions.isPresent()) {
                transactionsResponseMapper = optionalTransactions.get();
            } else {
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(
                        new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
            }
            LOGGER.info(" ---------------- CBS RESPONSE RECEIVED ---------------- ");
            LOGGER.info("transactionsResponseMapper " + transactionsResponseMapper);
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(transactionsResponseMapper), secretKey));
            /*return new ResponseEntity<>(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(transactionsResponseMapper), secretKey), HttpStatus.OK);*/
        } catch (Exception e) {
            LOGGER.error("FETCH Z-REPORT EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
        /*return new ResponseEntity<>(Crypt.encrypt(
                new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
    }

    /**
     * Registering a new member
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> registerNewMember(@RequestBody String payload, HttpServletRequest request,
                                               HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        //Todo: Generate a new access token if it is about to expire

        ApiResponse error = new ApiResponse();
        error.setSuccessStatus("01");
        error.setNarration("Customer registration Failed");
        try {
            NewMember member = cbsService.insertNewMember(requestMap);
            LOGGER.info("member :: {}", new ObjectMapper().writeValueAsString(member));
            if (member == null) {
                LOGGER.error(" ---------------- Error on registration ---------------- ");
                error.setNarration("Invalid response from CBS");
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
                /*return new ResponseEntity<>(Crypt.encrypt(
                        new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
            }
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(member), secretKey));
            /*return new ResponseEntity<>(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(member), secretKey), HttpStatus.OK);*/
        } catch (Exception ex) {
            LOGGER.error("Error occurred during registration {}", ex.getMessage(), ex);
            ex.printStackTrace();
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey));
            /*return new ResponseEntity<>(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(error), secretKey), HttpStatus.OK);*/
        }
    }

    /**
     * Resetting Pin Feature
     *
     * @return String
     */
    @PostMapping(value = "/resetPin", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> resetPin(@RequestBody String payload, HttpServletRequest request,
                                      HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);

        ApiResponse response = new ApiResponse();
        response.setSuccessStatus("01");
        response.setNarration("Pin reset Failed");

        try {
            String resetPin = crudTransactions.resetAgentStorePin(requestMap.get("msisdn").trim());
            LOGGER.info("Reset Pin Result: " + resetPin);
            if (resetPin == null) {
                LOGGER.error(" ---------------- SPOTCASH AGENT RESET PIN FAILED---------------- ");
                String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
                //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
            }

            response.setSuccessStatus("00");
            response.setNarration("Pin reset is successful");
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);

            return ResponseEntity.status(200).headers(responseHeaders).body(encryptedResponse);
        } catch (Exception e) {
            LOGGER.error("RESET PIN EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
            response.setSuccessStatus("01");
            response.setNarration("Pin reset Failed");
        }

        String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
        //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
    }

    /**
     * Fetching the non_member_transactions accounts from the Core Banking
     * System.
     *
     * @Co-author Polycarp
     */
    @RequestMapping(value = "/nmAccounts", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getNonMemberAccounts(@RequestBody String payload, HttpServletRequest request,
                                                  HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        ApiResponse error = new ApiResponse();
        error.setSuccessStatus("01");
        error.setNarration("Invalid Response from CBS");

        CbsNonMemberAccountsResponseMapper nmaccountsResponseMapper;
        try {
            Optional<CbsNonMemberAccountsResponseMapper> optionalnonMemberAccounts =
                    Optional.ofNullable(cbsService.fetchNonMemberAccounts(requestMap.get("clientId")));
            if (optionalnonMemberAccounts.isPresent()) {
                nmaccountsResponseMapper = optionalnonMemberAccounts.get();
                Gson gson = new Gson();
                String jsonAccounts = gson.toJson(nmaccountsResponseMapper.getAccountDetails());
                String secret = AesEncryption.encodeKey(this.environment.getRequiredProperty("secret.key"));
                nmaccountsResponseMapper.setEncryptedAccounts(AesEncryption.encrypt(jsonAccounts, secret));
            } else {
                String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey);
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
                //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
            }
            LOGGER.info(" ---------------- CBS RESPONSE RECEIVED ---------------- ");
            LOGGER.info("nm accounts ResponseMapper " + nmaccountsResponseMapper);

            if (!nmaccountsResponseMapper.getResponseCode().trim().matches(ResponseCodes.SUCCESS.getResponseCode())
                    && !nmaccountsResponseMapper.getResponseCode().trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
                LOGGER.info(" ---------------- CBS RESPONSE FAIL ---------------- ");
                error.setSuccessStatus("01");
                error.setNarration(nmaccountsResponseMapper.getErrorString());
                String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey);
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
                //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
            }
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(nmaccountsResponseMapper), secretKey);
            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
            //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("NON MEMBER ACCOUNTS EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
            error.setSuccessStatus("01");
            error.setNarration("Error Fetching accounts");
        }
        String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(error), secretKey);
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
        //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
    }

    /**
     * Agent Pin change request
     */
    @PostMapping(value = "/pinChange", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> agentPinChangeRequest(@RequestBody String payload, HttpServletRequest request,
                                                   HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        LOGGER.info("Change Pin Transaction");
        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        String storeUserId = jwtUtil.getClaim(token, StoreUser.STORE_USER_ID.getValue());

        // checks if the agent is blacklisted
        if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUserId))) {
            LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUserId);
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "agentBlacklisted",
                    "Dear Agent, your account is locked, please contact sacco for activation!");

            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        String msisdn = requestMap.get("msisdn");
        String pin = requestMap.get("newPin");

        //ArrayList<Map<String, Object>> myList = new ArrayList<>();
        //myList.add(transactionService.failedSpTransaction(900));
        HashMap<String, Object> response = transactionService.failedSpTransaction(900);
        String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
        try {
            ArrayList<Map<String, Object>> respResult = crudTransactions.updateAgentStorePin(msisdn.trim(), pin.trim());
            if (respResult.size() == 0) {
                LOGGER.error(" ---------------- SPOTCASH AGENT STORE PIN CHANGE FAILED ---------------- ");
                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
                //return new ResponseEntity<>(encryptedResponse, HttpStatus.OK);
            }

            String encryptedResult = Crypt.encrypt(new ObjectMapper().writeValueAsString(respResult.get(0)), secretKey);
            return ResponseEntity.status(200).headers(responseHeaders).body(encryptedResult);
        } catch (Exception e) {
            LOGGER.error("PIN CHANGE EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }

        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(encryptedResponse), secretKey));
    }


    @RequestMapping(value = "/getMember", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getMember(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        GetMemberRequest request = new Gson().fromJson(decryptedPayload, GetMemberRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        GetMemberResponse response = new GetMemberResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try {
            //Check if memberId and ClientID have been provided.
            if (!ObjectUtils.isEmpty(!ObjectUtils.isEmpty(request.getIdNumber()))) {
                //Fetch VPN Configs
                Optional<GetMemberCbsResponse> optionalCbsMemberDetails = Optional.ofNullable(cbsService.getMember(request));
                if (optionalCbsMemberDetails.isPresent()) {
                    GetMemberCbsResponse cbsResponse = optionalCbsMemberDetails.get();

                    //Check status of the CBS Response
                    if (cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                        response.setMember(cbsResponse.getResponseMessage().getMember());
                    } else {
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                ? cbsResponse.getErrorMessage() : "Member not found";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                } else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Member does not exist");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            } else {
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("FAILED TO GET MEMBER. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " + e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Error occurred on fetching member details.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }

    @RequestMapping(value = "/getMemberImage", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getMemberImage(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        GetMemberRequest request = new Gson().fromJson(decryptedPayload, GetMemberRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        GetMemberResponse response = new GetMemberResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try {
            //Check if memberId and ClientID have been provided.
            if (!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getIdNumber())) {
                //Fetch VPN Configs
                Optional<GetMemberCbsResponse> optionalCbsMemberDetails = Optional.ofNullable(cbsService.getMemberImage(request));
                if (optionalCbsMemberDetails.isPresent()) {
                    GetMemberCbsResponse cbsResponse = optionalCbsMemberDetails.get();

                    //Check status of the CBS Response
                    if (cbsResponse.getResponseCode().equalsIgnoreCase("00")
                            || cbsResponse.getResponseCode().equalsIgnoreCase("000")) {
                        response.setMember(cbsResponse.getResponseMessage().getMember());
                    } else {
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                ? cbsResponse.getErrorMessage() : "Member image not available";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                } else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Error occurred on fetching member image");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            } else {
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("FAILED TO GET MEMBER. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " + e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Error occurred on fetching member image");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }

    @RequestMapping(value = "/getEvents", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getEvents(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if (!ObjectUtils.isEmpty(deviceId)) {
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
            }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey != null && token.equals(tokenKey.getJwtToken())) {
            secretKey = tokenKey.getDataKey();
        } else {
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        GetEventsRequest request = new Gson().fromJson(decryptedPayload, GetEventsRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        GetEventsResponse response = new GetEventsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try {
            //Check if storeUserId and ClientID have been provided.
            if (!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getStoreUserId())) {
                //Fetch VPN Configs
                Optional<GetEventsCbsResponse> optionalCbsEvents = Optional.ofNullable(cbsService.getEvents(request));
                if (optionalCbsEvents.isPresent()) {
                    GetEventsCbsResponse cbsResponse = optionalCbsEvents.get();

                    //Check status of the CBS Response
                    if (cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                        response.setEvents(cbsResponse.getResponseMessage().getEvent());
                    } else {
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                ? cbsResponse.getErrorMessage() : "No open events";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                } else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Failed to fetch events");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            } else {
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("FAILED TO GET EVENTS. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " + e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Failed to fetch events.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }


    @RequestMapping(value = "/validateEventCode", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateEventCode(@RequestBody String payload,
                                               HttpServletRequest httpServletRequest,
                                               HttpServletResponse resp) throws Exception {
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);
        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        // Default encryption key from config
        String encryptionKey = environment.getRequiredProperty("app.secretKey");

        // If deviceId is present, fetch dynamic encryption key
        if (!ObjectUtils.isEmpty(deviceId)) {
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if (spAgencyEncryptionKey != null) {
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
            }
        }

        // Validate JWT token and fetch data key
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if (tokenKey == null || !token.equals(tokenKey.getJwtToken())) {
            LOGGER.error("JWT token is invalid. Agent must login again.");
            AuthResponse response = new AuthResponse("01", "Your current session has been invalidated due to app configuration changes. Please login again.");
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(encryptedResponse);
        }

        String secretKey = tokenKey.getDataKey();
        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());

        // Check if request is within working hours
        if (!workingHoursService.requestIsWithinWorkingHours(agentStoreId)) {
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(clientId, "actionOutsideWorkingHours", "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);
            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(encryptedRes);
        }

        // Decrypt payload
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String decryptedPayload = Crypt.decrypt(encryptedRequest.getData().replace("\n", ""), secretKey);
        ValidateEventCodeRequest request = new Gson().fromJson(decryptedPayload, ValidateEventCodeRequest.class);

//        LOGGER.info("Validate event code request :: {}", new Gson().toJson(request));
        ValidateEventsResponse response = new ValidateEventsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try {
            if (!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getStoreUserId())) {
                Optional<ValidateEventsResponse> optionalCbsEvents = Optional.ofNullable(cbsService.validateEventCode(request));
                if (optionalCbsEvents.isPresent()) {
                    ValidateEventsResponse cbsResponse = optionalCbsEvents.get();
                    if ("00".equalsIgnoreCase(cbsResponse.getResponseCode())) {
                        // Map CBS response details if needed
                        // response.setEvents(cbsResponse.getResponseMessage().getEvent());
                    } else {
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setResponseMessage(
                                ObjectUtils.isEmpty(cbsResponse.getResponseMessage())
                                        ? "No open events"
                                        : cbsResponse.getResponseMessage());
                    }
                } else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setResponseMessage("Failed to fetch events");
                }
            } else {
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setResponseMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("FAILED TO GET EVENTS. REQUEST({}). ERROR :: {}", new Gson().toJson(request), e.getMessage());
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setResponseMessage("Failed to fetch events.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }

        String encryptedResponse = Crypt.encrypt(new Gson().toJson(response), secretKey);
        return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders).body(encryptedResponse);
    }



    @RequestMapping(value = "/registerMemberToEvent", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> registerMemberToEvent(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        RegisterMemberRequest request = new Gson().fromJson(decryptedPayload, RegisterMemberRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        RegisterMemberResponse response = new RegisterMemberResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try{
            //Check if idNumber, eventId and have been provided.
            if(!ObjectUtils.isEmpty(request.getIdNumber()) && !ObjectUtils.isEmpty(request.getEventId())
                    && !ObjectUtils.isEmpty(request.getStoreUserId()) && !ObjectUtils.isEmpty(request.getClientId())
                    && !ObjectUtils.isEmpty(request.getAuthenticationMode())) {

                if(request.getAuthenticationMode().equals("1") && request.getRegComment().isEmpty()){
                    //bad request
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Bad Request");
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                            .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
                }
                else{
                    //Fetch VPN Configs
                    Optional<EventRegistrationCbsResponse> optionalCbsResponse = Optional.ofNullable(cbsService.registerMemberToEvent(request));
                    if (optionalCbsResponse.isPresent()) {
                        EventRegistrationCbsResponse cbsResponse = optionalCbsResponse.get();
                        if(cbsResponse.getResponseCode().equalsIgnoreCase("00")){
                            response.setResponseMessage(cbsResponse.getResponseMessage());
                        }
                        else{
                            String errMsg = !ObjectUtils.isEmpty(cbsResponse.getResponseMessage())
                                    ? cbsResponse.getResponseMessage() : "Event Registration Failed";
                            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                            response.setErrorMessage(errMsg);
                        }
                    }
                    else {
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage("Event Registration Failed. Please Try again later.");
                    }

                    return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                            .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
                }
            }
            else{
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        }
        catch (Exception e){
            LOGGER.error("FAILED TO REGISTER MEMBER TO AN EVENT. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " +e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Event registration failed.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }


    //Endpoint for registering non members to event
    @RequestMapping(value = "/registerNonMemberToEvent", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> registerNonMemberToEvent(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            LOGGER.info("This is the client ID" + jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue()));
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        RegisterNonMemberRequest request = new Gson().fromJson(decryptedPayload, RegisterNonMemberRequest.class);

        LOGGER.info("GET EVENT MEMBER REQUEST :: " + new Gson().toJson(request));
        RegisterNonMemberResponse response = new RegisterNonMemberResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try {
            // Check if name, idNumber, phoneNumber, eventId, clientId, storeUserId and gender have been provided.
            if (!ObjectUtils.isEmpty(request.getName())
                    && !ObjectUtils.isEmpty(request.getIdNumber())
                    && !ObjectUtils.isEmpty(request.getGender())
                    && !ObjectUtils.isEmpty(request.getPhoneNumber())
                    && !ObjectUtils.isEmpty(request.getEventId())
                    && !ObjectUtils.isEmpty(request.getClientId())
                    && !ObjectUtils.isEmpty(request.getStoreUserId())) {

                // Fetch VPN Configs
                Optional<EventRegistrationCbsResponse> optionalCbsResponse = Optional.ofNullable(cbsService.registerNonMemberToEvent(request));
                if (optionalCbsResponse.isPresent()) {
                    EventRegistrationCbsResponse cbsResponse = optionalCbsResponse.get();
                    if (cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                        response.setResponseMessage(cbsResponse.getResponseMessage());
                    } else {
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getResponseMessage())
                                ? cbsResponse.getResponseMessage() : "Event Registration Failed";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                } else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Event Registration Failed. Please Try again later.");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));

            } else {
                // Bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        } catch (Exception e) {
            LOGGER.error("FAILED TO REGISTER NON MEMBER TO AN EVENT. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " + e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Event registration failed.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }

    }


    //endpoint fetching redeemable items for a specific event
    @RequestMapping(value = "/getItemsToRedeem", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getItemsToRedeem(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        RedeemItemsRequest request = new Gson().fromJson(decryptedPayload, RedeemItemsRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        RedeemItemsResponse response = new RedeemItemsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try{
            //Check if clientId, eventId and customerId have been provided.
            if(!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getEventId()) && !ObjectUtils.isEmpty(request.getCustomerId())){
                //Fetch VPN Configs
                Optional<RedeemItemCbsResponse> optionalRedeemItemCbsResponse = Optional.ofNullable(cbsService.getRedeemItems(request));
                if (optionalRedeemItemCbsResponse.isPresent()) {
                    RedeemItemCbsResponse cbsResponse = optionalRedeemItemCbsResponse.get();

                    //Check status of the CBS Response
                    if(cbsResponse.getResponseCode().equalsIgnoreCase("00")){
                        if (cbsResponse.getResponseMessage() != null && cbsResponse.getResponseMessage().getRedeemedItems() != null) {
                            response.setRedeemedItems(cbsResponse.getResponseMessage().getRedeemedItems());
                            LOGGER.info("Response from cbs >>>>>>>", cbsResponse);
                        }else {
                            String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                    ? cbsResponse.getErrorMessage() : "No items to redeem";
                            response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());
                            response.setErrorMessage(errMsg);
                        }
                    }
                    else{
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                ? cbsResponse.getErrorMessage() : "No items to redeem";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                }
                else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Failed to fetch items");//todo:
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
            else{
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        }
        catch (Exception e){
            LOGGER.error("FAILED TO GET ITEMS TO REDEEM. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " +e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Failed to fetch items to redeem.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }

    //endpoint handling specific customer's selected items for redeeming
    @RequestMapping(value = "/customerRewards", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> customerSelectedItems(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        CustomerRewardsRequest request = new Gson().fromJson(decryptedPayload, CustomerRewardsRequest.class);

        LOGGER.info("GET MEMBER REQUEST :: " + new Gson().toJson(request));
        CustomerRewardsResponse response = new CustomerRewardsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());
        //ResponseEntity<CustomerRewardsResponse> responseEntity = new ResponseEntity<>(response, HttpStatus.OK);

        try {
            // Fetch VPN Configs
            String clientId = request.getClientId();
            SpVpnclientsConfigs cbsClientConfigData = crudTransactions.fetch_vpn_configs(new BigInteger(clientId));

            //Check if clientId, eventId, customerId and quantity have been provided.
            if (!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getEventId()) && !ObjectUtils.isEmpty(request.getCustomerId()) &&
                    !ObjectUtils.isEmpty(request.getSelectedItems()) && cbsClientConfigData != null) {

                String eventId = request.getEventId();
                String customerId = request.getCustomerId();
                String customerMsisdn = request.getCustomerMsisdn();

                List<RedeemItem> failedItems = new ArrayList<>();
                StringBuilder failedMsg = new StringBuilder();

                //todo float validation and initialise transaction- when the redeem item is cash and disbursement is mpesa
                Agent agent = new Agent();
                String float_ = agent.getFloat();
                LOGGER.info("This is the agent float: " + float_);
                if (float_ != null && !float_.isEmpty()) {
                    try {
                        float parsedFloat = Float.parseFloat(float_);
                        LOGGER.info("Float value is valid: " + parsedFloat);
                    } catch (NumberFormatException e) {
                        LOGGER.info("Float value is invalid.");
                    }
                } else {
                    LOGGER.info("Float value is null or empty.");
                }
                EncryptedData encryptedData = new Gson().fromJson(payload, EncryptedData.class);
                String encryptedPayload1 = encryptedData.getTransTemp();

                // Fetch dataKey from the DB using the received token
                String secretKey1 = "";
                SpTokenKey tokenKey1 = crudTransactions.fetchTokenAndKeyWithToken(token);
                if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey1 = tokenKey1.getDataKey(); }
                String requestParams = Crypt.decrypt(encryptedPayload1.replace("\n",""),  secretKey1);
                LOGGER.info("REQUEST FROM DEVICE " + requestParams);
                HttpProcessorRequest requestBody = new SpotcashUtilities().returnMappedXipRequest(requestParams);


                for (RedeemItem redeemItem : request.getSelectedItems()) {
                    Optional<GetCustomerRewardsCbsResponse>  optionalGetCustomerRewardsCbsResponse = null;
                    // Call the method with VPN configs passed as a parameter

                    LOGGER.info("Customer Msisdn {} ", customerMsisdn);
                    if (redeemItem.getRewardName().equalsIgnoreCase("Cash")) {
                        if (request.getDisbursementMethod() != null && request.getDisbursementMethod().equalsIgnoreCase("Mpesa")) {
                            LOGGER.info("DisbursementMethod {}  ", request.getDisbursementMethod());
                            redeemItem.setDisbursementMethod(request.getDisbursementMethod());
                            //handle mpesa case here
                            if (redeemItem.getRewardName().equalsIgnoreCase("cash")) {
                                BigDecimal amount = BigDecimal.valueOf(redeemItem.getQuantity());
                                String serviceId = "798";
                                validationResponseMessage = validateEventFloat(amount, new BigDecimal(clientId), customerMsisdn, crudTransactions, serviceId);
                                if (validationResponseMessage.contains("00")) {
                                    //initiate transaction.
                                    //HttpProcessorRequest requestBody = new SpotcashUtilities().returnMappedXipRequest(String.valueOf(decryptedPayload));
                                    SpotcashTrxDetails transactionDataObj = new SpotcashTrxDetails();
                                    transactionDataObj.setAmount(String.valueOf(amount));
                                    transactionDataObj.setCustomer_msisdn(customerMsisdn);
                                    transactionDataObj.setCustomer_name(request.getCustomerName());
                                    transactionDataObj.setClient_id(clientId);
                                    transactionDataObj.setSpotcash_agent_id(new BigInteger(agentId));
                                    transactionDataObj.setSpotcash_service_id(serviceId);
                                    SpAgents spAgent = null;

                                    Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(clientId)));
                                    if (optionalAgent.isPresent()) {
                                        spAgent = optionalAgent.get();
                                        LOGGER.info("AGENT AUTH: " + spAgent.getCbsAuth());
                                        int resp1 = spAgent.getCbsAuth().compareTo(new BigInteger("1"));
                                        if (resp1 == 0) {
                                            cbsAuth = true;
                                        }
                                        LOGGER.info("AGENT AUTH BOOL: " + cbsAuth);
                                    } else {
                                        LOGGER.info("AGENT NOT FOUND PASSED ");
                                    }
                                    SpAgentStores agentStoreData = crudTransactions.fetchAgentStoreData(requestBody.getHeader().getUserXid(), "254" + requestBody.getHeader().getMsisdn());
                                    String trxId = createTransTempPayment(transactionDataObj, spAgent, agentStoreData, requestBody);
                                    GetCustomerRewardsCbsResponse rewardsResponse = new GetCustomerRewardsCbsResponse();
                                    if (trxId != null) {
                                        optionalGetCustomerRewardsCbsResponse = Optional.ofNullable(cbsService.insertCustomerRewards(redeemItem, clientId, eventId, customerId, cbsClientConfigData, trxId));
                                        LOGGER.info("CBS RESPONSE " + rewardsResponse);
                                        //update transaction
                                        if (optionalGetCustomerRewardsCbsResponse.get().getResponseCode().equalsIgnoreCase("00")) {
                                            SpTransTempTable spTransTempTable = crudTransactions.getTransaction(trxId);
                                            spTransTempTable.setTrxStatus(BigInteger.valueOf(2));
                                            spTransTempTable.setIntermediateStatus(BigInteger.valueOf(1));
                                            spTransTempTable.setRespCode("00");
                                            crudTransactions.updateTransTempTable(spTransTempTable);
                                        }
                                        response.setResponseMessage("Items redeemed successfully");
                                    } else {
                                        failedItems.add(redeemItem);
                                        failedMsg.append(rewardsResponse.getResponseMessage());
                                        LOGGER.info("Response from cbs >>>>>>>", response);
                                        response.setResponseMessage(failedMsg.toString());
                                    }
                                }
                            }
                        } else if (request.getDisbursementMethod() != null && request.getDisbursementMethod().equalsIgnoreCase("FOSA")) {
                            redeemItem.setDisbursementMethod(request.getDisbursementMethod());
                            optionalGetCustomerRewardsCbsResponse =
                                    Optional.ofNullable(cbsService.insertCustomerRewards(redeemItem, clientId, eventId, customerId, cbsClientConfigData, null));
                            if (optionalGetCustomerRewardsCbsResponse.isPresent()) {
                                GetCustomerRewardsCbsResponse cbsResponse = optionalGetCustomerRewardsCbsResponse.get();

                                // Check status of the CBS Response
                                if (!cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                                    failedItems.add(redeemItem);
                                    failedMsg.append(cbsResponse.getErrorMessage());
                                    LOGGER.info("Response from cbs >>>>>>>", response);
                                } else {
                                    //TODO
                                    response.setResponseMessage("Items redeemed successfully");


                                }
                            } else {
                                failedItems.add(redeemItem);
                            }

                        } else if (request.getDisbursementMethod() != null && request.getDisbursementMethod().equalsIgnoreCase("Cash")) {
                            redeemItem.setDisbursementMethod(request.getDisbursementMethod());
                            optionalGetCustomerRewardsCbsResponse =
                                    Optional.ofNullable(cbsService.insertCustomerRewards(redeemItem, clientId, eventId, customerId, cbsClientConfigData, null));
                            if (optionalGetCustomerRewardsCbsResponse.isPresent()) {
                                GetCustomerRewardsCbsResponse cbsResponse = optionalGetCustomerRewardsCbsResponse.get();

                                // Check status of the CBS Response
                                if (!cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                                    failedItems.add(redeemItem);
                                    failedMsg.append(cbsResponse.getErrorMessage());
                                    LOGGER.info("Response from cbs >>>>>>>", response);
                                } else {
                                    //TODO
                                    response.setResponseMessage("Items redeemed successfully");
                                }
                            } else {
                                failedItems.add(redeemItem);
                            }

                        }
                    }else {
                        optionalGetCustomerRewardsCbsResponse =
                                Optional.ofNullable(cbsService.insertCustomerRewards(redeemItem, clientId, eventId, customerId, cbsClientConfigData, null));
                        if (optionalGetCustomerRewardsCbsResponse.isPresent()) {
                            GetCustomerRewardsCbsResponse cbsResponse = optionalGetCustomerRewardsCbsResponse.get();

                            // Check status of the CBS Response
                            if (!cbsResponse.getResponseCode().equalsIgnoreCase("00")) {
                                failedItems.add(redeemItem);
                                failedMsg.append(cbsResponse.getErrorMessage());
                                LOGGER.info("Response from cbs >>>>>>>", response);
                            }
                            else{
                                //TODO
                                response.setResponseMessage("Items redeemed successfully");
                            }
                        } else {
                            failedItems.add(redeemItem);
                        }
                    }
                }

                //request.getSelectedItems().stream().filter(failedItems::contains).collect(Collectors.toList());

                //handling failedItems state
                if (!failedItems.isEmpty()) {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage(failedMsg.toString());
                    response.setFailedItems(failedItems);
                } else {
                    response.setResponseMessage("Items redeemed successfully");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            } else {
                //bad request or missing VPN configs
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request or Missing VPN Configs");
                response.setFailedItems(request.getSelectedItems());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        }
        catch (Exception e) {
            LOGGER.error("FAILED TO REDEEM CUSTOMER REWARDS. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " + e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Redeeming customer rewards unsuccessful.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }

    //endpoint to fetch all redeemed customer rewards
    @RequestMapping(value = "/getRedeemedCustomerRewards", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getRedeemedCustomerRewards(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception{
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        RedeemedCustomerRewardsRequest request = new Gson().fromJson(decryptedPayload, RedeemedCustomerRewardsRequest.class);

        LOGGER.info("GET REQUEST :: " + new Gson().toJson(request));
        RedeemedCustomerRewardsResponse response = new RedeemedCustomerRewardsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try{
            //Check if memberId and CLientID have been provided.
            if(!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getCustomerId())){
                //Fetch VPN Configs
                Optional<GetRedeemedCustomerRewardsCbsResponse> optionalGetRedeemedCustomerRewardsCbsResponse = Optional.ofNullable(cbsService.getAllRedeemedRewards(request));
                if (optionalGetRedeemedCustomerRewardsCbsResponse.isPresent()) {
                    GetRedeemedCustomerRewardsCbsResponse cbsResponse = optionalGetRedeemedCustomerRewardsCbsResponse.get();

                    //Check status of the CBS Response
                    if(cbsResponse.getResponseCode().equalsIgnoreCase("00")){
                        response.setRedeemedCustomerRewards(cbsResponse.getResponseMessage().getRedeemedCustomerRewards());
                    }
                    else{
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getErrorMessage())
                                ? cbsResponse.getErrorMessage() : "Redeemed Rewards not found";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }
                }
                else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Rewards not found");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
            else{
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        }
        catch (Exception e){
            LOGGER.error("FAILED TO FETCH REDEEMED REWARDS. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " +e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Error occurred on fetching member details.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }

    public String validateEventFloat(BigDecimal amount, BigDecimal clientId, String customerMsisdn, CrudTransactionController crudTransactionController, String serviceId) {
        // check if float is available
        LOGGER.info("-------- FETCHING AVAILABLE ACCOUNTS -------");
        String response = "00#00";
        SpAccounts myAccounts =  null;
        // Ignore validation of float accounts for inter accunt transfer
        if(!serviceId.equalsIgnoreCase("708")) myAccounts = crudTransactionController.fetchAccounts(String.valueOf(clientId), serviceId);

        boolean floatAvailable = ("708".equalsIgnoreCase(serviceId))
                || (myAccounts != null && myAccounts.getAvailBal() != null
                && amount.compareTo(myAccounts.getAvailBal()) <= 0);
        if(!floatAvailable) response = "01#07";

        return response;
    }
    private BigInteger getMaximumDailyAmount(BigDecimal clientId, String customerMsisdn, CrudTransactionController crudTransactionController, String serviceId) {
        BigInteger maxDailyAllowed = crudTransactionController.getMaxAllowed(String.valueOf(clientId), customerMsisdn, serviceId);
        return maxDailyAllowed;
    }
    public String createTransTempPayment(SpotcashTrxDetails transactionDataObj, SpAgents spAgent, SpAgentStores agentStoreData, HttpProcessorRequest requestData) throws IOException {
        Calendar currenttime = Calendar.getInstance();
        Date sqldate = new Date((currenttime.getTime()).getTime());
        BigDecimal return_id = null;
        String response = null;
        BigDecimal commission;
        BigDecimal serviceCharge;
        BigDecimal amountConverted = null;
        SpTransTempTable tranTempObj = new SpTransTempTable();
        LOGGER.info("GETTING COMMISSION SERVICE ID## " + transactionDataObj.getSpotcash_service_id());
        LOGGER.info("GETTING COMMISSION CLIENT ID ## " + transactionDataObj.getClient_id());
        LOGGER.info("GETTING COMMISSION ## AMOUNT " + transactionDataObj.getAmount());
        try {
            String amount = transactionDataObj.getAmount();

            if (amount != null && !amount.trim().isEmpty()) {
                // Remove any commas from the amount string before converting
                amount = amount.replace(",", "");
                amountConverted = new BigDecimal(amount);
            } else {
                // Handle the null or empty amount case
                throw new IllegalArgumentException("Amount is null or empty");
            }
            Map<String, BigDecimal> comissionChargeData = getAgencyCommissionServiceChargeData(new BigInteger(transactionDataObj.getSpotcash_service_id()), new BigInteger(transactionDataObj.getClient_id()), amountConverted);
            LOGGER.info("COMMISSION MAP RETURNED ## " + comissionChargeData);
            if (comissionChargeData != null) {
                if (comissionChargeData.size() > 0) {
                    if (comissionChargeData != null) { //Check using service id type
                        commission = comissionChargeData.get("commission");
                        serviceCharge = comissionChargeData.get("serviceCharge");
                    } else { //Non commissionable transaction
                        commission = BigDecimal.ZERO;
                        serviceCharge = BigDecimal.ZERO;
                    }
                } else {
                    commission = BigDecimal.ZERO;
                    serviceCharge = BigDecimal.ZERO;
                }
            } else {
                commission = BigDecimal.ZERO;
                serviceCharge = BigDecimal.ZERO;
            }
            LOGGER.info("commission . " + commission);
            LOGGER.info("serviceCharge . " + serviceCharge);
            String instransactionId = getupdateTransactionId();

            LOGGER.info("Create the record in the SpLocationLog for Geo Mapping");
            try {
                String transactionType = "";
                SpServices service = crudTransactions.fetchServiceWithId(transactionDataObj.getSpotcash_service_id());
                if(service != null){
                    transactionType = service.getTitle();
                }
                Gson gson = new Gson();
//                String locality = requestData.getRequestData().getLocality().split(",")[0];
//                HashMap<String, String> latLong = new LinkedHashMap<>();
//                latLong.put("Longitude", requestData.getRequestData().getLongitude());
//                latLong.put("Latitude", requestData.getRequestData().getLatitude());

                HashMap<String, String> location = new HashMap<>();
//                location.put(locality, gson.toJson(latLong));
                String ipAddress = SharedFunctions.getRemoteIpAddress(request, environment);
                SpStoreUsers storeUsers = crudTransactions.fetchAgentStoreUserDataWithAgentStoreId(transactionDataObj.getSpotcash_agent_store_id().toString());
                SpLocationLog spLocationLog = new SpLocationLog();
                spLocationLog.setTrxId(instransactionId);
                spLocationLog.setRequestTime(sqldate);
                spLocationLog.setClientId(new BigInteger(transactionDataObj.getClient_id()));
                spLocationLog.setLocation((new ObjectMapper().writeValueAsString(0)));
                spLocationLog.setAgentId(new BigDecimal(transactionDataObj.getSpotcash_agent_id()));
                spLocationLog.setAgentStoreId(transactionDataObj.getSpotcash_agent_store_id());
                spLocationLog.setStoreUserId(storeUsers.getStoreUserId());
                spLocationLog.setIpAddress(ipAddress);
                spLocationLog.setTransactionType(transactionType);
                crudTransactions.updateLocationLog(spLocationLog);
            }catch (Exception ex){
                LOGGER.error("Error saving Location record: {}", ex.getLocalizedMessage());
            }

            transactionDataObj.setTransactionId(instransactionId);
            LOGGER.info("TRANSACTION SEQUENCE GENERATED " + instransactionId);
            if (SpotcashTransactionService.CLIENT_AIRTIME.getServiceId().equals(transactionDataObj.getSpotcash_service_id())
                    && (!transactionDataObj.getDescription().equals(""))) {
                tranTempObj.setMsisdn(transactionDataObj.getDescription());
            } else {
                tranTempObj.setMsisdn(transactionDataObj.getCustomer_msisdn());
            }
            tranTempObj.setAmount(amountConverted);
            tranTempObj.setClientId(new BigInteger(transactionDataObj.getClient_id()));
            tranTempObj.setServiceId(new BigInteger(transactionDataObj.getSpotcash_service_id()));
            tranTempObj.setCustomerName(transactionDataObj.getCustomer_name());
            tranTempObj.setAgentId(transactionDataObj.getSpotcash_agent_id());
            tranTempObj.setAgentStoreId(transactionDataObj.getSpotcash_agent_store_id());
            tranTempObj.setAccesschannelId(BigInteger.valueOf(5));
            tranTempObj.setProcessorFailCount(0);
            tranTempObj.setFailCount(BigInteger.ZERO);
            tranTempObj.setIntermediateStatus(BigInteger.ZERO);
            tranTempObj.setTrxId(instransactionId);
            tranTempObj.setRequestTime(sqldate);
            LOGGER.info(" The organization client ID IS ### " + transactionDataObj.getClient_id());
            String client_type = crudTransactions.fetchclient_type(new BigDecimal(transactionDataObj.getClient_id())).getClientType();

            if ("VPN".equals(client_type)) {

                tranTempObj.setTrxStatus(BigInteger.ONE);

            } else {

                tranTempObj.setTrxStatus(BigInteger.ZERO);
            }

            tranTempObj.setSpotcashCommission(commission);
            tranTempObj.setThirdpartyCharge(serviceCharge);
            //tranTempObj.setAccountNumber(transactionDataObj.getAccountNumber());
            //CbsRequestData constructCbsRequestData = constructCbsRequestData(transactionDataObj, spAgent, agentStoreData, return_id, instransactionId, serviceCharge, requestData);
            tranTempObj.setAgentPhoneNumber(transactionDataObj.getAgentPhoneNumber());
            HashMap<String,String> map = new HashMap<>();
            map.put("trxId",tranTempObj.getTrxId());
            map.put("msisdn",tranTempObj.getMsisdn());
            map.put("clientId",tranTempObj.getClientId().toString());
            map.put("serviceId",tranTempObj.getServiceId().toString());
            map.put("amount",tranTempObj.getAmount().toString());
            //map.put("sourceMsisdn",tranTempObj.getSourceMsisdn()==null?"":tranTempObj.getSourceMsisdn());
            map.put("accountNumber",tranTempObj.getAccountNumber()==null?"":tranTempObj.getAccountNumber());
            //map.put("toAccount",tranTempObj.getToAccount()==null?"":tranTempObj.getToAccount());

            LOGGER.info("ABOUT TO INSERT TRANS TEMP PAYMENT OBJECT #### ");
            return_id = this.crudTransactions.insertTransTmpTable(tranTempObj);
            LOGGER.info("[TRANSACTION PROCESSORS.CREATE TRANS TEMP PAYMENT] >> " + return_id);
            //transaction chaining
            if(return_id != null) {
                LOGGER.info("[TRANSACTION PROCESSORS.CREATE TRANS TEMP PAYMENT] >> " + return_id);

                map.put("trxTableId", String.valueOf(return_id));

                String trxChain = sharedFunctions1.updateChainTrx(null, map, tranTempObj.getTrxId(), return_id.toString()); // chain null since first time creation.
                ChainedStatus = trxChain == null ? 0 : 1;
                if (ChainedStatus == 1) {
                    //trx chained successfully proceed processing.
                    SpTrxValue spTrxValue = new SpTrxValue();
                    spTrxValue.setTrxId(tranTempObj.getTrxId());
                    spTrxValue.setTvalue(trxChain);
                    crudTransactions.createtrxValue(spTrxValue);
                } else {
                    //break;
                }
            }

            response = String.valueOf(instransactionId);
            return response;
        }
        catch (Exception e){
            LOGGER.error("PROCESS REQUEST EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return response;
    }
    public Map<String, BigDecimal> getAgencyCommissionServiceChargeData(BigInteger service_id, BigInteger client_id, BigDecimal amount) {
        LOGGER.info("Get commission service data:");
        try {
            SpServiceSubscriptions service_commissionData = crudTransactions.fetchServicesubscriptionData(service_id, client_id);
            if (service_commissionData != null) {
                SpAgencyTariff agencyTariffData = crudTransactions.fetchAgencyTariffData(service_id, client_id);
                Map<String, BigDecimal> tariffDetailsData = new HashMap<>();
                if (agencyTariffData != null) {
                    List<SpAgencyTariffDetails> tariffDetail = crudTransactions.fetchAgencyTariffsDetails(agencyTariffData.getId().toString(), amount, agencyTariffData.getType());
                    if (tariffDetail != null) {
                        if (tariffDetail.size() > 0) {
                            for (SpAgencyTariffDetails spTransData : tariffDetail) {
                                tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                                tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
                            }
                        } else {
                            tariffDetailsData.put("commission", new BigDecimal("0"));
                            tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
                        }
                    }
                    return tariffDetailsData;
                }
                List<SpTariffDetails> tariffDetail = crudTransactions.fetchTariffsDetails(service_commissionData.getTariffId(), amount);
                if (tariffDetail != null) {
                    if (tariffDetail.size() > 0) {
                        for (SpTariffDetails spTransData : tariffDetail) {
                            tariffDetailsData.put("commission", spTransData.getSpotcashComission());
                            tariffDetailsData.put("serviceCharge", spTransData.getServiceCharge());
                        }
                    } else {
                        tariffDetailsData.put("commission", new BigDecimal("0"));
                        tariffDetailsData.put("serviceCharge", new BigDecimal("0"));
                    }
                }
                return tariffDetailsData;
            } else {
                LOGGER.info(" TRANSACTION PROCESSORS No tariff details information for service ID >> " + service_id + " & Client ID " + client_id);
                return null;
            }
        }
        catch (Exception e){
            LOGGER.error("COMPUTE AGENCY TARIFF EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return null;
    }
    private CbsRequestData constructCbsRequestData(SpotcashTrxDetails transactionDataObj, SpAgents spAgent, SpAgentStores agentStoreData, BigDecimal return_id, String instransactionId, BigDecimal serviceCharge, HttpProcessorRequest requestData) {
        String msisdn = agentStoreData.getContactMsisdn();
        CbsRequestData cbsRequestObj = new CbsRequestData();
        try {
            cbsRequestObj.setMsisdn(transactionDataObj.getCustomer_msisdn());
            cbsRequestObj.setTrnx_charges(transactionDataObj.getTrnx_charges());
            cbsRequestObj.setAmount(transactionDataObj.getAmount());
            cbsRequestObj.setDescription(transactionDataObj.getDescription());
            cbsRequestObj.setClientId(transactionDataObj.getClient_id());
            cbsRequestObj.setTransactionId(instransactionId != null ? instransactionId : "");
            cbsRequestObj.setService_charge(serviceCharge != null ? serviceCharge.toString() : "0");
            cbsRequestObj.setServiceId(transactionDataObj.getSpotcash_service_id());
            cbsRequestObj.setCustomer_name(transactionDataObj.getCustomer_name() != null ? transactionDataObj.getCustomer_name() : "");
            cbsRequestObj.setTranstemptableid(return_id != null ? return_id.toString() : "");
            cbsRequestObj.setAccount_number(transactionDataObj.getAccountNumber());
            cbsRequestObj.setAccountName(requestData.getRequestData().getAccName());
            cbsRequestObj.setAgent_store_code(agentStoreData.getAgentStoreCode());
            if (agentStoreData.getBranch_code() == "0" || agentStoreData.getBranch_code() == "null") {
                cbsRequestObj.setCustomerType(transactionDataObj.getCustomerType());
            } else {
                cbsRequestObj.setCustomerType(agentStoreData.getBranch_code());
            }
            if (requestData.getHeader().getUserId() != null) {
                if (Integer.parseInt(requestData.getHeader().getUserId()) > 0) {
                    cbsRequestObj.setUserId(requestData.getHeader().getUserId());
                    Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchStoreUserWithId(new BigInteger(requestData.getHeader().getUserId()),requestData.getHeader().getUserXid()));
                    if (optionalStoreUser.isPresent()) {
                        SpStoreUsers spStoreUser = optionalStoreUser.get();
                        msisdn = spStoreUser.getContactMsisdn();
                    }
                }
            }
            cbsRequestObj.setAgent_msisdn(msisdn);
            cbsRequestObj.setAgentPhoneNumber(msisdn);
            cbsRequestObj.setNewPrintDesign(spAgent.getNewPrintDesign().toString());
            cbsRequestObj.setWithdrawal_sms(spAgent.getEnable_withdrawals_sms().toString());
            LOGGER.info("CBS REQUEST OBJECT " + cbsRequestObj);
            return cbsRequestObj;
        }
        catch (Exception e){
            LOGGER.error("CONSTRUCT CBS REQUEST OBJECT EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return cbsRequestObj;
    }

    public CorebankingResponse invokecorebankingsystem(CrudTransactionController crudTransactions, CbsRequestData vpnConfigs) {
        LOGGER.info("CBSREQUESTADATA " + vpnConfigs);
        try {
            return new CorebankingSystemInvokerImpl(crudTransactions,reserveFundsConfigurations).callCBS(vpnConfigs, environment);
        }  catch (Exception ex) {
            LOGGER.error("Error invoking CBS - Exception " + ex.toString());
            ex.printStackTrace();
            return null;
        }
    }
    public String getupdateTransactionId() { //Get current transaction Id
        try {
            String transactionId = crudTransactions.fetchsetLastTransactionId();
            //After fetching, increment the id
            String newTrsanctionId = new SpotcashUtilities().nextTransactionId(transactionId);
            crudTransactions.incrementReturnTransactionId(newTrsanctionId);
            return newTrsanctionId;
        } catch (Exception e) {
            LOGGER.error("Error generating a new transaction id" + e.toString());
            e.printStackTrace();
            return null;
        }
    }
    @RequestMapping(value = "/getServiceCharge", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getServiceCharge(@RequestBody String payload, HttpServletResponse resp) throws Exception {
        LOGGER.info("GET SERVICE CHARGE REQUEST :: " + new Gson().toJson(payload));
        GetServiceChargeCbsResponse response = new GetServiceChargeCbsResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());
        ResponseEntity<GetServiceChargeCbsResponse> responseEntity = new ResponseEntity<>(response, HttpStatus.OK);
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);
        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response1 = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the dynamic secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response1), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }


//        HashMap<String, String> requestMap = new ObjectMapper().readValue(payload, HashMap.class);
        LOGGER.info("fetching service charges...");
        LOGGER.info(payload.toString());
        ApiResponse error = new ApiResponse();
        error.setSuccessStatus("01");
        error.setNarration("Invalid Response from CBS");
        ObjectMapper objectMapper = new ObjectMapper();

        // Step 1: Parse outer JSON
        Map<String, String> outerMap = objectMapper.readValue(payload, Map.class);

        // Step 2: Get the stringified inner JSON from the "data" field
        String dataJson = outerMap.get("data");

        // Step 3: Parse the inner JSON string into a map
        Map<String, String> requestMap = objectMapper.readValue(dataJson, Map.class);

        // Step 4: Map values to your request object
        ServiceChargeRequest request = new ServiceChargeRequest();
        request.setAmount(requestMap.get("amount"));
        request.setClientId(requestMap.get("clientId"));
        request.setTransactionType("58");
        request.setCustomerPhoneNumber(requestMap.get("customerPhoneNumber"));
        request.setIdNumber(requestMap.get("idNumber"));


        try{
            //Check if phoneNumber and CLientID have been provided.
            if(!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getCustomerPhoneNumber())){
                //Fetch VPN Configs
                Optional<GetServiceChargeCbsResponse> optionalGetServiceChargeResponse = Optional.ofNullable(cbsService.getServiceCharge(request));
                if (optionalGetServiceChargeResponse.isPresent()) {
                    GetServiceChargeCbsResponse cbsResponse = optionalGetServiceChargeResponse.get();

                    //Check status of the CBS Response
                    if(cbsResponse.getResponseCode().equalsIgnoreCase("00")){
                        response.setResponseMessage(cbsResponse.getResponseMessage());
                    }
                    else{
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getResponseCode())
                                ? cbsResponse.getResponseCode() : "Service  Charge not found";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setErrorMessage(errMsg);
                    }

                }
                else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setErrorMessage("Service Charge not found");
                }
            }
            else{
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setErrorMessage("Bad Request");
                responseEntity = new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
            }
        }
        catch (Exception e){
            LOGGER.error("FAILED TO FETCH REDEEMED REWARDS. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " +e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setErrorMessage("Error occurred on fetching member details.");
            responseEntity = new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        LOGGER.info("RESPONSE :: " + new Gson().toJson(response));


            return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                .body(Crypt.encrypt(new Gson().toJson(response), secretKey));

    }


    @RequestMapping(value = "/getJobGroups", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getJobGroups(@RequestBody String payload, HttpServletRequest
            httpServletRequest, HttpServletResponse resp) throws Exception {
        //Get the updated(or not) headers containing the JWTs if they had expired
        HttpHeaders responseHeaders = jwtUtil.getUpdatedTokenHeaders(resp);

        String token = resp.getHeader("WatchDog").split(" ")[1];
        String deviceId = jwtUtil.getClaim(token, StoreUser.DEVICE_ID.getValue());

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        //Tokens that contain the deviceId use a dynamic encryption Key
        if(!ObjectUtils.isEmpty(deviceId)){
            //Fetch the encryption key from DB as token which have deviceId are for APK's version 1.9+
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        // Fetch dataKey from the DB using the received token
        String secretKey;
        SpTokenKey tokenKey = crudTransactions.fetchTokenAndKeyWithToken(token);
        if(tokenKey != null && token.equals(tokenKey.getJwtToken())){ secretKey = tokenKey.getDataKey(); }
        else{
            // JWT token is invalid
            LOGGER.error("JWT token is invalid thus requires Agent to login again");
            AuthResponse response = new AuthResponse("01",
                    "Your current session has been invalidated due to app configuration changes. Please Login again.");

            //Note: The response has been encrypted using the static secretKey.
            String encryptedResponse = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey);

            return new ResponseEntity<>(encryptedResponse, HttpStatus.UNAUTHORIZED);
        }

        String agentStoreId = jwtUtil.getClaim(token, StoreUser.AGENT_STORE_ID.getValue());
        String agentId = jwtUtil.getClaim(token, StoreUser.AGENT_ID.getValue());
        if(!workingHoursService.requestIsWithinWorkingHours(agentStoreId)){
            // Transaction is outside working hours.
            LOGGER.error("Action Request is outside working hours!");
            String clientId = jwtUtil.getClaim(token, StoreUser.CLIENT_ID.getValue());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "actionOutsideWorkingHours",
                    "Sorry, the action cannot be performed as it is outside working hours");
            AuthResponse response = new AuthResponse("01", errorNarration);

            String encryptedRes = Crypt.encrypt(new ObjectMapper().writeValueAsString(response), secretKey);
            return new ResponseEntity<>(encryptedRes, HttpStatus.UNAUTHORIZED);
        }

        // Decrypt the request
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                tokenKey.getDataKey());
        GetJobGroupRequest request = new Gson().fromJson(decryptedPayload, GetJobGroupRequest.class);

        LOGGER.info("GET JOB GROUP REQUEST :: " + new Gson().toJson(request));
        GetJobGroupResponse response = new GetJobGroupResponse();
        response.setResponseCode(ApiResponseCodes.SUCCESS.getCode());

        try{
            //Check if storeUserId and ClientID have been provided.
            if(!ObjectUtils.isEmpty(request.getClientId()) && !ObjectUtils.isEmpty(request.getStoreUserId())){
                //Fetch VPN Configs
                Optional<GetJobGroupResponse> optionalJobGroups = Optional.ofNullable(cbsService.getJobGroups(request));
                if (optionalJobGroups.isPresent()) {
                    GetJobGroupResponse cbsResponse = optionalJobGroups.get();

                    //Check status of the CBS Response
                    if(cbsResponse.getResponseCode().equalsIgnoreCase("00")){
                        response.setJobGroups(cbsResponse.getJobGroups());
                        response.setResponseMessage(cbsResponse.getResponseMessage());
                    }
                    else{
                        String errMsg = !ObjectUtils.isEmpty(cbsResponse.getResponseMessage())
                                ? cbsResponse.getResponseMessage() : "Failed to fetch job groups";
                        response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                        response.setResponseMessage(errMsg);
                    }
                }
                else {
                    response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                    response.setResponseMessage("Failed to fetch job groups");
                }

                return ResponseEntity.status(HttpStatus.OK).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
            else{
                //bad request
                response.setResponseCode(ApiResponseCodes.ERROR.getCode());
                response.setResponseMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).headers(responseHeaders)
                        .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
            }
        }
        catch (Exception e){
            LOGGER.error("FAILED TO GET JOB GROUPS. REQUEST(" + new Gson().toJson(request) + "). ERROR :: " +e.getMessage());
            e.printStackTrace();
            response.setResponseCode(ApiResponseCodes.ERROR.getCode());
            response.setResponseMessage("Failed to fetch JOB GROUPS.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(responseHeaders)
                    .body(Crypt.encrypt(new Gson().toJson(response), secretKey));
        }
    }




}

