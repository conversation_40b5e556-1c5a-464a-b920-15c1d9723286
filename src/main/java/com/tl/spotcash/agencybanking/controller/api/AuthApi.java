package com.tl.spotcash.agencybanking.controller.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.tl.spotcash.agencybanking.configuration.JwtTokenFilter;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.ApiResponse;
import com.tl.spotcash.agencybanking.custommodels.ConfigurationResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.IMSIResponseBody;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.enums.ResponseCodes;
import com.tl.spotcash.agencybanking.pojo.AuthRequest;
import com.tl.spotcash.agencybanking.pojo.AuthResponse;
import com.tl.spotcash.agencybanking.pojo.EncryptedData;
import com.tl.spotcash.agencybanking.pojo.EncryptionKey;
import com.tl.spotcash.agencybanking.repository.CrudService;
import com.tl.spotcash.agencybanking.repository.GenericCrudService;
import com.tl.spotcash.agencybanking.service.*;
import com.tl.spotcash.agencybanking.utils.Crypt;
import com.tl.spotcash.agencybanking.utils.JwtTokenUtil;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;

/**
 * This file contains endpoints that can be called without any authentication required.
 */
@RestController
@RequestMapping("spotcash/auth")
public class AuthApi {
    private final AuthenticationManager authManager;
    private final CrudTransactionController crudTransactions;
    private final Environment environment;
    private final JwtTokenUtil jwtUtil;
    private final JwtTokenFilter jwtFilter;
    private final TransactionService transactionService;
    private final WorkingHoursService workingHoursService;
    private final OtpService otpService;
    private final IMSIService imsiService;
    private final GenericCrudService databaseCrudService;
    private final ErrorMessageService errorMessageService;

    SharedFunctions sharedFunctions = new SharedFunctions();

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthApi.class);

    public AuthApi(WorkingHoursService workingHoursService, AuthenticationManager authManager, CrudTransactionController crudTransactions, Environment environment, JwtTokenUtil jwtUtil, JwtTokenFilter jwtFilter, TransactionService transactionService, OtpService otpService, IMSIService imsiService, GenericCrudService databaseCrudService, ErrorMessageService errorMessageService) {
        this.workingHoursService = workingHoursService;
        this.authManager = authManager;
        this.crudTransactions = crudTransactions;
        this.environment = environment;
        this.jwtUtil = jwtUtil;
        this.jwtFilter = jwtFilter;
        this.transactionService = transactionService;
        this.otpService = otpService;
        this.imsiService = imsiService;
        this.databaseCrudService = databaseCrudService;
        this.errorMessageService = errorMessageService;
    }

    /**
     * Endpoint for fetching device configuration params using client ID.
     */
    @PostMapping(value = "/deviceConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> fetchDeviceConfiguration(@RequestBody String payload) throws Exception {
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);

        String encryptionKey = "";
        HashMap<String, String> requestMap = new HashMap<>();
        if(!ObjectUtils.isEmpty(encryptedRequest.getDeviceId()) && !ObjectUtils.isEmpty(encryptedRequest.getClientId())){
            String deviceId = encryptedRequest.getDeviceId();
            String clientId = encryptedRequest.getClientId();

            //Fetch the encryption key from DB
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }
        else{
            //Support older APK which use a hardcoded encryption Key in the APP
            String encryptedPayload = encryptedRequest.getData();
            String decryptedPayload = Crypt.decrypt(
                    encryptedPayload.replace("\n", ""),
                    environment.getRequiredProperty("app.secretKey"));
            requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        }

        String clientId = !requestMap.isEmpty() ? requestMap.get("clientId") : encryptedRequest.getClientId();
        //String serialNo = requestMap.get("serialNo");
        LOGGER.error("GETTING DEVICE CONFIGURATION ---------------- " + clientId);
        ApiResponse error = new ApiResponse();
        error.setMessage("Device Configuration does not Exist!");
        error.setNarration("Invalid Device.");
        ConfigurationResponseMapper configurationResponseMapper = new ConfigurationResponseMapper();

        if(encryptionKey.isEmpty()) encryptionKey = environment.getRequiredProperty("app.secretKey");

        SpAgents spAgent;
        List<SpAgentAuthentication> agentAuthenticationList;
        try {
            Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(clientId)));

            if (optionalAgent.isPresent()) {
                spAgent = optionalAgent.get();
                SpClients spClient = crudTransactions.fetchclient_type(new BigDecimal(spAgent.getClientId()));
                String clientName = spClient.getClientName();
                spAgent.setAgentName(clientName);
                configurationResponseMapper.setAgent(spAgent);
                configurationResponseMapper.setClient(spClient);

                SpVpnclientsConfigs vpnclientsConfigs = crudTransactions.fetch_vpn_configs(spAgent.getClientId());
                spAgent.setMobileWebservice(vpnclientsConfigs.getUseMobileBanking() != null
                        && BigInteger.ONE.equals(BigInteger.valueOf(vpnclientsConfigs.getUseMobileBanking())));

                agentAuthenticationList = crudTransactions.fetchAgentAuthenticationData(spAgent.getAgentId().toBigInteger());
                if (agentAuthenticationList != null) {
                    configurationResponseMapper.setAuthenticationList(agentAuthenticationList);
                }
                LOGGER.info("Device configs successfully fetched.");
                //LOGGER.info(new Gson().toJson(configurationResponseMapper));

                return new ResponseEntity<>(Crypt.encrypt(
                        new ObjectMapper().writeValueAsString(configurationResponseMapper),
                        encryptionKey), HttpStatus.OK);
            }
            else {
                LOGGER.error(" ---------------- AGENT CONFIGURATION MISSING ---------------- ");
                String errorNarration = errorMessageService.getErrorMessage(
                        clientId, "missingConfigurations",
                        "Invalid Agent");
                error.setMessage("Agent Configuration does not Exist!");
                error.setNarration(errorNarration);
                return new ResponseEntity<>(Crypt.encrypt(
                        new ObjectMapper().writeValueAsString(error),
                        encryptionKey), HttpStatus.EXPECTATION_FAILED);
            }
        }
        catch (Exception e) {
            LOGGER.error("DEVICE CONFIGURATION EXCEPTION CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(configurationResponseMapper),
                encryptionKey), HttpStatus.OK);
    }

    @PostMapping(value = "/agentLogin", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> authenticateAgent(@RequestBody @Valid String payload, HttpServletRequest request) throws Exception {
        AuthResponse response = new AuthResponse();

        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);
        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        boolean includeDeviceId = false;

        if(!ObjectUtils.isEmpty(encryptedRequest.getDeviceId()) && !ObjectUtils.isEmpty(encryptedRequest.getClientId())){
            //Fetch the encryption key from DB
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    encryptedRequest.getDeviceId(), encryptedRequest.getClientId());
            if(spAgencyEncryptionKey != null){
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
                includeDeviceId = true;
            }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }


        String encryptedPayload = encryptedRequest.getData(); //new JSONObject(payload).getString("data");
        String requestParams = Crypt.decrypt(encryptedPayload.replace("\n", ""),
                encryptionKey);
        HashMap<String, String> requestMap = new ObjectMapper().readValue(requestParams, HashMap.class);
        //Decrypt msisdn and password from the payload
        String decodedMsisdn = requestMap.get("msisdn");
        String decodedPassword = requestMap.get("password");
        String decodedUniqueID = requestMap.get("uniqueId");
        String deviceId = requestMap.get("deviceId");
        String clientId = requestMap.get("clientId");
        Float appVersion = null;
        try {
            if(!ObjectUtils.isEmpty(requestMap.get("appVersion"))) appVersion = Float.parseFloat(requestMap.get("appVersion"));
        }
        catch (Exception e) {e.printStackTrace();}

        try {
            if (decodedMsisdn != null && decodedPassword != null) {
                UsernamePasswordAuthenticationToken userAuthentication =
                        new UsernamePasswordAuthenticationToken(
                                decodedMsisdn, decodedPassword
                        );

                Authentication authentication = authManager.authenticate(userAuthentication);
                SpStoreUsers storeUser = (SpStoreUsers) authentication.getPrincipal();
                response.setAgentStoreUsers(storeUser);

                // checks if the agent belongs to the correct client
                String retrievedClient = String.valueOf(storeUser.getClientId()).trim();
                if(!retrievedClient.equals(clientId.trim())){
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration("Invalid Agent!");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    encryptionKey));
                }

                //Reset the pin retry count to 0 after successful authentication
                crudTransactions.resetStoreUserPinRetry(decodedMsisdn);

                //Gets the agentId using the agentStoreId and verifies if the agentStore and agent are active.
                // If not, restrict access.
                String agentId = crudTransactions.fetchAgentIdForAuthentication(String.valueOf(storeUser.getAgentStoreId()));

                //checks if the agent is blacklisted
                if (crudTransactions.isAgentBlacklisted(Long.parseLong(storeUser.getStoreUserId().toString()))) {
                    LOGGER.info("STORE USER WITH ID :: {}, I BLACKLISTED!", storeUser.getStoreUserId());
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());

                    String errorNarration = errorMessageService.getErrorMessage(
                            clientId, "agentBlacklisted",
                            "Dear Agent, your account is locked, please contact sacco for activation!");
                    response.setErrorNarration(errorNarration);

                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    encryptionKey));
                }

                Optional<SpAgentStores> optionalAgentStore = Optional.ofNullable(
                        crudTransactions.fetchAgentWithStoreId(storeUser.getAgentStoreId()));
                SpAgentStores spAgentStore;
                if (optionalAgentStore.isPresent()) {
                    spAgentStore = optionalAgentStore.get();

                    if (spAgentStore.getActiveStatus().equals(new BigInteger(String.valueOf(0)))){
                        LOGGER.info("LOGIN FAILED: AGENT STORE ACCOUNT WITH ID {} IS DEACTIVATED ", spAgentStore.getAgentStoreId());
                        String errorNarration = errorMessageService.getErrorMessage(
                                clientId, "agentDeactivated",
                                "Dear Agent, your account is deactivated, please contact sacco for activation");
                        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                        response.setErrorNarration(errorNarration);
                        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                        encryptionKey));
                    }

                    if (crudTransactions.fetchforceUpdateStatus(spAgentStore.getClientId()).equals("1")) {
                        Float currentVersion = transactionService.getCurrentVersion(agentId);
                        if(ObjectUtils.isEmpty(appVersion) || (!ObjectUtils.isEmpty(currentVersion) && Float.compare(appVersion, currentVersion) < 0)){
                            crudTransactions.sendSmswithLink(spAgentStore.getClientId(), storeUser.getContactMsisdn());
                            LOGGER.error(" ---------------- AGENT STORE APP VERSION DEPRECATED ---------------- ");
                            String errorNarration = errorMessageService.getErrorMessage(
                                    clientId, "appVersionDeprecated",
                                    "Agent app you're using is of a lower version, Kindly check Sms with link to download the new app!");

                            response.setSuccessStatus(ResponseCodes.SUCCESS.getCode());
                            response.setErrorNarration(errorNarration);
                            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                                    .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                            encryptionKey));
                        }
                        else {
                            if (spAgentStore.getDeviceId().equals("0")) {
                                LOGGER.info("**********************First time onboarding agent. Saving new device to db");
                                crudTransactions.saveNewDeviceId(spAgentStore.getAgentStoreId(), deviceId);
                                spAgentStore.setContactMsisdn(storeUser.getContactMsisdn());
                                spAgentStore.setContactName(storeUser.getContactName());
                                response.setAgentStores(spAgentStore);
                            }
                            else {
                                if (spAgentStore.getDeviceId().equals(deviceId)) {
                                    LOGGER.info(" ---------------- AGENT STORE DEVICE ID VERIFIED SUCCESSFULLY ---------------- ");

                                    //Reset the deactivation count to 0 after successful check
                                    crudTransactions.resetStoreUserDeactivationCount(decodedMsisdn);

                                    spAgentStore.setContactMsisdn(storeUser.getContactMsisdn());
                                    spAgentStore.setContactName(storeUser.getContactName());
                                    response.setAgentStores(spAgentStore);
                                }
                                else {
                                    LOGGER.error(" ---------------- INVALID AGENT DEVICE: {} ---------------- ", deviceId);

                                    //Get the count
                                    SpAgents spAgent = crudTransactions.fetchAgentData(BigInteger.valueOf(Long.parseLong(clientId)));
                                    int deactivationCount = spAgent != null ?  spAgent.getDeactivationCount() : 0;
                                    String spAgentId = spAgent != null ? String.valueOf(spAgent.getAgentId())  : "0";

                                    if(deactivationCount > 0){
                                        //check if the storeUser/Agent has exceeded the count
                                        int retryCount = storeUser.getDeactivationCount();

                                        //update store user deactivation count
                                        crudTransactions.updateStoreUserDeactivationCount(decodedMsisdn,
                                                String.valueOf(++retryCount));

                                        if(retryCount >= deactivationCount){
                                            //Deactivate the store user
                                            crudTransactions.deactivatedStoreUser(decodedMsisdn);
                                            SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
                                            spAgentDeactivations.setDateCreated(new Date(System.currentTimeMillis()));
                                            spAgentDeactivations.setAgentId(BigInteger.valueOf(Long.parseLong(spAgentId)));
                                            spAgentDeactivations.setClientId(BigInteger.valueOf(Long.parseLong(clientId)));
                                            spAgentDeactivations.setDeactivationType("Store User Deactivation");
                                            spAgentDeactivations.setStatus("Pending");
                                            spAgentDeactivations.setStoreUserId(storeUser.getStoreUserId());
                                            spAgentDeactivations.setDeactivationReason("Deactivated for using an invalid device.");
                                            spAgentDeactivations.setAgentStoreId(storeUser.getAgentStoreId());
                                            databaseCrudService.save(spAgentDeactivations);

                                            //Reset the deactivation count to 0
                                            crudTransactions.resetStoreUserDeactivationCount(decodedMsisdn);

                                            String errorNarration = errorMessageService.getErrorMessage(
                                                    clientId, "invalidDeviceDeactivation",
                                                    "Your account has been deactivated for using the wrong device!" +
                                                            " Contact sacco for activation");

                                            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                                            response.setErrorNarration(errorNarration);

                                            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                                                    Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                                            encryptionKey));
                                        }
                                    }

                                    String errorNarration = errorMessageService.getErrorMessage(
                                            clientId, "invalidDevice",
                                            "Dear Agent, we have noticed a change in device. " +
                                                    "Please use the correct device to prevent your account from being deactivated.");
                                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                                    response.setErrorNarration(errorNarration);
                                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                                    encryptionKey));
                                }
                            }
                        }
                    }
                    else {
                        if (spAgentStore.getDeviceId().equals("0")) {
                            LOGGER.info("**********************First time onboarding agent. Saving new device to db");
                            crudTransactions.saveNewDeviceId(spAgentStore.getAgentStoreId(), deviceId);
                            spAgentStore.setContactMsisdn(storeUser.getContactMsisdn());
                            spAgentStore.setContactName(storeUser.getContactName());
                            response.setAgentStores(spAgentStore);
                        }
                        else {
                            if (spAgentStore.getDeviceId().equals(deviceId)) {
                                LOGGER.info(" ---------------- AGENT STORE DEVICE ID VERIFIED SUCCESSFULLY ---------------- ");

                                //Reset the deactivation count to 0 after successful check
                                crudTransactions.resetStoreUserDeactivationCount(decodedMsisdn);

                                spAgentStore.setContactMsisdn(storeUser.getContactMsisdn());
                                spAgentStore.setContactName(storeUser.getContactName());
                                response.setAgentStores(spAgentStore);
                            }
                            else {
                                LOGGER.error(" ---------------- INVALID AGENT DEVICE: {} ---------------- ", deviceId);

                                //Get the count
                                SpAgents spAgent = crudTransactions.fetchAgentData(BigInteger.valueOf(Long.parseLong(clientId)));
                                int deactivationCount = spAgent != null ?  spAgent.getDeactivationCount() : 0;
                                String spAgentId = spAgent != null ? String.valueOf(spAgent.getAgentId())  : "0";

                                if(deactivationCount > 0){
                                    //check if the storeUser/Agent has exceeded the count
                                    int retryCount = storeUser.getDeactivationCount();

                                    //update store user deactivation count
                                    crudTransactions.updateStoreUserDeactivationCount(decodedMsisdn,
                                            String.valueOf(++retryCount));

                                    if(retryCount >= deactivationCount){
                                        //Deactivate the store user
                                        crudTransactions.deactivatedStoreUser(decodedMsisdn);
                                        SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
                                        spAgentDeactivations.setDateCreated(new Date(System.currentTimeMillis()));
                                        spAgentDeactivations.setAgentId(BigInteger.valueOf(Long.parseLong(spAgentId)));
                                        spAgentDeactivations.setClientId(BigInteger.valueOf(Long.parseLong(clientId)));
                                        spAgentDeactivations.setDeactivationType("Store User Deactivation");
                                        spAgentDeactivations.setStatus("Pending");
                                        spAgentDeactivations.setStoreUserId(storeUser.getStoreUserId());
                                        spAgentDeactivations.setDeactivationReason("Deactivated for using an invalid device.");
                                        spAgentDeactivations.setAgentStoreId(storeUser.getAgentStoreId());
                                        databaseCrudService.save(spAgentDeactivations);

                                        //Reset the deactivation count to 0
                                        crudTransactions.resetStoreUserDeactivationCount(decodedMsisdn);

                                        String errorNarration = errorMessageService.getErrorMessage(
                                                clientId, "invalidDeviceDeactivation",
                                                "Your account has been deactivated for using the wrong device!" +
                                                        " Contact sacco for activation");

                                        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                                        response.setErrorNarration(errorNarration);

                                        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                                                Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                                        encryptionKey));
                                    }
                                }

                                String errorNarration = errorMessageService.getErrorMessage(
                                        clientId, "invalidDevice",
                                        "Dear Agent, we have noticed a change in device. " +
                                                "Please use the correct device to prevent your account from being deactivated.");
                                response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                                response.setErrorNarration(errorNarration);
                                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                                        .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                                encryptionKey));
                            }
                        }
                    }
                }
                else {
                    LOGGER.error(" ---------------- AGENT STORE CONFIGURATION MISSING ---------------- ");
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    String errorNarration = errorMessageService.getErrorMessage(
                            clientId, "missingConfigurations",
                            "Invalid Agent!");
                    response.setErrorNarration(errorNarration);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    encryptionKey));
                }

                BigInteger id = spAgentStore.getAgentId();
                if (crudTransactions.fetchAgentWithId(id.toString()).getFence().equals(BigInteger.ONE)
                        && spAgentStore.getAgentType() == 1) {
                    String centralLocationJson = spAgentStore.getCentralLocation();
                    JSONObject centralLocation = new JSONObject(centralLocationJson);
                    double latitude = Double.parseDouble(centralLocation.getString("latitude"));
                    double longitude = Double.parseDouble(centralLocation.getString("longitude"));
                    if (!transactionService.withinLocation(Double.parseDouble(requestMap.get("latitude")), Double.parseDouble(requestMap.get("longitude")), latitude, longitude, spAgentStore.getStoreRadius())) {
                        LOGGER.error(" ---------------- AGENT IS OUTSIDE WORKING AREA ---------------- ");
                        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                        String errorNarration = errorMessageService.getErrorMessage(
                                clientId, "outsideWorkingArea",
                                "OUTSIDE WORKING AREA");
                        response.setErrorNarration(errorNarration);
                        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                                .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                        encryptionKey));
                    }
                }


                String storeId = String.valueOf(storeUser.getAgentStoreId());
                boolean requireOTP = false;

                if (agentId == null) {
                    //Return Error Response
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration("Invalid Agent!");

                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                    encryptionKey));
                }

                if (transactionService.checkAgentAllowsDeviceCapture(agentId)) {
                    requireOTP = transactionService.checkUniqueDeviceId(storeId, decodedUniqueID);
                }
                else requireOTP = transactionService.isOtpOnLoginRequired(agentId);

                String workingHours = workingHoursService.fetchAgentWorkingHours(new BigInteger(storeId));
                if ((!workingHoursService.checkRequestIsWithinWorkingHours(new BigInteger(storeId), workingHours))) {
                    String errorNarration = errorMessageService.getErrorMessage(
                            clientId, "outsideWorkingHours",
                            "You cannot login outside working hours!");
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration(errorNarration);
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
                }

                //Check if agent allows imsi-check and if true return imsi-check-interval
                int imsiInterval = imsiService.checkAgentAllowsImsiCheck(agentId);
                if (imsiInterval > 0) {
                    int daysPastImsiCheck = imsiService.daysSinceLastImsiCheck(decodedMsisdn);
                    if (daysPastImsiCheck >= imsiInterval) {
                        //Make api call to imsi check and return true if sim swap happened
                        if (imsiService.isSimSwapped(decodedMsisdn, storeUser.getClientId(), spAgentStore.getAgentId().toString(), storeUser.getStoreUserId().toString(), storeId)) {
                            crudTransactions.deactivatedStoreUser(decodedMsisdn);
                            LOGGER.info("Deactivated store user: {}", storeUser);
                            String errorNarration = errorMessageService.getErrorMessage(
                                    clientId, "simSwap",
                                    "Your agent account has been DEACTIVATED due to a recent sim swap");
                            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                            response.setErrorNarration(errorNarration);

                            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
                        }
                        //No record in IMSI table, create one
                    } else if (daysPastImsiCheck == -1) { // First time calling imsiCheck
                        LOGGER.info("First time creating the agentStoreUser, calling IMSI check.");
                        imsiService.isSimSwapped(decodedMsisdn, storeUser.getClientId(), spAgentStore.getAgentId().toString(), storeUser.getStoreUserId().toString(), storeId);
                    }
                }
                if (requireOTP) {
                    response.setRequireOTP(requireOTP);
                    return ResponseEntity.ok().body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
                }
                String sec = jwtUtil.generateRandomKey(16);//transactionService.generateRandomString();

                String accessToken = jwtUtil.generateAccessToken(storeUser, agentId, sec, encryptionKey, includeDeviceId);
                String refreshToken = jwtUtil.generateRefreshToken(storeUser, agentId);
                //LOGGER.info("Random String++++++++++++++:   " + sec);
                HttpHeaders headers = new HttpHeaders();
                try {
                    SpTokenKey spTokenKey = new SpTokenKey();
                    spTokenKey.setStoreUserId(storeUser.getStoreUserId());
                    spTokenKey.setJwtToken(accessToken);
                    spTokenKey.setDataKey(sec);
                    spTokenKey.setValidToken(BigInteger.ONE);
                    spTokenKey.setRequestTime(new Date(System.currentTimeMillis()));
                    crudTransactions.saveUpdateTokenAndKey(spTokenKey);

                    response.setSuccessStatus(ResponseCodes.SUCCESS.getCode());

                    headers.add("WatchDog", "Bearer " + accessToken);
                    headers.add("Refresh-Token", "Bearer " + refreshToken);
                }
                catch (Exception ex) {
                    LOGGER.error("Error Validating token and key: {}", ex.getLocalizedMessage());
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration("");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
                }
                LOGGER.info("Create the record in the SpLocationLog for Geo Mapping");
                try {
                    Gson gson = new Gson();
                    String locality =  requestMap.get("locality") != null ? requestMap.get("locality").split(",")[0] : null;
                    HashMap<String, String> latLong = new LinkedHashMap<>();
                    latLong.put("Longitude", requestMap.get("longitude"));
                    latLong.put("Latitude", requestMap.get("latitude"));

                    HashMap<String, String> location = new HashMap<>();
                    location.put(locality, new ObjectMapper().writeValueAsString(latLong));
                    String ipAddress = SharedFunctions.getRemoteIpAddress(request, environment);
                    SpLocationLog spLocationLog = new SpLocationLog();
                    spLocationLog.setRequestTime(new Date(System.currentTimeMillis()));
                    spLocationLog.setClientId(storeUser.getClientId());
                    spLocationLog.setLocation((new ObjectMapper().writeValueAsString(latLong)));
                    spLocationLog.setAgentId(new BigDecimal(agentId));
                    spLocationLog.setAgentStoreId(new BigInteger(storeId));
                    spLocationLog.setStoreUserId(storeUser.getStoreUserId());
                    spLocationLog.setIpAddress(ipAddress);
                    spLocationLog.setTransactionType("Login");
                    crudTransactions.updateLocationLog(spLocationLog);
                }
                catch (Exception ex) {
                    LOGGER.error("Error saving Location record: {}", ex.getLocalizedMessage());
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration("");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
                }
                return ResponseEntity.ok().headers(headers).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
            }
            else {
                String errorNarration = errorMessageService.getErrorMessage(
                        clientId, "authenticationFailure",
                        "Sorry, Authentication Failed!");
                response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                response.setErrorNarration(errorNarration);
            }

        }
        catch (BadCredentialsException ex) {
            //Handles an InvalidPin
            LOGGER.error("ERrr: {}", ex.getLocalizedMessage());

            //Check if agent has exceeded the pin retry count
            //Fetch the configured retry count
            SpStoreUsers storeUser = crudTransactions.fetchStoreUserWithPhoneNumber(decodedMsisdn);
            int pinRetries = storeUser != null ? storeUser.getPinRetries() : 0;
            BigInteger storeUserId = storeUser != null ? storeUser.getStoreUserId() : BigInteger.ZERO;

            SpAgents spAgent = crudTransactions.fetchAgentData(BigInteger.valueOf(Long.parseLong(clientId)));
            int retryCount = spAgent != null ?  spAgent.getLockCount().intValue() : 3;
            String spAgentId = spAgent != null ? String.valueOf(spAgent.getAgentId())  : "0";

            //update store user pin retries
            crudTransactions.updateStoreUserPinRetry(decodedMsisdn, String.valueOf(++pinRetries));

            if(pinRetries >= retryCount){
                //Deactivate the store user
                crudTransactions.deactivatedStoreUser(decodedMsisdn);
                SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
                spAgentDeactivations.setDateCreated(new Date(System.currentTimeMillis()));
                spAgentDeactivations.setAgentId(BigInteger.valueOf(Long.parseLong(spAgentId)));
                spAgentDeactivations.setClientId(BigInteger.valueOf(Long.parseLong(clientId)));
                spAgentDeactivations.setDeactivationType("Store User Deactivation");
                spAgentDeactivations.setStatus("Pending");
                spAgentDeactivations.setStoreUserId(storeUserId);
                spAgentDeactivations.setDeactivationReason("Deactivated after exceeding PIN retries.");
                spAgentDeactivations.setAgentStoreId(storeUser.getAgentStoreId());
                databaseCrudService.save(spAgentDeactivations);
                LOGGER.info("Deactivated store user(Exceeded Pin retries): " +
                                "msisdn :: {}, clientId :: {}, pinCount :: {}, storeUser pin retries :: {}",
                        decodedMsisdn, clientId, pinRetries, retryCount);

                //Reset the retry count to 0
                crudTransactions.resetStoreUserPinRetry(decodedMsisdn);

                response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                response.setErrorNarration("Pin retries exceeded. Your account has been deactivated!");

                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                        Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                                encryptionKey));

            }

            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "invalidCredentials",
                    "Invalid Phone number or Pin!");
            response.setSuccessStatus(ResponseCodes.INCORRECT_PIN.getCode());
            response.setErrorNarration(errorNarration);

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(response),
                    encryptionKey));
        }
        catch (InternalAuthenticationServiceException e) {
            //Handles an invalid phoneNumber
            LOGGER.error("USER AUTHENTICATION: {}", e.getLocalizedMessage());
            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
            response.setErrorNarration(e.getMessage());

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(
                    new ObjectMapper().writeValueAsString(response),
                    encryptionKey));
        }
        catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Error During login: {}", ex.getLocalizedMessage());
            String errorNarration = errorMessageService.getErrorMessage(
                    clientId, "authenticationFailure",
                    "Sorry, Authentication Failed!");
            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
            response.setErrorNarration(errorNarration);
        }

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                        encryptionKey));
    }

    @RequestMapping(value = "/initiateAgentOTP", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> initiateLoginOtp(@RequestBody String payload) throws Exception {
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);

        String encryptionKey = "";
        if(!ObjectUtils.isEmpty(encryptedRequest.getDeviceId()) && !ObjectUtils.isEmpty(encryptedRequest.getClientId())){
            //Fetch the encryption key from DB
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    encryptedRequest.getDeviceId(), encryptedRequest.getClientId());
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }
        if(encryptionKey.isEmpty()) encryptionKey = environment.getRequiredProperty("app.secretKey");

        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(
                encryptedPayload.replace("\n", ""),
                encryptionKey);
        HashMap<String, String> requestMap = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        String clientId = requestMap.get("clientId");
        String msisdn = requestMap.get("msisdn");
        //String agentStoreId = requestMap.get("agentStoreId");
        LOGGER.info("initiateLoginOTP/{msisdn}/{clientId}/{storeUserId}");
        String smsUrl = environment.getRequiredProperty("datasource.spotcash.smsSendingUrl");
        boolean otpSend = false;

        AuthResponse response = new AuthResponse();
        response.setOtpResponse(otpSend);

        try {
            Optional<SpAgents> optionalAgent = Optional.ofNullable(crudTransactions.fetchAgentData(new BigInteger(clientId)));
            Optional<SpStoreUsers> optionalStoreUser = Optional.ofNullable(crudTransactions.fetchStoreUserWithMsIdn(msisdn));
            if (optionalAgent.isPresent()) {
                SpAgents spAgent = optionalAgent.get();
                if (spAgent.getSmsUrl() != null) {
                    if (!spAgent.getSmsUrl().equals("")) {
                        smsUrl = spAgent.getSmsUrl();
                    }
                }
            }                       // Generate the OTP
            if (optionalStoreUser.isPresent()) {
                SpStoreUsers storeUser = optionalStoreUser.get();
                int otp = otpService.generateOTP(msisdn, clientId);
                crudTransactions.createOtpLog(msisdn, String.valueOf(storeUser.getStoreUserId()), clientId, String.valueOf(otp));
                if (sharedFunctions.validPhoneNumber(msisdn)) {
                    String msg = "" + smsUrl + "&dest=" + msisdn + "&msg=" + URLEncoder.encode("Agency Banking OTP: " + otp, "UTF-8");
                    LOGGER.info(msg);

                    if (sharedFunctions.makeGetRequest(msg).contains("Success")) {

                        LOGGER.info("SMS Successfully");
                    } else {
                        LOGGER.info("SMS Sending Failed");
                    }
                    otpSend = true;
                    //return otpSend;
                    response.setOtpResponse(otpSend);
                    return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey), HttpStatus.OK);
                } else {
                    LOGGER.info("Invalid Phone: " + msisdn + " for Incoming OTP Message.");
                    //return otpSend;
                    return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey), HttpStatus.OK);
                }
            } else {
                LOGGER.info("SpStoreUser with agentStoreID not Found."); //Todo:
                //return otpSend;
                return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey), HttpStatus.OK);
            }


        } catch (Exception e) {
            LOGGER.error("EXCEPTION GENERATING AGENT OTP CAUGHT {}", e.getMessage(), e);
            e.printStackTrace();
        }
        LOGGER.info(String.valueOf(otpSend));
        return new ResponseEntity<>(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey), HttpStatus.OK);
    }

    @RequestMapping(value = "/validateAgentOTP", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> validateAgentOTP(@RequestBody String payload) throws Exception {
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);

        String encryptionKey = environment.getRequiredProperty("app.secretKey");
        boolean includeDeviceId = false;
        if(!ObjectUtils.isEmpty(encryptedRequest.getDeviceId()) && !ObjectUtils.isEmpty(encryptedRequest.getClientId())){
            //Fetch the encryption key from DB
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    encryptedRequest.getDeviceId(), encryptedRequest.getClientId());
            if(spAgencyEncryptionKey != null){
                encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
                includeDeviceId = true;
            }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }

        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(encryptedPayload.replace("\n", ""), encryptionKey);
        HashMap<String, String> request = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        //get agentStoreId, agentId, msisdn, otp
        //Add Agent and agentStore
        boolean isValid = transactionService.validateAgentOTP(request);
        AuthResponse response = new AuthResponse();
        SpStoreUsers spStoreUser = null;
        SpAgentStores spAgentStore = null;
        if (isValid) {
            try {
                Optional<SpStoreUsers> optionalSpStoreUser = Optional.ofNullable(crudTransactions.fetchStoreUserWithMsIdn(request.get("msisdn")));

                BigInteger asid = BigInteger.ZERO;
                if (optionalSpStoreUser.isPresent()) asid = optionalSpStoreUser.get().getAgentStoreId();

                Optional<SpAgentStores> optionalSpAgentStores = Optional.ofNullable(crudTransactions.fetchAgentWithStoreId(asid));

                if (optionalSpStoreUser.isPresent() && optionalSpAgentStores.isPresent()) {
                    spStoreUser = optionalSpStoreUser.get();
                    spAgentStore = optionalSpAgentStores.get();

                    spAgentStore.setContactMsisdn(spStoreUser.getContactMsisdn());
                    spAgentStore.setContactName(spStoreUser.getContactName());
                } else {
                    LOGGER.error(" ---------------- AGENT STORE CONFIGURATION MISSING ---------------- ");
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    //response.setErrorNarration("Invalid Agent!");
                    response.setErrorNarration("OTP Validation Error!");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                            encryptionKey));
                }
                String sec = jwtUtil.generateRandomKey(16);//transactionService.generateRandomString();

                String accessToken = jwtUtil.generateAccessToken(spStoreUser, String.valueOf(spAgentStore.getAgentId()),
                        sec, encryptionKey, includeDeviceId);
                LOGGER.info("Random String++++++++++++++:   " + sec);
                SpTokenKey spTokenKey = new SpTokenKey();
                spTokenKey.setStoreUserId(spStoreUser.getStoreUserId());
                spTokenKey.setJwtToken(accessToken);
                spTokenKey.setDataKey(sec);
                spTokenKey.setRequestTime(new Date(System.currentTimeMillis()));
                crudTransactions.saveUpdateTokenAndKey(spTokenKey);

                response.setSuccessStatus(ResponseCodes.SUCCESS.getCode());
                response.setAgentStores(spAgentStore);
                response.setAgentStoreUsers(spStoreUser);
                HttpHeaders headers = new HttpHeaders();
                headers.add("WatchDog", "Bearer " + accessToken);

                crudTransactions.updateAppUniqueId(String.valueOf(spStoreUser.getAgentStoreId()), request.get("uniqueId"));

                return ResponseEntity.ok().headers(headers).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                        encryptionKey));
            } catch (Exception ex) {
                response.setErrorNarration("");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                        encryptionKey));
            }

        } else {
            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
            response.setErrorNarration("OTP Validation Error!");

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response),
                    encryptionKey));
        }

    }


    @Deprecated
    @RequestMapping(value = "/refreshToken", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> generateRefreshToken(HttpServletRequest request) throws Exception {
        LOGGER.info("INITIALIZATION FOR GENERATING A NEW TOKEN USING A REFRESH TOKEN.");
        AuthResponse response = new AuthResponse();

        try {
            // Verify the Refresh Token. If it passes, generate a new Token.
            if (jwtFilter.hasRefreshTokenBearer(request)) { //checks if it has the 'Refresh-Token' Header.
                    String refreshToken = jwtFilter.getRefreshToken(request); // Gets the refresh Token

                if (jwtUtil.validateAccessToken(refreshToken)) { // Validate the Token.

                    UserDetails userDetails = jwtFilter.getUserDetails(refreshToken, "");

                    UsernamePasswordAuthenticationToken
                            authentication = new UsernamePasswordAuthenticationToken(userDetails, null, null);

                    authentication.setDetails(
                            new WebAuthenticationDetailsSource().buildDetails(request));

                    SpStoreUsers storeUser = (SpStoreUsers) authentication.getPrincipal();

                    //Gets the agentId using the agentStoreId and verifies if the agentStore and agent are active.
                    // If not, restrict access.
                    String agentId = crudTransactions.fetchAgentIdForAuthentication(String.valueOf(storeUser.getAgentStoreId()));

                    if (agentId == null) {
                        LOGGER.info("AGENT WITH AGENT_STORE_ID :: {} NOT FOUND. THUS SESSION EXPIRED.", storeUser.getAgentStoreId());
                        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                        response.setErrorNarration("Current Session Has Expired! You need to Login again to proceed.");
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
                    }

                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    String sec = jwtUtil.generateRandomKey(16);//transactionService.generateRandomString();
                    // Create a new access Token
                    String accessToken = jwtUtil.generateAccessToken(storeUser, agentId, sec, "" , false);
                    response.setSuccessStatus(ResponseCodes.SUCCESS.getCode());

                    LOGGER.info("Random String++++++++++++++:   " + sec);
                    SpTokenKey spTokenKey = new SpTokenKey();
                    spTokenKey.setStoreUserId(storeUser.getStoreUserId());
                    spTokenKey.setJwtToken(accessToken);
                    spTokenKey.setDataKey(sec);
                    spTokenKey.setRequestTime(new Date(System.currentTimeMillis()));
                    crudTransactions.saveUpdateTokenAndKey(spTokenKey);

                    HttpHeaders headers = new HttpHeaders();
                    headers.add("WatchDog", "Bearer " + accessToken);

                    return ResponseEntity.ok().headers(headers).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), environment.getRequiredProperty("app.secretKey")));
                } else {
                    //Refresh Token is invalid
                    LOGGER.info("REFRESH TOKEN IS INVALID :: {} ", refreshToken);
                    response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                    response.setErrorNarration("Current Session Has Expired! You need to Login again to proceed.");

                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), environment.getRequiredProperty("app.secretKey")));
                }
            } else {
                // A bad request was received
                response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
                response.setErrorNarration("Sorry, Authentication Failed!");
            }
        } catch (UsernameNotFoundException | BadCredentialsException ex) {
            ex.printStackTrace();
            LOGGER.info("USER AUTHENTICATION ERROR");
            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
            response.setErrorNarration("Current Session Has Expired! You need to Login again to proceed.");

            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), environment.getRequiredProperty("app.secretKey")));
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.info("AN ERROR GENERATING REFRESH TOKEN: {}", ex.getLocalizedMessage());
            response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
            response.setErrorNarration("Current Session Has Expired! You need to Login again to proceed.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), environment.getRequiredProperty("app.secretKey")));
        }

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), environment.getRequiredProperty("app.secretKey")));
    }

    @RequestMapping(value = "/deactivateStoreUser", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> deactivateStoreUser(@RequestBody String payload) throws Exception {
        EncryptedData encryptedRequest = new Gson().fromJson(payload, EncryptedData.class);

        String encryptionKey = "";
        if(!ObjectUtils.isEmpty(encryptedRequest.getDeviceId()) && !ObjectUtils.isEmpty(encryptedRequest.getClientId())){
            //Fetch the encryption key from DB
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    encryptedRequest.getDeviceId(), encryptedRequest.getClientId());
            if(spAgencyEncryptionKey != null){ encryptionKey = spAgencyEncryptionKey.getEncryptionKey(); }
            else{ return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED); }
        }
        if(encryptionKey.isEmpty()) encryptionKey = environment.getRequiredProperty("app.secretKey");

        String encryptedPayload = encryptedRequest.getData();
        String decryptedPayload = Crypt.decrypt(encryptedPayload.replace("\n", ""), encryptionKey);
        HashMap<String, String> request = new ObjectMapper().readValue(decryptedPayload, HashMap.class);
        AuthResponse response = new AuthResponse();
        String clientId = request.get("clientId");
        String msisdn = request.get("msisdn");
        crudTransactions.deactivatedStoreUser(msisdn);
        SpStoreUsers storeUsers = crudTransactions.fetchStoreUserWithPhoneNumber(msisdn);
        SpAgentDeactivations spAgentDeactivations = new SpAgentDeactivations();
        spAgentDeactivations.setDateCreated(new Date(System.currentTimeMillis()));
        spAgentDeactivations.setAgentId(new BigInteger(request.get("agentId")));
        spAgentDeactivations.setClientId(storeUsers.getClientId());
        spAgentDeactivations.setDeactivationType("Store User Deactivation");
        spAgentDeactivations.setStatus("Pending");
        spAgentDeactivations.setStoreUserId(storeUsers.getStoreUserId());
        spAgentDeactivations.setDeactivationReason("Deactivated after exceeding PIN retries.");
        spAgentDeactivations.setAgentStoreId(storeUsers.getAgentStoreId());
        databaseCrudService.save(spAgentDeactivations);
        LOGGER.info("Deactivated store user: msisdn{}, clientId{}", msisdn, clientId);
        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
        response.setErrorNarration("Pin retries exceeded. Your account has been deactivated!");

        return ResponseEntity.status(HttpStatus.OK).body(Crypt.encrypt(new ObjectMapper().writeValueAsString(response), encryptionKey));
    }

    @PostMapping(value = "/getKey", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EncryptionKey> getKey(@RequestBody EncryptionKey encryptionKeyReq, HttpServletRequest servletRequest){
        String deviceId = encryptionKeyReq.getDeviceId();
        String clientId = encryptionKeyReq.getClientId();
        ResponseEntity<EncryptionKey> responseEntity = new ResponseEntity<>(encryptionKeyReq, HttpStatus.OK);

        try{
            if(!ObjectUtils.isEmpty(deviceId) && !ObjectUtils.isEmpty(clientId)){
                //Step 1: generate a random Key
                String encryptionKey = jwtUtil.generateRandomKey(16);

                //Step 2: Save the key in DB
                SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
                if(spAgencyEncryptionKey != null){
                    //Update the encryptionKey, ipAddress and Date created
                    spAgencyEncryptionKey.setEncryptionKey(encryptionKey);
                    spAgencyEncryptionKey.setDateCreated(new Date());
                    spAgencyEncryptionKey.setIpAddress(SharedFunctions.getRemoteIpAddress(servletRequest, environment));
                }
                else{
                    //Create a new Record
                    spAgencyEncryptionKey = new SpAgencyEncryptionKey();
                    spAgencyEncryptionKey.setClientId(BigInteger.valueOf(Long.parseLong(clientId)));
                    spAgencyEncryptionKey.setDeviceId(deviceId);
                    spAgencyEncryptionKey.setIpAddress(SharedFunctions.getRemoteIpAddress(servletRequest, environment));
                    spAgencyEncryptionKey.setEncryptionKey(encryptionKey);
                    spAgencyEncryptionKey.setDateCreated(new Date());
                }

                crudTransactions.save(spAgencyEncryptionKey);
                encryptionKeyReq.setKey(encryptionKey);
            }
            else { responseEntity = new ResponseEntity<>(encryptionKeyReq, HttpStatus.BAD_REQUEST);}
        }
        catch (Exception e){
            LOGGER.error("FAILED TO GENERATE AN ENCRYPTION KEY FOR REQUEST BODY :: {}", encryptionKeyReq);
            responseEntity = new ResponseEntity<>(encryptionKeyReq, HttpStatus.INTERNAL_SERVER_ERROR);
        }

        return responseEntity;
    }

}

