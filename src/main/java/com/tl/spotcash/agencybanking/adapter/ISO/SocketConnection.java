package com.tl.spotcash.agencybanking.adapter.ISO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 *
 * <AUTHOR>
 */
public class SocketConnection {

    private static final Logger LOGGER = LoggerFactory.getLogger(SocketConnection.class);
    private static Socket socket;

    /**
     *
     * @param host
     * @param port
     * @param isoMessage
     * @return
     */
    public String j8583isoserverMessage(String host, int port, String isoMessage) {
        try {

            InetAddress address = InetAddress.getByName(host);
            socket = new Socket();
            socket.connect(new InetSocketAddress(address, port), 10000);

            //Send the message to the server
            OutputStream os = socket.getOutputStream();
            OutputStreamWriter osw = new OutputStreamWriter(os);
            BufferedWriter bw = new BufferedWriter(osw);

            String sendMessage = isoMessage + "\n";
            bw.write(sendMessage);
            bw.flush();
            LOGGER.info("MESSAGE SENT TO THE SERVER : " + sendMessage);
            //Receive response
            String iso_msg = null;
            byte[] lenbuf = new byte[2];

            socket.getInputStream().read(lenbuf);
            int size = socket.getInputStream().available();

            byte[] buf = new byte[size];
            if (socket.getInputStream().read(buf) == size) {
                iso_msg = new String(buf);
                iso_msg = iso_msg.replace("\n", "");
                LOGGER.info("RECEIVED message ISO RESPONSE FROM SERVER :" + iso_msg);
            }
            return iso_msg;
        }
        catch (NumberFormatException | IOException ex) {
            LOGGER.error("SOMETHING WENT WRONG WHILE TRYING TO GET TO THE CBS " + ex.getMessage());
            return null;
        }
    }
}
