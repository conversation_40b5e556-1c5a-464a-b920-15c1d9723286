package com.tl.spotcash.agencybanking.adapter;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposIntegratorResponse;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposRequestData;
import com.tl.spotcash.agencybanking.exceptions.OzoneAuthenticationException;

public interface ZposIntegratorInvoker {
	ZposIntegratorResponse zposIntegratorResponse(CrudTransactionController crudTransactionController,
			ZposRequestData zposRequestData) throws OzoneAuthenticationException;
}
