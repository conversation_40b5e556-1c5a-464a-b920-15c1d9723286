package com.tl.spotcash.agencybanking.adapter.ISO;

import bsh.StringUtil;
import com.tl.spotcash.agencybanking.adapter.ISO.models.ParentServiceMapper;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.IsoResponseMapper;
import com.tl.spotcash.agencybanking.entity.SpTransResponseTable;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;

/**
 *
 * <AUTHOR>
 */
public class IsoResponseProcessor {

    private CrudTransactionController crudTransactions;
    private CbsRequestData vpnConfigs;

    private static final Logger LOGGER = LoggerFactory.getLogger(IsoResponseProcessor.class);

    public IsoResponseProcessor(CrudTransactionController crudTransactions, CbsRequestData vpnConfigs) {
        this.crudTransactions = crudTransactions;
        this.vpnConfigs = vpnConfigs;
    }

    /**
     *
     * @param respoMapper
     * @param isomapData
     * @return
     */
    public CorebankingResponse processISOmapData(ParentServiceMapper respoMapper, IsoResponseMapper isomapData) {
        DecimalFormat myFormatter = new DecimalFormat("###,###.00");
        CorebankingResponse corebankingresponse = new CorebankingResponse();

        String update_id = null;
        String transacreponseMsg = null;
        String errorMessage = null;

        SpTransTempTable transatempObj = new SpTransTempTable();

        corebankingresponse.setCorebankingResponseCode(isomapData.getResponse_code());
        corebankingresponse.setCorebankingResponseMessage(isomapData.getDesc());
        corebankingresponse.setSpotcash_transaction_id(isomapData.getOriginalTrxId());

        transatempObj.setId(new BigDecimal(isomapData.getTransTempTableId()));
        transatempObj.setMsisdn(isomapData.getMsisdn());
        transatempObj.setOriginalTxnId(isomapData.getOriginalTrxId());
        transatempObj.setRespCode(isomapData.getResponse_code());

        if ("00".equals(isomapData.getResponse_code())) {
            corebankingresponse.setPOSresponsecode("0000");
            transatempObj.setDescription("Transaction processed Successfully");
        } else {
            corebankingresponse.setPOSresponsecode("9942");
            switch (isomapData.getResponse_code()) {
                case "01":
                    corebankingresponse.setCorebankingResponseDescription("Member funds insufficient");
                    break;
                case "02":
                case "03":
                    corebankingresponse.setCorebankingResponseDescription("Inactive Member account");
                    break;
                case "06":
                    corebankingresponse.setCorebankingResponseDescription("System error");
                    break;

                case "11":
                    corebankingresponse.setCorebankingResponseDescription("Amount less than minimum allowed");
                    break;
                case "12":
                    corebankingresponse.setCorebankingResponseDescription("Amount higher than maximum allowed");
                    break;
                case "13":
                    corebankingresponse.setCorebankingResponseDescription("Maximum daily amount exceeded");
                    break;
                case "15":
                    corebankingresponse.setCorebankingResponseDescription("Transaction expired");
                    break;
                case "51":
                case "051":
                    corebankingresponse.setCorebankingResponseDescription("Account will fall below minimum balance");
                    break;
                default:
                    corebankingresponse.setCorebankingResponseDescription("Transaction failed to complete");
                    break;
            }
            transatempObj.setDescription(corebankingresponse.getCorebankingResponseDescription());
            errorMessage = corebankingresponse.getCorebankingResponseDescription();
        }

        switch (isomapData.getProcessing_code()) {
            case "560000":
            case "570000":
                break;
            case "580000":
                LOGGER.info("Balance enquiry message here ");
                LOGGER.info("Balance CR/DR " + respoMapper.get58000().getCreditDebit().getUpperlimit());
                LOGGER.info("Balance actual " + respoMapper.get58000().getCreditDebitActual().getUpperlimit());
                LOGGER.info("Balance enquiry message here ");
                if ("00".equals(isomapData.getResponse_code())) {
                    String currency = (isomapData.getBal()).substring(Integer.parseInt(respoMapper.get58000().getCurrency().getUpperlimit()), Integer.parseInt(respoMapper.get58000().getCurrency().getLowerlimit()));
                    String parsesavingbal = (isomapData.getBal()).substring(Integer.parseInt(respoMapper.get58000().getAvailableBalance().getUpperlimit()), Integer.parseInt(respoMapper.get58000().getAvailableBalance().getLowerlimit()));

                    LOGGER.info("currency " + currency);
                    LOGGER.info("parsesavingbal " + parsesavingbal);

                    String credDeb = (isomapData.getBal()).substring(Integer.parseInt(respoMapper.get58000().getCreditDebit().getUpperlimit()), Integer.parseInt(respoMapper.get58000().getCreditDebit().getLowerlimit()));
                    double savingbal = Double.parseDouble(parsesavingbal);
                    savingbal = savingbal / 100;

                    LOGGER.info("credDeb " + credDeb);
                    LOGGER.info("savingbal " + savingbal);

                    String parseActualBal = null;
                    parseActualBal = (isomapData.getBal()).substring(Integer.parseInt(respoMapper.get58000().getActualBalance().getUpperlimit()), Integer.parseInt(respoMapper.get58000().getActualBalance().getLowerlimit()));
                    double actualBal = Double.parseDouble(parseActualBal);
                    actualBal = actualBal / 100;

                    LOGGER.info("parseActualBal " + parseActualBal);
                    LOGGER.info("actualBal " + actualBal);

                    String credDebActual = (isomapData.getBal()).substring(Integer.parseInt(respoMapper.get58000().getCreditDebitActual().getUpperlimit()), Integer.parseInt(respoMapper.get58000().getCreditDebitActual().getLowerlimit()));
                    credDebActual = credDebActual.equalsIgnoreCase("D") ? "dr" : "cr";
                    if (credDeb.equalsIgnoreCase("cr")) {
                        savingbal = savingbal + 0;
                    } else if (credDeb.equalsIgnoreCase("dr")) {
                        savingbal = 0 - savingbal;
                    }
                    transacreponseMsg = "Dear Customer, your available balance is " + currency + ". " + myFormatter.format(actualBal) + " " + credDeb + ", Actual Bal " + currency + ". " + myFormatter.format(savingbal) + " " + credDebActual + "";
                    transatempObj.setCustBal(BigDecimal.valueOf(actualBal));
                }
                break;
            case "000000":
                break;
            case "370000":
            case "380000":
                if ("00".equals(isomapData.getResponse_code())) {
                    String msgstmt = "Mini Stmt:\n";
                    String[] trnx = (isomapData.getMsg()).split("~");
                    for (int a = 0; a < trnx.length; a++) {
                        String trnx_details = trnx[a];
                        String str = trnx_details;
                        String date = "", description = "";
                        Double amntStament = 0.0;
                        String[] temp = str.split("\\|");
                        for (int i = 0; i < temp.length; i++) {
                            try {
                                date = temp[0].substring(Integer.parseInt(respoMapper.get37000().getFirststm().getLowerlimit()), Integer.parseInt(respoMapper.get37000().getFirststm().getUpperlimit()))
                                        + "-" + temp[0].substring(Integer.parseInt(respoMapper.get37000().getSecondstm().getLowerlimit()), Integer.parseInt(respoMapper.get37000().getSecondstm().getUpperlimit()))
                                        + "-" + temp[0].substring(Integer.parseInt(respoMapper.get37000().getThirdstm().getLowerlimit()), Integer.parseInt(respoMapper.get37000().getSecondstm().getUpperlimit()));
                                description = temp[1];
                                String amntStament1 = temp[2];
                                amntStament = Double.parseDouble(amntStament1);
                                amntStament = amntStament / 1;
                            }
                            catch (Exception e) {
                                LOGGER.error("THERE WAS AN ERROR LOOPING ");
                            }
                        }
                        msgstmt += "" + date + " " + description + " KES:" + amntStament + "\n ";
                        transacreponseMsg = msgstmt;
                        transatempObj.setCustBal(null);
                    }
                }
                break;
            case "610000":
            case "620000":
                break;
            default:
                break;
        }
        LOGGER.info("BIGDECIMAL TRANS TEMP ID IS >>>" + this.vpnConfigs.getTranstemptableid());
        LOGGER.info("SWITCHADAPTER.processISOmapData() UPDATING TRANSTEMP TABLE>>");
        transatempObj.setTrxStatus(BigInteger.valueOf(2));
        update_id = crudTransactions.updateTransTempTable(transatempObj);

        corebankingresponse.setSpotcash_transaction_status(update_id);
        String[] splitarray = StringUtil.split(update_id, "|");

        LOGGER.info("TRANSTEMUPDATE " + update_id);
        if (Integer.parseInt(splitarray[0]) > 0) {
            if ("00".equals(transatempObj.getRespCode()) && ("580000".equals(isomapData.getProcessing_code()) || "380000".equals(isomapData.getProcessing_code()
            ) || "370000".equals(isomapData.getProcessing_code()))) {
                LOGGER.info("Transaction response message # \n " + transacreponseMsg);

                SpTransResponseTable msgObject = new SpTransResponseTable();
                msgObject.setMessage(transacreponseMsg);
                msgObject.setTrxId(transatempObj.getOriginalTxnId());
                crudTransactions.insertAgencyMsgs(msgObject);
                LOGGER.info("INSERTED ALERT MESSAGE TO RESPONSE TABLE");
            } else {
                SpTransResponseTable msgObject = new SpTransResponseTable();
                msgObject.setMessage(errorMessage);
                msgObject.setTrxId(transatempObj.getOriginalTxnId());
                crudTransactions.insertAgencyMsgs(msgObject);
            }
        } else {
            LOGGER.warn("TRX WAS NOT SUCCESSFULL");
        }
        return corebankingresponse;
    }
}
