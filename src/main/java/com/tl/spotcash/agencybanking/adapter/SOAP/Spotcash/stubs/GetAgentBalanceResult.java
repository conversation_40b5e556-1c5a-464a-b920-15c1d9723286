
package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import javax.xml.bind.annotation.*;
import java.math.BigDecimal;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="return_value" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="agentPhoneNo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="responseMessage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "returnValue",
        "agentPhoneNo",
        "responseMessage"
})
@XmlRootElement(name = "GetAgentBalance_Result")
public class GetAgentBalanceResult {

    @XmlElement(name = "return_value", required = true)
    protected BigDecimal returnValue;
    @XmlElement(required = true)
    protected String agentPhoneNo;
    @XmlElement(required = true)
    protected String responseMessage;

    /**
     * Gets the value of the returnValue property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getReturnValue() {
        return returnValue;
    }

    /**
     * Sets the value of the returnValue property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setReturnValue(BigDecimal value) {
        this.returnValue = value;
    }

    /**
     * Gets the value of the agentPhoneNo property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAgentPhoneNo() {
        return agentPhoneNo;
    }

    /**
     * Sets the value of the agentPhoneNo property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAgentPhoneNo(String value) {
        this.agentPhoneNo = value;
    }

    /**
     * Gets the value of the responseMessage property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getResponseMessage() {
        return responseMessage;
    }

    /**
     * Sets the value of the responseMessage property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setResponseMessage(String value) {
        this.responseMessage = value;
    }

}