package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.AgencyBanking;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.AgencyBankingPort;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.MobileBanking;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.MobileBankingPort;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.CbsResponseData;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.entity.SpXmlTemplates;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;

import javax.xml.ws.BindingProvider;
import javax.xml.ws.Holder;
import javax.xml.ws.soap.SOAPFaultException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.Authenticator;
import java.text.SimpleDateFormat;
import java.util.*;

public class MobileWebserviceStubsImpl {

	private static final Logger logger = LoggerFactory.getLogger(MobileWebserviceStubsImpl.class);

	private Environment environment;

	BigInteger clientId;

	CrudTransactionController crudTransactionController;

	SharedFunctions sharedFunctions = new SharedFunctions();

	Boolean useXmlTemplates = false;

	private String userName;
	private String password;
	private String host;
	private String cbsUrl;

	public MobileWebserviceStubsImpl(String userName, String password, String host, Environment environment) {
		this.userName = userName;
		this.password = password;
		this.host = host;
		this.environment = environment;
	}

	public MobileWebserviceStubsImpl(String userName, String password, String host, Environment environment,
                                     CrudTransactionController crudTransactionController, String cbsUrl,
                                     BigInteger clientId) {
		this.userName = userName;
		this.password = password;
		this.host = host;
		this.environment = environment;
		this.crudTransactionController = crudTransactionController;
		this.cbsUrl= cbsUrl;
		this.clientId = clientId;
	}

	public void authenticate_to_corebanking() {
		NtlmAuthenticator authenticator = new NtlmAuthenticator(userName, password);
		Authenticator.setDefault(authenticator);
	}

	/**
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public CbsResponseData spotcash_cbs_call(CbsRequestData requestData) throws Exception {
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		CbsResponseData cbsresponse = new CbsResponseData();
		Holder<String> requestId = new Holder<>(requestData.getTransactionId());
		cbsresponse.setRequestId(requestId.value);
		Holder<String> phoneNo = new Holder<>(requestData.getMsisdn());
		Holder<Integer> transactionType = new Holder<>(Integer.parseInt(requestData.getMobileServiceId()));
		Holder<String> amount = new Holder<>(requestData.getAmount());
		//Holder<String> agentPhoneNo = new Holder<>(requestData.getAgent_msisdn());
		Holder<String> trnx_charges = new Holder<>(requestData.getTrnx_charges());
		Holder<String> accountNumber = new Holder<>(requestData.getAccount_number());// NPM...
		String customerType = requestData.getCustomerType();
		String description = !ObjectUtils.isEmpty(requestData.getDescription()) ? requestData.getDescription() : " ";
		//String fkeyString = requestData.getTransactionId();
		/*if(requestData.getUserId() != null){
			if(!requestData.getUserId().equals(""))
				fkeyString = requestData.getUserId();
		}*/

		/**
		 * These fields are not mandatory, but will hold return values, perfect
		 * example of mutable types in Java See
		 * <link>https://docs.oracle.com/javaee/7/api/javax/xml/ws/Holder.html</link>
		 */
		Holder<String> crAccount = new Holder<>();
		if (requestData.getCr_account() != null) {
		crAccount = new Holder<>(!ObjectUtils.isEmpty(requestData.getCr_account()) ? requestData.getCr_account(): "");


		}else {
			crAccount = new Holder<>(!ObjectUtils.isEmpty(requestData.getDestinationAccount()) ? requestData.getDestinationAccount(): "");

		}
		Holder<String> status = new Holder<>("");
		Holder<String> fKey = new Holder<>(!ObjectUtils.isEmpty(requestData.getFKey()) ? requestData.getFKey() : "");
		Holder<String> balance = new Holder<>("0");
		Holder<String> message = new Holder<>("");
		Holder<String> response = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> startDate = new Holder<>(requestData.getStartDate() != null ? requestData.getStartDate() : "");
		Holder<String> endDate = new Holder<>(requestData.getEndDate() != null ? requestData.getEndDate() : "");
		Holder<String> startTime = new Holder<>("");
		Holder<String> endTime = new Holder<>("");
		Holder<String> emailaddress = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		logger.info("data to be posted to the cbs");
		logger.info(requestId.value+" , "+ phoneNo.value+" , "+transactionType.value+" , "+amount.value+" , "+trnx_charges.value+" , "+accountNumber.value+" , "+crAccount.value+" , "+status.value+" , "+fKey.value+" , "+balance.value+" , "+message.value+" , "+response.value+" , "+responseMessage.value+" , "+customerType+" , "+description);

		logger.info("Attempt for call to CBS for transaction "+requestId.value);
		//portType.spotcash(requestId, phoneNo, transactionType, amount, trnx_charges, accountNumber, crAccount, status, fKey, balance, message, response, responseMessage, agentPhoneNo, customerType,description);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction "+requestId.value);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					ObjectMapper mapper = new ObjectMapper();
					Map<String, String> requestMap = mapper.convertValue(requestData, Map.class);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("Spotcash", this.clientId);
					logger.info("RECORD ID: {}, CLIENT_ID:{}, FUNCTION_NAME: {}", spXmlTemplates.getId(),
							spXmlTemplates.getClientId(), spXmlTemplates.getFunctionName());
					//logger.info("XML_TEMPLATE:{}", spXmlTemplates.getRequestXml());

					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), new HashMap<>(requestMap));
					logger.info("Request to CBS for Spotcash call "+request);
					String response1 = sharedFunctions.postToCbs("Spotcash", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for Spotcash call "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					response.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
				}
				else {
					portType.spotcash(requestId, phoneNo, transactionType, amount, trnx_charges, accountNumber, crAccount,
							status, fKey, balance, message, response, responseMessage, customerType,
							description, startDate, endDate, startTime, endTime, emailaddress);
				}
				break;
			} catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					Thread.sleep(sleepTime);
				} else {
					break;
				}
			}
		}

		cbsresponse.setBalance(balance.value);
		cbsresponse.setTransactionType(transactionType.value.toString());
		cbsresponse.setStatus(status.value);
		cbsresponse.setResponse(response.value);
		cbsresponse.setResponseMessage(responseMessage.value);
		cbsresponse.setAgentPhoneNo(requestData.getAgentPhoneNumber());
		cbsresponse.setPhoneNo(phoneNo.value);
		cbsresponse.setAccountNumber(accountNumber.value);
		cbsresponse.setAmount(amount.value);
		return cbsresponse;
	}

	public FetchImageResponseMapper fetch_member_image_call(String msisdn) {
		logger.info("fetch member image with phoneNo {} ", msisdn);
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		FetchImageResponseMapper fetchImageResponse = new FetchImageResponseMapper();

		Holder<String> phoneNo = new Holder<>(msisdn);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host);

		logger.info("Attempt for call to CBS for transaction, fetching member details with phoneNo: " +msisdn);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, fetching member image with phoneNo: "+msisdn);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("phoneNo", msisdn);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetMemberDetails", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for GetMemberDetails Image call "+request);
					String response1 = sharedFunctions.postToCbs("GetMemberDetails", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from Cbs for GetMemberDetails call "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
				}
				else {
					portType.getMemberDetails(phoneNo, responseCode, responseMessage);
				}
				break;
			}
			catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to fetch member image");
			fetchImageResponse.setResponseCode(responseCode.value);
			//fetchImageResponse.setResponseMessage(responseMessage.value);
			logger.info("fetch member image response {} ", responseCode.value.trim());
			//logger.info("fetch member image response message {} ", errorMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode())
					|| responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetch member image.......");
				ObjectMapper mapper = new ObjectMapper();
				MobileMemberDetails mobileMemberDetails = mapper.readValue(responseMessage.value.trim(), MobileMemberDetails.class);

				Member member = new Member();
				member.setMemberImage(mobileMemberDetails.getMember().getMemberImage());
				member.setMemberNo(mobileMemberDetails.getMember().getMemberNo());
				member.setNationalId(mobileMemberDetails.getMember().getNationalId());
				member.setPhoneNo(mobileMemberDetails.getMember().getPhoneNo());
				member.setName(mobileMemberDetails.getMember().getName());
				String memberImg = mobileMemberDetails.getMember().getMemberImage();
				member.setMemberImage(memberImg != null ? memberImg : "");
				member.setMobileWebservice(true);

				MemberDetails memberDetails  = new MemberDetails();
				memberDetails.setMember(member);
				fetchImageResponse.setMemberDetails(memberDetails);
				fetchImageResponse.setResponseMessage(new Gson().toJson(memberDetails));
			}
			else {
				logger.info("Error fetch");
				fetchImageResponse.setErrorString(responseMessage.value);
			}
		} catch (Exception e) {
			fetchImageResponse.setErrorString("Invalid CBS Error");
			logger.warn("Exception thrown {} ", e);
		}
		return fetchImageResponse;
	}

	public FetchImageResponseMapper spotcash_validate_accounts_call(String accountNumberString, String identifier) {
		logger.info("validate account with no {} ", accountNumberString);
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		ValidateAccountResponseMapper validateResponse = new ValidateAccountResponseMapper();
		FetchImageResponseMapper fetchImageResponse = new FetchImageResponseMapper();
		identifier = !ObjectUtils.isEmpty(identifier) ? identifier : "idNumber";

		Holder<String> accountNumber = new Holder<>(accountNumberString);
		Holder<String> identifierHolder = new Holder<>(identifier);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host);

		logger.info("Attempt for call to CBS for transaction validating accounts with account number"+accountNumberString);
		//portType.validateAccountDetails(accountNumber, responseCode, responseMessage, errorMessage);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction validating accounts with account number"+accountNumberString);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE)==0?true:false;
				if (useXmlTemplates) {
					ObjectMapper mapper = new ObjectMapper();
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("account_number", accountNumberString);
					hashMap.put("identifier", identifier);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ValidateAccountDetailsWithImageAMust", this.clientId);
					String request3 = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Validate Account details call "+request3);
					String response3 = sharedFunctions.postToCbs("ValidateAccountDetailsWithImageAMust", request3, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for ValidateAccountDetailsWithImageAMust call "+response3);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseCode");
					errorMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "errorMessage");
				}
				else {
					portType.validateAccountDetailsWithImageAMust(accountNumber, responseCode, responseMessage, errorMessage);
				}
				break;
			}
			catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to verify account");
			logger.info("response code ::: "+responseCode.value);
			validateResponse.setResponseCode(responseCode.value);
			fetchImageResponse.setResponseCode(responseCode.value);
			validateResponse.setResponseMessage(responseMessage.value);
			logger.info("verify account response {} ", responseCode.value.trim());
			logger.info("verify account response message {} ", errorMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success verify account");
				ObjectMapper mapper = new ObjectMapper();
				mapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
				validateResponse.setAccountDetails(mapper.readValue(responseMessage.value, AccountDetails.class));

				Member member = new Member();
				String idNumber = validateResponse.getAccountDetails().getIDNo();

				member.setMemberImage(validateResponse.getAccountDetails().getMemberImage());
				member.setMemberNo(validateResponse.getAccountDetails().getCustomerId());
				member.setNationalId(!ObjectUtils.isEmpty(idNumber) ? idNumber : validateResponse.getAccountDetails().getCustomerId());
				member.setPhoneNo(validateResponse.getAccountDetails().getMobileNo());
				member.setName(validateResponse.getAccountDetails().getOwnerName());
				String memberImg = validateResponse.getAccountDetails().getMemberImage();
				member.setMemberImage(memberImg != null ? memberImg : "");
				member.setMobileWebservice(true);

				MemberDetails memberDetails  = new MemberDetails();
				memberDetails.setMember(member);
				fetchImageResponse.setMemberDetails(memberDetails);
				fetchImageResponse.setResponseMessage(new Gson().toJson(memberDetails));
			}
			else {
				logger.info("Error fetch");
				validateResponse.setErrorString(errorMessage.value);
				fetchImageResponse.setErrorString(errorMessage.value);
			}
		} catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
		}
		return fetchImageResponse;
	}

	public ValidateAccountResponseMapper spotcash_validate_account(String accountNumberString) {
		logger.info("validate account with no {} ", accountNumberString);
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		ValidateAccountResponseMapper validateResponse = new ValidateAccountResponseMapper();

		Holder<String> accountNumber = new Holder<>(accountNumberString);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host);

		logger.info("Attempt for call to CBS for transaction validating accounts with account number"+accountNumberString);
		//portType.validateAccountDetails(accountNumber, responseCode, responseMessage, errorMessage);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction validating accounts with account number"+accountNumberString);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE)==0?true:false;
				if (useXmlTemplates) {
					ObjectMapper mapper = new ObjectMapper();
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("account_number", accountNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ValidateAccountDetails", this.clientId);
					String request3 = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Validate Account details call "+request3);
					String response3 = sharedFunctions.postToCbs("ValidateAccountDetails", request3, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for ValidateAccountDetails call "+response3);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseCode");
				}
				else {
					portType.validateAccountDetails(accountNumber, responseCode, responseMessage, errorMessage);
				}
				break;
			} catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to verify account");
			logger.info("response code ::: "+responseCode.value);
			validateResponse.setResponseCode(responseCode.value);
			validateResponse.setResponseMessage(responseMessage.value);
			logger.info("verify account response {} ", responseCode.value.trim());
			logger.info("verify account response message {} ", errorMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success verify account");
				//todo.. cbs not conforming. response codes and the response messages
				ObjectMapper mapper = new ObjectMapper();
				mapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
				validateResponse.setAccountDetails(mapper.readValue(responseMessage.value, AccountDetails.class));
			} else {
				logger.info("Error fetch");
				validateResponse.setErrorString(errorMessage.value);
			}
		} catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
		}
		return validateResponse;
	}

	public CbsAccountsResponseMapper spotcash_cbs_accounts_call(String msisdn) {
		logger.info("(MOBILE)sp call accounts with msisdn {} ", msisdn);
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		CbsAccountsResponseMapper cbsresponse = new CbsAccountsResponseMapper();

		Holder<String> mobilePhoneNo = new Holder<>(msisdn);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>();

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		logger.info("Attempt for call to CBS for transaction of fetching accounts with msisdn: "+msisdn);

		//Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction of fetching accounts with msisdn: "+msisdn);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetMemberAccounts", this.clientId);
					ObjectMapper mapper = new ObjectMapper();
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("mobilePhoneNo", msisdn);

					String request3 = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for GetMemberAccounts call "+request3);
					String response3 = sharedFunctions.postToCbs("GetMemberAccounts", request3, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for GetMemberAccounts call "+response3);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseCode");
				}
				else {
					portType.getSavingsAccounts(mobilePhoneNo, responseCode, responseMessage, errorMessage, returnValue);
				}
				break;
			}
			catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to fetch accounts");
			cbsresponse.setResponseCode(responseCode.value);
			logger.info("fetch accounts response {} ", responseCode.value.trim());
			logger.info("fetch accounts response message {} ", errorMessage.value.trim());
			logger.info("fetch accounts response message {} ", responseMessage.value);
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetch");
				ObjectMapper mapper = new ObjectMapper();
				cbsresponse.setAccounts(mapper.readValue(responseMessage.value, CustomerAccounts.class));
			} else {
				logger.info("Error fetch");
				cbsresponse.setErrorString(errorMessage.value);
			}
		} catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
		}
		return cbsresponse;
	}

	public ReversalResponseMapper reverse_cbs_transaction(String transactionId) {
		logger.info("reverse mobile transaction trx id: "+ transactionId);

		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		ReversalResponseMapper cbsresponse = new ReversalResponseMapper();

		Holder<String> requestID = new Holder<>(transactionId);
		//Holder<String> refNo = new Holder<>(trxId);
		//Holder<String> phoneNo = new Holder<>(originalTransaction.getMsisdn());
		//Holder<String> transactionType = new Holder<>(originalTransaction.getServiceId().toString());
		//Holder<String> agentPhoneNo = new Holder<>(trx.getAgentPhoneNumber());
		//Holder<String> accountNumber = new Holder<>(trx.getAccountNumber());

		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		logger.info("Attempt for call to CBS for transaction: "+transactionId);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		logger.info("attempting to reverse transaction. trxId: {}", transactionId);
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction: "+transactionId);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE)==0?true:false;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("requestID", transactionId);
					/*hashMap.put("msisdn", originalTransaction.getMsisdn());
					hashMap.put("reference", originalTransaction.getTrxId());
					hashMap.put("serviceId", originalTransaction.getServiceId().toString());
					hashMap.put("account_number", trx.getAccountNumber());
					hashMap.put("agentPhoneNumber", trx.getAgentPhoneNumber());*/

					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ProcessReversal", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Process Reversal call "+request);
					String response1 = sharedFunctions.postToCbs("ProcessReversal", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for Process Reversal "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
				}
				else {
					portType.processReversal(requestID, responseCode, responseMessage, errorMessage);
				}
				break;
			} catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			cbsresponse.setResponseCode(responseCode.value);
			cbsresponse.setTransactionId(requestID.value);
			logger.info("reverse transaction response {} ", responseCode.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success reverse transaction");
			} else {
				logger.error("failed to reverse mobile cbs transaction. trx_id: {} ", transactionId);
				logger.error("reverse transaction error response message {} ", responseMessage.value.trim());
				cbsresponse.setResponseMessage(responseMessage.value);
			}
		} catch (Exception e) {
			logger.error("failed to reverse mobile cbs transaction!! trx_id: {} ", transactionId);
			logger.error("reverse transaction response {} ", responseCode.value.trim());
			logger.error("Exception thrown {} ", e);
		}
		return cbsresponse;
	}

	public CbsTransactionsResponseMapper spotcash_cbs_transactions_call(
			String phoneNumber, /*String accountNumber,*/ String startDate, String endDate, String startTime, String endTime) {
		CbsTransactionsResponseMapper cbsresponse = new CbsTransactionsResponseMapper();

		try {
			CbsRequestData cbsRequestData = new CbsRequestData();
			String requestId = UUID.randomUUID().toString().replace("-", "").substring(0, 20);
			cbsRequestData.setTransactionId(requestId);
			cbsRequestData.setStartDate(convertDateFormat(startDate));
			cbsRequestData.setEndDate(convertDateFormat(endDate));
			cbsRequestData.setMsisdn(phoneNumber);
			cbsRequestData.setStartTime(startTime);
			cbsRequestData.setEndTime(endTime);
			cbsRequestData.setMobileServiceId("13");
			cbsRequestData.setCr_account("");
			cbsRequestData.setFKey(requestId);
			cbsRequestData.setAmount("0");
			cbsRequestData.setTrnx_charges("0");
			cbsRequestData.setCustomerType("");

			CbsResponseData res =  spotcash_cbs_call(cbsRequestData);
			if ((ResponseCodes.SUCCESS.getResponseCode().equals(res.getResponse())
					|| ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(res.getResponse()))
					&& res.getResponseMessage() != null && !res.getResponseMessage().trim().isEmpty()) {
				logger.info("Transactions Success fetch");
				ObjectMapper mapper = new ObjectMapper();
				String jsonResponse = (res.getResponseMessage()).replace("\\", "").replace("?","").replace("},]","}]");
				JSONObject jsonObject = new JSONObject(jsonResponse);
				TransactionsReport transactionsReport = new TransactionsReport();
				List<Transaction> transactions  = new ArrayList<Transaction>();
				JSONArray transList = (JSONArray) jsonObject.get("Transactions");
				for(int i = 0; i < transList.length(); i ++){
					JSONObject jObject = (JSONObject) transList.get(i);
					Transaction trans  = new Transaction();
					trans.setCustomerPhoneNo(jObject.get("CustomerPhoneNo").toString());
					trans.setAmount(jObject.get("Amount").toString());
					trans.setDescription(jObject.get("Description").toString());
					trans.setPostingDate(jObject.get("PostingDate").toString());
					trans.setTransactionID(jObject.get("TransactionID").toString());
					trans.setCustomerNo(jObject.get("CustomerNo").toString());
					trans.setCustomerName(jObject.get("CustomerName").toString());
					trans.setPostingTime(jObject.get("PostingTime").toString());
					transactions.add(trans);
				}
				transactionsReport.setTransactions(transactions);
				cbsresponse.setTransactions(transactionsReport);
				cbsresponse.setErrorString("no error");
				cbsresponse.setResponseCode("000");
			}
			else {
				logger.info("Null Transactions fetch");
				ObjectMapper mapper = new ObjectMapper();
				cbsresponse.setResponseCode("xxx");
				cbsresponse.setErrorString("No Results Found");
				cbsresponse.setTransactions(null);
			}
		}
		catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
			cbsresponse.setErrorString("Failed to process request");
			e.printStackTrace();
		}

		return cbsresponse;
	}

	private String convertDateFormat(String inputDateStr) {
		SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat outputFormat = new SimpleDateFormat("dd-MM-yyyy");
		Date date;
		String outputDateStr = "";

		try {
			date = inputFormat.parse(inputDateStr);
			outputDateStr = outputFormat.format(date);
		}
		catch (Exception e) {
			logger.info("FAILED TO PARSE DATE FORMAT. ERROR: {}", e.getMessage());
			e.printStackTrace();
		}

		return outputDateStr;
	}

	public AgentIdAndBalanceResponseMapper getAgentBalance(String agentPhoneNumberString) {
		logger.info("Agent request id and balance triggered . agent with phone number: "+agentPhoneNumberString);
		MobileBanking service = new MobileBanking();
		MobileBankingPort portType = service.getMobileBankingPort();
		AgentIdAndBalanceResponseMapper agentResponse = new AgentIdAndBalanceResponseMapper();

		Holder<String> responseMessage = new Holder<>("");
		Holder<String> response = new Holder<>("");
		Holder<BigDecimal> balance = new Holder<>();
		Holder<String> agentPhoneNumber = new Holder<>(agentPhoneNumberString);

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		logger.info("Attempt for call to CBS for transaction of fetching agent balance with phoneNumber: "+agentPhoneNumberString);

		//Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction of fetching agent balance with phoneNumber: "+agentPhoneNumberString);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("msisdn", agentPhoneNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetAgentBalance", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Agent Balance call "+request);
					String response1 = sharedFunctions.postToCbs("GetAgentBalance", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from cbs for GetAgentBalance call "+response1);
					String returnValue1 = sharedFunctions.responseFormatter(spXmlTemplates, response1, "balance");
					balance.value = new BigDecimal(returnValue1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "response_message");
					response.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "response");
				}
				else portType.getAgentBalance(agentPhoneNumber, balance, response, responseMessage);
				break;
			}
			catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("agent id and balance received ");
			agentResponse.setBalance(balance.value.toString());
			agentResponse.setAgentId(agentPhoneNumberString);
			agentResponse.setResponseMessage(responseMessage.value);
			logger.info("agent balance returnValue {} ", balance.value.toString());
			logger.info("agent balance response message {} ", responseMessage.value);
			logger.info("agent balance response code {} ", response.value);

			/*if(null != responseMessage.value){
				String agentId = "No Agent Id";
				String responseAgent = responseMessage.value;
				JSONObject agentObject = new JSONObject(responseAgent);
				if(null != agentObject.get("agentid")){
					if (agentObject.toString().contains("agentid")) {
						agentId = agentObject.getString("agentid");
					}
				}
				String formattedAgentId = agentId.replaceAll("^\"|\"$", "");
				agentResponse.setAgentId(formattedAgentId);
				agentResponse.setResponseMessage(responseMessage.value);
				logger.info("agent balance returnValue {} ", balance.value.toString());
				logger.info("agent balance response message {} ", responseMessage.value);
				logger.info("agent balance response code {} ", response.value);
			}*/
		}
		catch (Exception e) { logger.info("agent id and balance call failed: "); e.printStackTrace();}

		return agentResponse;
	}

}