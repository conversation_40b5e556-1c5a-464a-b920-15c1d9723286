package com.tl.spotcash.agencybanking.adapter.ISO;

import com.solab.iso8583.IsoMessage;
import com.solab.iso8583.IsoType;
import com.solab.iso8583.MessageFactory;
import com.solab.iso8583.impl.SimpleTraceGenerator;
import com.solab.iso8583.parse.ConfigParser;
import com.tl.spotcash.agencybanking.adapter.ISO.models.HostParameters;
import com.tl.spotcash.agencybanking.adapter.ISO.models.ParentServiceMapper;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.IsoResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.SpotcashUtilities;
import com.tl.spotcash.agencybanking.entity.SpVpnclientsConfigs;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;

/**
 *
 * <AUTHOR>
 */
public class Iso8583serverinvoker {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(Iso8583serverinvoker.class);
    private JSONObject innerJson;
    private SpVpnclientsConfigs vpnclientconfig;
    private ParentServiceMapper respoMapper;
    private CbsRequestData isoParams;
    private MessageFactory mfact;

    /**
     *
     * @param innerJson
     * @param isoParams
     * @param vpnclientconfig
     * @param respoMapper
     */
    public Iso8583serverinvoker(JSONObject innerJson, CbsRequestData isoParams, SpVpnclientsConfigs vpnclientconfig, ParentServiceMapper respoMapper) {
        this.innerJson = innerJson;
        this.vpnclientconfig = vpnclientconfig;
        this.isoParams = isoParams;
        this.respoMapper = respoMapper;
        
        try {
            mfact = ConfigParser.createFromUrl(new java.net.URL("http://localhost/isoconfigs/client_" + isoParams.getClientId() + ".xml"));
            LOGGER.info("PASSED CONFIG FILE #### http://localhost/isoconfigs/client_" + isoParams.getClientId() + ".xml");
        } catch (IOException ex) {
            LOGGER.error("COULD NOT READ CONFIG FILE " + isoParams.getClientId() + ".xml" + " FROM THE GIVEN PATH http://localhost/isoconfigs/client_" + isoParams.getClientId());
        }
    }

    /**
     * @return
     */
    public IsoMessage createISO() { //0420 for reversal
        DateFormat dateformat = new SimpleDateFormat("MMddHHmmss");
        IsoMessage isoMsg = mfact.newMessage(0x200);
        mfact.setTraceNumberGenerator(new SimpleTraceGenerator((int) (System.currentTimeMillis() % 10000)));
        Iterator<?> keys = innerJson.keys();
        LOGGER.info("Transaction id >>> " + isoParams.getTransactionId());
        while (keys.hasNext()) {
            try {
                String key = (String) keys.next();
                if (innerJson.get(key) instanceof JSONObject) {
                    JSONObject isoJsonValues = new JSONObject(innerJson.get(key).toString());
                    IsoType DATATYPE = IsoType.valueOf(isoJsonValues.getString("datatype"));
                    
                    LOGGER.info("" + isoJsonValues.getString("value") + " >>>> " + key + "_" + DATATYPE + "_" + isoJsonValues.getString("length"));
                    switch (isoJsonValues.getString("value")) {
                        case "MSISDN":
                            LOGGER.info("msisdn : # " + isoParams.getMsisdn());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getMsisdn(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "AMOUNT":
                            LOGGER.info("amount : # " + isoParams.getAmount());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getAmount(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "DATE":
                            LOGGER.info("date : # " + new Integer(dateformat.format(new Date())));
                            isoMsg.setValue(Integer.parseInt(key), new Integer(dateformat.format(new Date())), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "CHARGES":
                            LOGGER.info("charges : # " + isoParams.getService_charge());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getService_charge(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "CR_ACCOUNT": //Get the account of client and include in the request  account_number
                            LOGGER.info("cr account : # " + isoParams.getAccount_number());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getAccount_number(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "TRACE":
                            LOGGER.info("trace : # " + new SpotcashUtilities().randInt(100000, 600000));
                            isoMsg.setValue(Integer.parseInt(key), new SpotcashUtilities().randInt(1000, 6000), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "TRX_ID":
                            if (HostParameters.HAS_NO_ISOLENGTH.iso_length_required().equals(vpnclientconfig.getHasLength())) {
                                LOGGER.info("trxid : # " + isoParams.getTranstemptableid());
                                isoMsg.setValue(Integer.parseInt(key), isoParams.getTranstemptableid(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            } else {
                                LOGGER.info("trxid : # " + isoParams.getTransactionId());
                                isoMsg.setValue(Integer.parseInt(key), isoParams.getTransactionId(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            }
                            break;
                        case "NAMES":
                            LOGGER.info("names : # " + isoParams.getCustomer_name());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getCustomer_name(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "AGENT_STORE_CODE":
                            LOGGER.info("store_code : # " + isoParams.getAgent_store_code());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getAgent_store_code(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "ACCOUNT":
                            LOGGER.info("account : # " + isoParams.getAccount_number());
                            isoMsg.setValue(Integer.parseInt(key), isoParams.getAccount_number(), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        case "RESP_CODE":
                            isoMsg.setValue(Integer.parseInt(key), "000", DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                        default:
                            isoMsg.setValue(Integer.parseInt(key), isoJsonValues.getString("value"), DATATYPE, Integer.parseInt(isoJsonValues.getString("length")));
                            break;
                    }
                    
                }
            } catch (Exception ex) {
                LOGGER.error("Error occured {}", ex.getMessage(), ex);
            }
        }
        return isoMsg;
    }

    /**
     *
     * @param isoMessage
     * @return
     */
    public IsoResponseMapper sendRequest(IsoMessage isoMessage) {
        IsoResponseMapper response = null;
        String isoString = new String(isoMessage.writeData());
        int isoLength = isoString.length();
        String formatedLength = String.format("%06d", isoLength);
        LOGGER.info("The ISO message # " + isoString);
        String returnIso;
        String host = vpnclientconfig.getHostUrl();
        int port = Integer.parseInt(vpnclientconfig.getHostPort());
        SocketConnection isoSocket = new SocketConnection();
        if ("NO".equals(vpnclientconfig.getHasLength())) {
            returnIso = isoSocket.j8583isoserverMessage(host, port, isoString);
        } else {
            returnIso = isoSocket.j8583isoserverMessage(host, port, formatedLength + isoString);
        }
        if (returnIso != null) {
            try {
                LOGGER.info("[ 0210 RESPONSE ] >>> " + returnIso);
                response = getResponse(returnIso, isoParams.getTransactionId());
            } catch (ParseException | UnsupportedEncodingException ex) {
                LOGGER.error("[ 0210 RESPONSE ERROR ] >>> " + ex.toString());
            }
        }
        return response;
    }

    /**
     *
     * @param iso_msg
     * @param orignalTrxId
     * @return
     * @throws ParseException
     * @throws UnsupportedEncodingException
     */
    public IsoResponseMapper getResponse(String iso_msg, String orignalTrxId) throws ParseException, UnsupportedEncodingException {
        IsoResponseMapper responseobject = new IsoResponseMapper();
        if (iso_msg != null) {
            LOGGER.info("SERVER RESPONSE RECEIVED :" + iso_msg);
            LOGGER.info("Response mapper " + respoMapper.getISOSTRING().getSubstring().getVal());
            
            iso_msg = iso_msg.substring(Integer.parseInt(respoMapper.getISOSTRING().getSubstring().getVal()));
            LOGGER.info("Stripped ISO message >>>> " + iso_msg);
            
            IsoMessage resp = mfact.parseMessage(iso_msg.getBytes(), 0);
            String mti = iso_msg.substring(0, 4);
            String msisdn = resp.hasField(2) ? resp.getField(2).toString() : "0";
            String processing_code = resp.hasField(3) ? resp.getField(3).toString() : "";
            String amount = resp.hasField(4) ? resp.getField(4).toString() : "0";
            String charges = resp.hasField(6) ? resp.getField(6).toString() : "0";
            String date = resp.hasField(7) ? resp.getField(7).toString() : "";
            String trace = resp.hasField(11) ? resp.getField(11).toString() : "";
            String dt_short = resp.hasField(12) ? resp.getField(12).toString() : "";
            String smp = resp.hasField(24) ? resp.getField(24).toString() : "";
            String othercharges = resp.hasField(28) ? resp.getField(28).toString() : "0";
            String institution_id = resp.hasField(32) ? resp.getField(32).toString() : "";
            String transaction_id = resp.hasField(37) ? resp.getField(37).toString() : "0";
            String auth_code = resp.hasField(38) ? resp.getField(38).toString() : "";
            String response_code = resp.hasField(39) ? resp.getField(39).toString() : "";
            String names = resp.hasField(44) ? resp.getField(44).toString() : "";
            String product = resp.hasField(46) ? resp.getField(46).toString() : "";
            String msg = resp.hasField(48) ? resp.getField(48).toString() : "";
            String currency_code = resp.hasField(49) ? resp.getField(49).toString() : "";
            String availableBalance = resp.hasField(54) ? resp.getField(54).toString() : "00";
            String client_name = resp.hasField(62) ? resp.getField(62).toString() : "";
            String dob = resp.hasField(66) ? resp.getField(66).toString() : "";
            String mpesa = resp.hasField(98) ? resp.getField(98).toString() : "";
            String customer_account = resp.hasField(102) ? resp.getField(102).toString() : "";
            String account_no = resp.hasField(103) ? resp.getField(103).toString() : "";
            String desc = resp.hasField(112) ? resp.getField(112).toString() : "";
            String iso_response_code = resp.hasField(121) ? resp.getField(121).toString() : "";
            String response_field = resp.hasField(122) ? resp.getField(122).toString() : "";
            String id_no = resp.hasField(124) ? resp.getField(124).toString() : "";
            String reg_date = resp.hasField(125) ? resp.getField(125).toString() : "";
            String m_num = resp.hasField(126) ? resp.getField(126).toString() : "";
            
            responseobject.setMti(mti.trim());
            responseobject.setMsisdn(msisdn.trim());
            responseobject.setProcessing_code(processing_code.trim());
            responseobject.setAmount(amount.trim());
            responseobject.setCharges(charges.trim());
            responseobject.setDate(date.trim());
            responseobject.setTrace(trace.trim());
            responseobject.setDt_short(dt_short);
            responseobject.setSmp(smp.trim());
            responseobject.setOthercharges(othercharges.trim());
            responseobject.setInstitution_id(institution_id.trim());
            responseobject.setTransaction_id(transaction_id.trim());
            responseobject.setAuth_code(auth_code.trim());
            if ("000".equals(response_code)) {
                responseobject.setResponse_code("00");
            } else {
                responseobject.setResponse_code(response_code.trim());
            }
            responseobject.setNames(names.trim());
            responseobject.setProduct(product.trim());
            responseobject.setMsg(msg.trim());
            responseobject.setCurrency_code(currency_code.trim());
            responseobject.setBal(availableBalance.trim());
            responseobject.setClient_name(client_name.trim());
            responseobject.setDob(dob.trim());
            responseobject.setMpesa(mpesa.trim());
            responseobject.setCustomer_account(customer_account.trim());
            responseobject.setCredit_account(account_no.trim());
            responseobject.setDesc(desc.trim());
            responseobject.setDesc(desc.trim());
            responseobject.setIso_response_code(iso_response_code.trim());
            responseobject.setResponse_field(response_field.trim());
            responseobject.setId_no(id_no.trim());
            responseobject.setReg_date(reg_date.trim());
            responseobject.setMsisdn_mnum(m_num.trim());
            responseobject.setIsomessage(iso_msg);
            responseobject.setOriginalTrxId(orignalTrxId);
            responseobject.setTransTempTableId(isoParams.getTranstemptableid());
            LOGGER.info("MTI " + mti);
            LOGGER.info("2.MSISDN " + msisdn);
            LOGGER.info("3.PROCESS CODE " + processing_code);
            LOGGER.info("4.AMOUNT " + amount);
            LOGGER.info("6.CHARGES " + charges);
            LOGGER.info("7.DATE " + date);
            LOGGER.info("11.TRACE " + trace);
            LOGGER.info("24.TRACE " + smp);
            LOGGER.info("28.OTHER CHARGES " + othercharges);
            LOGGER.info("32.INSTITUTION " + institution_id);
            LOGGER.info("37.TRANSACTION ID " + transaction_id);
            LOGGER.info("38.AUTH CODE " + auth_code);
            LOGGER.info("39.RESP CODE " + response_code);
            LOGGER.info("44.NAME " + names);
            LOGGER.info("46.PRODUCT " + product);
            LOGGER.info("48.MSG " + msg);
            LOGGER.info("49.CURRENCY_CODE " + currency_code);
            LOGGER.info("54.AVAIL BAL " + availableBalance);
            LOGGER.info("62.CLIENT NAME " + client_name);
            LOGGER.info("66.DOB " + dob);
            LOGGER.info("98.MPESA " + mpesa);
            LOGGER.info("102.ACC NO " + customer_account);
            LOGGER.info("103.ACC NO " + account_no);
            LOGGER.info("112.DESC " + desc);
            LOGGER.info("121.RESPONSE CODE 121 " + iso_response_code);
            LOGGER.info("122.RESPONSE FIELD " + response_field);
            LOGGER.info("112.DESC " + desc);
            LOGGER.info("124.ID NO " + id_no);
            LOGGER.info("125.REG DATE " + reg_date);
            LOGGER.info("126.MOBILE NO " + m_num);
            return responseobject;
        } else {
            return null;
        }
    }
//   returnIso = "00210E62000010A818000000000000400000012254727484829370000000000000000000006291121460000000045742514563215510113869    000SPAS0663        *****************|STAMP DUTY|2|KES~**************|COMMISSION ON WITHDRAWAL|50|KES~**************|CASH WITHDRAWAL|16000|KES~40418400502000001381700";
}
