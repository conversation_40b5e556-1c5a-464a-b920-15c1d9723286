package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.xml.ws.soap.SOAPFaultException;
import com.fasterxml.jackson.core.type.TypeReference;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonSyntaxException;
import com.sun.xml.ws.fault.ServerSOAPFaultException;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.AgencyBanking;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs.AgencyBankingPort;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.*;
import com.tl.spotcash.agencybanking.custommodels.agent.AgentReportResponse;
import com.tl.spotcash.agencybanking.custommodels.agent.AgentReportResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.*;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.ResponseCodes;
import com.tl.spotcash.agencybanking.custommodels.event.*;
import com.tl.spotcash.agencybanking.custommodels.member.GetMemberRequest;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.entity.SpVpnclientsConfigs;
import com.tl.spotcash.agencybanking.entity.SpXmlTemplates;
import com.tl.spotcash.agencybanking.pojo.AgencyResponseData;
import com.tl.spotcash.agencybanking.service.SpotcashCbsService;
import com.tl.spotcash.agencybanking.utils.Crypt;
import com.tl.spotcash.agencybanking.utils.Httpfunctions;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.xml.ws.BindingProvider;
import javax.xml.ws.Holder;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.Authenticator;
import java.util.*;

/**
 * <AUTHOR>
 */
public class WebserviceStubsImpl {

	private static final Logger logger = LoggerFactory.getLogger(WebserviceStubsImpl.class);

	private Environment environment;

	BigInteger clientId;



	private SpotcashCbsService spotcashCbsService;


	CrudTransactionController crudTransactionController;

	SharedFunctions sharedFunctions = new SharedFunctions();

	Boolean useXmlTemplates = false;

	private String userName;
	private String password;
	private String host;
	private String cbsUrl;

	public WebserviceStubsImpl(String userName, String password, String host, Environment environment) {
		this.userName = userName;
		this.password = password;
		this.host = host;
		this.environment = environment;
	}

	public WebserviceStubsImpl(String userName, String password, String host, Environment environment,
							   CrudTransactionController crudTransactionController, String cbsUrl,
							   BigInteger clientId) {
		this.userName = userName;
		this.password = password;
		this.host = host;
		this.environment = environment;
		this.crudTransactionController = crudTransactionController;
		this.cbsUrl= cbsUrl;
		this.clientId = clientId;
	}

	

	public void authenticate_to_corebanking() {
		NtlmAuthenticator authenticator = new NtlmAuthenticator(userName, password);
		Authenticator.setDefault(authenticator);
	}

	/**
	 * @param requestData
	 * @return
	 * @throws Exception
	 */



	public CbsResponseData spotcash_cbs_call(CbsRequestData requestData) throws Exception {
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		CbsResponseData cbsresponse = new CbsResponseData();

		Holder<String> requestId = new Holder<>(requestData.getTransactionId());
		Holder<String> phoneNo = new Holder<>(requestData.getMsisdn());
		Holder<Integer> transactionType = new Holder<>(Integer.parseInt(requestData.getServiceId()));
		Holder<String> amount = new Holder<>(requestData.getAmount());
		Holder<String> agentPhoneNo = new Holder<>(requestData.getAgent_msisdn());
		Holder<String> trnx_charges = new Holder<>(requestData.getTrnx_charges());
		Holder<String> accountNumber = new Holder<>(requestData.getAccount_number());

		String description = Optional.ofNullable(requestData.getDescription()).orElse(" ");
		String fkeyString = Optional.ofNullable(requestData.getUserId()).orElse("");

		Holder<String> crAccount = new Holder<>("");
		Holder<String> status = new Holder<>("");
		Holder<String> fKey = new Holder<>(fkeyString);
		Holder<String> balance = new Holder<>("");
		Holder<String> message = new Holder<>("");
		Holder<String> response = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		String customerType = requestData.getCustomerType();

		((BindingProvider) portType).getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host
		);
		if (requestData.getDestinationAccount() != null) {
			crAccount = new Holder<>(!ObjectUtils.isEmpty(requestData.getDestinationAccount()) ? requestData.getDestinationAccount(): "");
			requestData.setOtherAccountNumber(requestData.getDestinationAccount());
		}

		logger.info("Posting to CBS with values: {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}",
				requestId.value, phoneNo.value, transactionType.value, amount.value, trnx_charges.value,
				accountNumber.value, crAccount.value, status.value, fKey.value, balance.value,
				message.value, response.value, responseMessage.value, agentPhoneNo.value, description);

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		for (int count = 1; count <= retryCount; count++) {
			try {
				logger.info("Attempt number {} for transaction {}", count, requestId.value);

				BigInteger useXmlTemplates = crudTransactionController
						.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
						.getUseXmlTemplate();

				if (useXmlTemplates.equals(BigInteger.ONE)) {
					// XML Template Mode
					ObjectMapper mapper = new ObjectMapper();
					Map<String, String> requestMap = mapper.convertValue(requestData, Map.class);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("Spotcash", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), new HashMap<String, String>(requestMap));
					logger.info("Request to CBS for Spotcash call "+request);
					String response1 = sharedFunctions.postToCbs("Spotcash", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for Spotcash call "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					response.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					break;

				} else if (useXmlTemplates.equals(BigInteger.valueOf(2))) {
					// REST Bridge Integration
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetchClientAppBridgeConfiguration(new BigInteger(requestData.getClientId()));
					String authenticateUrl = bridgeConfigs.getHostUrl() + "/authenticate";

					ObjectMapper mapper = new ObjectMapper();
					Map<String, String> paramMap = mapper.readValue(bridgeConfigs.getApiParams(), Map.class);
					String sessionId = Httpfunctions.obtainBridgeHandshake(authenticateUrl, paramMap.get("cbsUser"), paramMap.get("cbsPassword"));

					String processUrl;
					switch (requestData.getServiceId()) {
						case "5": case "6": case "7": case "23": case "600": case "601":
							processUrl = bridgeConfigs.getHostUrl() + "/agency/directDeposit";
							break;
						case "741":
							requestData.setServiceId("710");
							processUrl = bridgeConfigs.getHostUrl() + "/agency/comprehensiveApplyLoan";
							break;
						default:
							processUrl = bridgeConfigs.getHostUrl() + "/agency/Spotcash";
					}

					String cbsResponseString = Httpfunctions.POSTRequestWithSession(processUrl, requestData, sessionId);
					logger.info("Bridge CBS Response: {}", cbsResponseString);

					CbsResponseData cbsBridgeResponse = new ObjectMapper().readValue(cbsResponseString, CbsResponseData.class);
					return cbsBridgeResponse;

				} else {
					// SOAP Call
					portType.spotcash(requestId, phoneNo, transactionType, amount, trnx_charges, accountNumber, crAccount,
							status, fKey, balance, message, response, responseMessage, agentPhoneNo, customerType, description);
					break;
				}

			} catch (SOAPFaultException e) {
				logger.warn("SOAPFaultException: {}", e.getMessage());
				if (e.getMessage().contains("locked by another user")) {
					Thread.sleep(sleepTime);
				} else {
					break;
				}
			} catch (Exception e) {
				logger.error("Unexpected exception: ", e);
				break;
			}
		}

		// Finalize response
		cbsresponse.setRequestId(requestId.value);
		cbsresponse.setPhoneNo(phoneNo.value);
		cbsresponse.setTransactionType(transactionType.value.toString());
		cbsresponse.setAmount(amount.value);
		cbsresponse.setAccountNumber(accountNumber.value);
		cbsresponse.setAgentPhoneNo(agentPhoneNo.value);
		cbsresponse.setBalance(balance.value);
		cbsresponse.setStatus(status.value);
		cbsresponse.setResponse(response.value);
		cbsresponse.setResponseMessage(responseMessage.value);

		return cbsresponse;
	}


public AgentIdAndBalanceResponseMapper getAgentIdAndBalance(String agentPhoneNumberString) {
	logger.info("Agent request id and balance triggered. Agent phone number: {}", agentPhoneNumberString);

	AgencyBanking service = new AgencyBanking();
	AgencyBankingPort portType = service.getAgencyBankingPort();
	AgentIdAndBalanceResponseMapper agentResponse = new AgentIdAndBalanceResponseMapper();

	Holder<String> responseMessage = new Holder<>("");
	Holder<BigDecimal> returnValue = new Holder<>();
	Holder<String> agentPhoneNumber = new Holder<>(agentPhoneNumberString);

	BindingProvider bindingProvider = (BindingProvider) portType;
	bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

	int count = 1;
	int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
	long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

	while (count <= retryCount) {
		try {
			logger.info("Attempt #{} to fetch agent balance", count);

			BigInteger useXmlTemplates = crudTransactionController
					.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
					.getUseXmlTemplate();

			if (useXmlTemplates.equals(BigInteger.ONE)) {

					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("agentPhoneNumber", agentPhoneNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetAgentBalance", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Agent Balance call "+request);
					String response1 = sharedFunctions.postToCbs("GetAgentBalance", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from cbs for GetAgentBalance call "+response1);
					String returnValue1 = sharedFunctions.responseFormatter(spXmlTemplates, response1, "return_value");
					returnValue.value = new BigDecimal(returnValue1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");


			} else if (useXmlTemplates.equals(BigInteger.valueOf(2))) {
				// === REST (BRIDGE) MODE ===
				SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
				String authenticateUrl = bridgeConfigs.getHostUrl() + "/authenticate";

				ObjectMapper mapper = new ObjectMapper();
				Map<String, String> paramMap = mapper.readValue(bridgeConfigs.getApiParams(), Map.class);

				String sessionId = Httpfunctions.obtainBridgeHandshake(authenticateUrl, paramMap.get("cbsUser"), paramMap.get("cbsPassword"));

				String processUrl = bridgeConfigs.getHostUrl() + "/agency/GetAgentBalance";
				Map<String, String> hashMap = new HashMap<>();
				hashMap.put("agentPhoneNumber", agentPhoneNumberString);

				String cbsResponseString = Httpfunctions.POSTRequestWithSession(processUrl, hashMap, sessionId);
				logger.info("Response from REST CBS service:\n{}", cbsResponseString);

				AgencyResponseData cbsResponse = mapper.readValue(cbsResponseString, AgencyResponseData.class);

				// Assign values into holders to match SOAP/XML processing
				returnValue.value = new BigDecimal(cbsResponse.getReturnValue());
				responseMessage.value = mapper.writeValueAsString(cbsResponse.getResponseMessage());

			} else {
				// === SOAP MODE ===
				portType.getAgentBalance(agentPhoneNumber, responseMessage, returnValue);
			}

			// Break loop if successful
			break;

		} catch (SOAPFaultException ex) {
			logger.warn("SOAP Fault Exception: {}", ex.getMessage());
			if (ex.getMessage().contains("locked by another user")) {
				count++;
				try {
					Thread.sleep(sleepTime);
				} catch (InterruptedException e) {
					logger.error("Sleep interrupted: {}", e.getMessage());
				}
			} else {
				break;
			}
		} catch (RuntimeException | IOException ex) {
			logger.error("Exception during balance fetch: ", ex);
			break;
		}
	}

	try {
		logger.info("Agent balance fetched: {}", returnValue.value);
		agentResponse.setBalance(returnValue.value != null ? returnValue.value.toString() : "0");

		if (responseMessage.value != null && !responseMessage.value.trim().isEmpty()) {
			String responseAgent = responseMessage.value;
			logger.info("Agent response message: {}", responseAgent);

			try {
				JSONObject agentObject = new JSONObject(responseAgent);
				String agentId = agentObject.optString("agentid", "No Agent Id");
				agentResponse.setAgentId(agentId);
			} catch (Exception parseEx) {
				logger.warn("Failed to parse agentId from response message.");
				agentResponse.setAgentId("Unknown");
			}

			agentResponse.setResponseMessage(responseAgent);
		}

	} catch (Exception e) {
		logger.error("Failed to parse final agent response: ", e);
	}

	return agentResponse;
}




	public CbsAccountsResponseMapper spotcash_cbs_accounts_call(String idNumberString) {
		logger.info("SP call accounts with ID: {}", idNumberString);
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		CbsAccountsResponseMapper cbsresponse = new CbsAccountsResponseMapper();

		Holder<String> idNumber = new Holder<>(idNumberString);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>();

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host
		);

		logger.info("Attempting to call CBS to fetch accounts for ID: {}", idNumberString);

		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number {} to CBS for ID: {}", count, idNumberString);

				BigInteger useXmlTemplates = crudTransactionController
						.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
						.getUseXmlTemplate();

				ObjectMapper mapper = new ObjectMapper();

				if (BigInteger.ONE.equals(useXmlTemplates)) {
					// XML Template Mode
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetMemberAccounts", this.clientId);

					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", idNumberString);

					String request3 = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for GetMemberAccounts call "+request3);
					String response3 = sharedFunctions.postToCbs("GetMemberAccounts", request3, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for GetMemberAccounts call "+response3);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseCode");
				} else if (BigInteger.valueOf(2).equals(useXmlTemplates)) {
					// REST (Bridge) Mode
					SpVpnclientsConfigs bridgeConfig = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
					String authenticateUrl = bridgeConfig.getHostUrl() + "/authenticate";

					Map<String, String> paramMap = mapper.readValue(bridgeConfig.getApiParams(), new TypeReference<Map<String, String>>() {});
					String sessionId = Httpfunctions.obtainBridgeHandshake(authenticateUrl, paramMap.get("cbsUser"), paramMap.get("cbsPassword"));

					String processUrl = bridgeConfig.getHostUrl() + "/agency/GetMemberAccounts";
					Map<String, String> requestMap = new HashMap<>();
					requestMap.put("idNumber", idNumberString);

					String restResponse = Httpfunctions.POSTRequestWithSession(processUrl, requestMap, sessionId);
					logger.info("Response from REST CBS: {}", restResponse);

					AgencyResponseData responseData = mapper.readValue(restResponse, AgencyResponseData.class);
					returnValue.value = Integer.valueOf(responseData.getReturnValue());


					JsonNode rootNode = mapper.readTree(restResponse);


					JsonNode responseMessageNode = rootNode.path("responseMessage");



					responseMessage.value = mapper.writeValueAsString(responseMessageNode);


//					responseCode.value = rootNode.path("responseCode").asText();

					responseCode.value = responseData.getResponseCode();


					idNumber.value = rootNode.path("idNumber").asText();


					errorMessage.value = rootNode.path("errorString").asText();


				} else {
					// Default SOAP mode
					portType.getMemberAccounts(idNumber, responseCode, responseMessage, errorMessage, returnValue);
				}

				break; // exit retry loop if successful

			} catch (SOAPFaultException ex) {
				logger.warn("SOAPFaultException: {}", ex.getMessage());
				if (ex.getMessage() != null && ex.getMessage().contains("locked by another user")) {
					logger.info("Retrying due to locked table (attempt {} of {})", count, retryCount);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException ie) {
						logger.error("Sleep interrupted: {}", ie.getMessage(), ie);
					}
				} else {
					break;
				}
			} catch (Exception ex) {
				logger.error("Unexpected exception during CBS call: {}", ex.getMessage(), ex);
				break;
			}
		}

		// Parse response
		try {
			logger.info("Processing CBS response");
			String code = StringUtils.trimToEmpty(responseCode.value);
			String message = StringUtils.trimToEmpty(responseMessage.value);
			String error = StringUtils.trimToEmpty(errorMessage.value);

			cbsresponse.setResponseCode(code);
			logger.info("CBS Response Code: {}", code);
			logger.info("CBS Error Message: {}", error);
			logger.info("CBS Response Message: {}", message);

			if (ResponseCodes.SUCCESS.getResponseCode().equals(code) || ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(code)) {
				logger.info("Fetch accounts success");
				ObjectMapper mapper = new ObjectMapper();
				cbsresponse.setAccounts(mapper.readValue(message, CustomerAccounts.class));
			} else {
				logger.warn("Fetch accounts failed with error: {}", error);
				cbsresponse.setErrorString(error);
			}
		} catch (Exception e) {
			logger.error("Exception while parsing CBS response: {}", e.getMessage(), e);
		}

		return cbsresponse;
	}


	public NewMember spotcash_cbs_new_registration_call(HashMap<String, String> requestMap) {
		String memRegReqNewBioData = requestMap.getOrDefault("memRegReqNewBioData", "0");
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		NewMember cbsresponse = new NewMember();
		Holder<String> firstName = new Holder<>(requestMap.get("firstName"));
		Holder<String> middleName = new Holder<>(requestMap.get("middleName"));
		Holder<String> branchCode = new Holder<>(requestMap.get("branchCode"));
		Holder<String> surname = new Holder<>(requestMap.get("surname"));
		Holder<String> iDNo = new Holder<>(requestMap.get("iDNo"));
		Holder<String> pinNo = new Holder<>(requestMap.get("pinNo"));
		Holder<String> address = new Holder<>(requestMap.get("address"));
		Holder<String> applicationNo = new Holder<>(requestMap.get("applicationNo"));
		Holder<String> gender = new Holder<>(requestMap.get("gender"));
		Holder<String> occupation = new Holder<>(requestMap.get("occupation"));
		Holder<String> phoneNo = new Holder<>(requestMap.get("phoneNo"));
		Holder<String> email = new Holder<>(requestMap.get("email"));
		Holder<String> passportPhoto = new Holder<>(requestMap.get("passportPhoto"));
		Holder<String> frontID = new Holder<>(requestMap.get("frontID"));
		Holder<String> backID = new Holder<>(requestMap.get("backID"));
		Holder<String> signature = new Holder<>(requestMap.get("signature"));
		Holder<String> dateOfBirth = new Holder<>(requestMap.get("dateOfBirth"));
		Holder<String> agencyUserAccount = new Holder<>(requestMap.get("introducer"));
		Holder<String> maritalStatus = new Holder<>(requestMap.get("maritalStatus"));
		Holder<String> iDIssueDate = new Holder<>(requestMap.get("iDIssueDate"));
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>();
		Holder<String> payrollNo = new Holder<>(null);
		Holder<String> agentPhoneNo = new Holder<>(null);
		Holder<String> residence = new Holder<>(null);
		Holder<String> registrationForm = new Holder<>(null);

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);
		logger.info(".....................ENDPOINT ADDRESS PROPERTY {}", BindingProvider.ENDPOINT_ADDRESS_PROPERTY);

		try {
			logger.info(".................DOUBLE CHECKING FIRSTNAME " + firstName.value);
			if(memRegReqNewBioData.trim().equals("0")){
				logger.info("registering using the normal method");
				logger.info("Attempt for call to CBS for transaction new member registration");
				/*portType.memberRegistration(payrollNo,agentPhoneNo,residence,registrationForm,firstName, middleName,
						responseMessage, branchCode, surname, iDNo, pinNo, address, applicationNo, gender, occupation,
						phoneNo, email, responseCode, passportPhoto, frontID, backID, signature, agencyUserAccount,
						dateOfBirth, maritalStatus, iDIssueDate, returnValue);*/

				// Implementation to check for table locking.
				int count = 1;
				int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
				long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
				while (count <= retryCount) {
					try {
						logger.info("Attempt number " + count + " for call to CBS for transaction new member registration");
						useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
						if (useXmlTemplates) {
							SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("MemberRegistration", this.clientId);
							String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), requestMap);
							logger.info("Request to CBS for Member Registration Call "+request);
							String response1 = sharedFunctions.postToCbs("MemberRegistration", request, this.cbsUrl, this.userName, this.password);
							logger.info("Response from Cbs for MemberRegistration call "+response1);
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
							applicationNo.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "applicationNo");
						}
						else{
							portType.memberRegistration(payrollNo,agentPhoneNo,residence,registrationForm,firstName,
									middleName, responseMessage, branchCode, surname, iDNo, pinNo, address, applicationNo,
									gender, occupation, phoneNo, email, responseCode, passportPhoto, frontID, backID,
									signature, agencyUserAccount, dateOfBirth, maritalStatus, iDIssueDate, returnValue);
						}
						break;
					} catch (SOAPFaultException serverSOAPFaultException) {
						logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
						if(serverSOAPFaultException.getMessage().contains("locked by another user")){
							logger.info("Count is at " + count);
							count++;
							Thread.sleep(sleepTime);
						} else {
							break;
						}
					}
				}
			}
			else if(memRegReqNewBioData.trim().equals("1")){
				logger.info("registering using the new method");
				payrollNo = new Holder<>(requestMap.get("payrollNo"));
				agentPhoneNo = new Holder<>(requestMap.get("agentPhoneNo"));
				residence = new Holder<>(requestMap.get("residence"));
				registrationForm = new Holder<>(requestMap.get("registrationForm"));


				logger.info("Attempt for call to CBS for transaction new member registration");
				/*portType.memberRegistration(payrollNo,agentPhoneNo,residence,registrationForm,firstName, middleName,
						responseMessage, branchCode, surname, iDNo, pinNo, address, applicationNo, gender, occupation,
						phoneNo, email, responseCode, passportPhoto, frontID, backID, signature, agencyUserAccount,
						dateOfBirth, maritalStatus, iDIssueDate, returnValue);*/


				// Implementation to check for table locking.
				int count = 1;
				int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
				long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
				while (count <= retryCount) {
					try {
						logger.info("Attempt number " + count + " for call to CBS for transaction new member registration");
						useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE)==0?true:false;
						if(useXmlTemplates) {
							SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("MemberRegistration", this.clientId);
							String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), requestMap);
							logger.info("Request to CBS for Member Registration Call "+request);
							String response1 = sharedFunctions.postToCbs("MemberRegistration", request, this.cbsUrl, this.userName, this.password);
							logger.info("Response from Cbs for MemberRegistration call "+response1);
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
							applicationNo.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "applicationNo");
						}
						else {
							portType.memberRegistration(payrollNo,agentPhoneNo,residence,registrationForm,firstName, middleName,
									responseMessage, branchCode, surname, iDNo, pinNo, address, applicationNo, gender, occupation,
									phoneNo, email, responseCode, passportPhoto, frontID, backID, signature, agencyUserAccount,
									dateOfBirth, maritalStatus, iDIssueDate, returnValue);
						}
						break;
					} catch (SOAPFaultException serverSOAPFaultException) {
						logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
						if(serverSOAPFaultException.getMessage().contains("locked by another user")){
							logger.info("Count is at " + count);
							count++;
							Thread.sleep(sleepTime);
						} else {
							break;
						}
					}
				}
			}else{
				logger.error("unable to determine the registration method");
			}
			cbsresponse.setCbsResponse(responseMessage.value);
			cbsresponse.setResponseCode(responseCode.value);
			cbsresponse.setApplicationNo(applicationNo.value);
			return cbsresponse;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public AgentReportResponseMapper spotcash_cbs_agent_report_call(String phone, String date_from, String date_to) {
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();

		AgentReportResponseMapper cbsresponse = new AgentReportResponseMapper();

		Holder<String> fromDate = new Holder<>(date_from);
		Holder<String> toDate = new Holder<>(date_to);
		Holder<String> phoneNumber = new Holder<>(phone);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host);

		//portType.agentInfo(fromDate, toDate, responseCode, phoneNumber, responseMessage, errorMessage);
		try {
			cbsresponse.setResponseCode(responseCode.value);
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {

				ObjectMapper mapper = new ObjectMapper();
				cbsresponse.setAgentReportResponse(mapper.readValue(responseMessage.value, AgentReportResponse.class));

			} else {
				cbsresponse.setErrorMessage(errorMessage.value);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return cbsresponse;
	}





	public ValidateAccountResponseMapper spotcash_validate_accounts_call(String accountNumberString) {
		logger.info("Validating account with number: {}", accountNumberString);

		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		ValidateAccountResponseMapper validateResponse = new ValidateAccountResponseMapper();

		Holder<String> accountNumber = new Holder<>(accountNumberString);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		((BindingProvider) portType).getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		boolean accountDetailsAlreadySet = false;

		while (count <= retryCount) {
			try {
				logger.info("Attempt #{} to validate account number: {}", count, accountNumberString);

				BigInteger useXmlTemplates = crudTransactionController
						.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
						.getUseXmlTemplate();

				ObjectMapper mapper = new ObjectMapper();

				if (BigInteger.ONE.equals(useXmlTemplates)) {
					// === XML Template Mode ===
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("account_number", accountNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ValidateAccountDetails", this.clientId);
					String request3 = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for Validate Account details call "+request3);
					String response3 = sharedFunctions.postToCbs("ValidateAccountDetails", request3, this.cbsUrl, this.userName, this.password);
					logger.info("Response from CBS for ValidateAccountDetails call "+response3);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response3, "responseCode");

				} else if (BigInteger.valueOf(2).equals(useXmlTemplates)) {
					// === REST (Bridge) Mode ===
					SpVpnclientsConfigs bridgeConfig = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
					String authenticateUrl = bridgeConfig.getHostUrl() + "/authenticate";

					Map<String, String> authParams = new ObjectMapper().readValue(
							bridgeConfig.getApiParams(), new TypeReference<Map<String, String>>() {}
					);

					String sessionId = Httpfunctions.obtainBridgeHandshake(
							authenticateUrl, authParams.get("cbsUser"), authParams.get("cbsPassword"));

					String processUrl = bridgeConfig.getHostUrl() + "/agency/ValidateAccountDetails";
					Map<String, String> requestMap = new HashMap<>();
					requestMap.put("account_number", accountNumberString);

					String restResponse = Httpfunctions.POSTRequestWithSession(processUrl, requestMap, sessionId);
					logger.info("Response from REST CBS: {}", restResponse);

					JsonNode jsonNode = mapper.readTree(restResponse);

					responseCode.value = jsonNode.path("responseCode").asText();
					errorMessage.value = jsonNode.path("errorString").asText();

					JsonNode accountsNode = jsonNode.path("accounts");
					if (!accountsNode.isMissingNode()) {
						AccountDetails accountDetails = mapper.treeToValue(accountsNode, AccountDetails.class);
						validateResponse.setAccountDetails(accountDetails);
						accountDetailsAlreadySet = true;
					}

				} else {
					// === Default SOAP ===
					portType.validateAccountDetails(accountNumber, responseCode, responseMessage, errorMessage);
				}

				break; // exit loop on success

			} catch (SOAPFaultException ex) {
				logger.warn("SOAPFaultException: {}", ex.getMessage());
				if (ex.getMessage() != null && ex.getMessage().contains("locked by another user")) {
					logger.info("Retrying due to locked table (attempt {} of {})", count, retryCount);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException ie) {
						logger.error("Interrupted during sleep: {}", ie.getMessage(), ie);
					}
				} else {
					break;
				}
			} catch (Exception e) {
				logger.error("Exception during account validation: {}", e.getMessage(), e);
				break;
			}
		}

		try {
			logger.info("Processing response for account validation");

			String code = StringUtils.trimToEmpty(responseCode.value);
			String message = StringUtils.trimToEmpty(responseMessage.value);
			String error = StringUtils.trimToEmpty(errorMessage.value);

			validateResponse.setResponseCode(code);
			validateResponse.setResponseMessage(message);

			logger.info("Validation response code: {}", code);
			logger.info("Validation response message: {}", message);
			logger.info("Validation error message: {}", error);

			if (ResponseCodes.SUCCESS.getResponseCode().equals(code)
					|| ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(code)) {

				logger.info("Account validation successful");

				// Only parse message if not already set from REST
				if (!accountDetailsAlreadySet && StringUtils.isNotBlank(message)) {
					ObjectMapper mapper = new ObjectMapper();
					mapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);
					validateResponse.setAccountDetails(mapper.readValue(message, AccountDetails.class));
				}

			} else {
				logger.warn("Account validation failed");
				validateResponse.setErrorString(error);
			}

		} catch (Exception e) {
			logger.error("Exception while processing CBS response: {}", e.getMessage(), e);
		}

		return validateResponse;
	}



	public FetchImageResponseMapper fetch_member_image_call(String idNumberString) {
		logger.info("Fetching member image with ID number: {}", idNumberString);

		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		FetchImageResponseMapper fetchImageResponse = new FetchImageResponseMapper();

		Holder<String> idNumber = new Holder<>(idNumberString);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>(0);

		((BindingProvider) portType).getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		boolean memberDetailsAlreadySet = false;

		while (count <= retryCount) {
			try {
				logger.info("Attempt #{} to fetch member image for ID: {}", count, idNumberString);

				BigInteger useXmlTemplates = crudTransactionController
						.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
						.getUseXmlTemplate();

				ObjectMapper mapper = new ObjectMapper();

				if (BigInteger.ONE.equals(useXmlTemplates)) {
					// === XML Template Mode ===
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", idNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetMemberImage", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request to CBS for GetMember Image call "+request);
					String response1 = sharedFunctions.postToCbs("GetMemberImage", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from Cbs for GetMemberImage call "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");

				} else if (BigInteger.valueOf(2).equals(useXmlTemplates)) {
					// === REST (Bridge) Mode ===
					SpVpnclientsConfigs bridgeConfig = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
					String authenticateUrl = bridgeConfig.getHostUrl() + "/authenticate";

					Map<String, String> authParams = mapper.readValue(
							bridgeConfig.getApiParams(), new TypeReference<Map<String, String>>() {});

					String sessionId = Httpfunctions.obtainBridgeHandshake(
							authenticateUrl, authParams.get("cbsUser"), authParams.get("cbsPassword"));

					String processUrl = bridgeConfig.getHostUrl() + "/agency/GetMemberImage";
					Map<String, String> requestMap = new HashMap<>();
					requestMap.put("idNumber", idNumberString);

					String restResponse = Httpfunctions.POSTRequestWithSession(processUrl, requestMap, sessionId);
					logger.info("Response from REST CBS: {}", restResponse);

					JsonNode jsonNode = mapper.readTree(restResponse);

					responseCode.value = jsonNode.path("responseCode").asText();
					errorMessage.value = jsonNode.path("errorString").asText();
					JsonNode member = jsonNode.path("responseMessage").path("member");
					ObjectNode wrapper = mapper.createObjectNode();
					wrapper.set("member", member);
					responseMessage.value = wrapper.toString();

//					responseMessage.value = member.toString();  // Set entire JSON object as String



				} else {
					// === Default SOAP ===
					portType.getMemberImage(idNumber, responseCode, responseMessage, errorMessage, returnValue);
				}

				break; // exit retry loop on success

			} catch (SOAPFaultException ex) {
				logger.warn("SOAPFaultException: {}", ex.getMessage());
				if (ex.getMessage() != null && ex.getMessage().contains("locked by another user")) {
					logger.info("Retrying due to locked table (attempt {} of {})", count, retryCount);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException ie) {
						logger.error("Interrupted during retry sleep: {}", ie.getMessage(), ie);
					}
				} else {
					break;
				}
			} catch (Exception e) {
				logger.error("Exception while fetching member image: {}", e.getMessage(), e);
				break;
			}
		}

		try {
			logger.info("Processing CBS response for member image fetch...");

			String code = StringUtils.trimToEmpty(responseCode.value);
			String message = StringUtils.trimToEmpty(responseMessage.value);
			String error = StringUtils.trimToEmpty(errorMessage.value);

			fetchImageResponse.setResponseCode(code);
			fetchImageResponse.setResponseMessage(message);

			logger.info("Response Code: {}", code);
			logger.info("Response Message: {}", message);
			logger.info("Error Message: {}", error);

			if (ResponseCodes.SUCCESS.getResponseCode().equals(code)
					|| ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(code)) {

				logger.info("Fetch member image successful.");

				if (!memberDetailsAlreadySet && StringUtils.isNotBlank(message)) {
					ObjectMapper mapper = new ObjectMapper();
					MemberDetails memberDetails = mapper.readValue(message, MemberDetails.class);
					fetchImageResponse.setMemberDetails(memberDetails);
				}

			} else {
				logger.warn("Fetch member image failed.");
				fetchImageResponse.setErrorString(error);
			}

		} catch (Exception e) {
			logger.error("Exception during member image response parsing: {}", e.getMessage(), e);
			fetchImageResponse.setErrorString("Invalid CBS Error");
		}

		return fetchImageResponse;
	}

	public CbsResponseMapper spotcash_lock_account_call(String accountNumberString, String idNumberString) {
		logger.info("Lock account no: {}", accountNumberString+" for Id No: "+idNumberString);
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		CbsResponseMapper cbsResponse = new CbsResponseMapper();

		Holder<String> accountNumber = new Holder<>(accountNumberString);
		Holder<String> idNumber = new Holder<>(idNumberString);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>();

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
				host);

		logger.info("Attempt for call to CBS for transaction, account locking: "+accountNumberString +"and idNumber: "+idNumberString);
		//portType.blockAccountNumber(idNumber, accountNumber, responseCode, responseMessage, errorMessage, returnValue);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, account locking: "+accountNumberString +"and idNumber: "+idNumberString);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", idNumberString);
					hashMap.put("account_number", accountNumberString);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("BlockAccountNumber", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
					logger.info("Request from Cbs for BlockAccountNumber "+request);
					String response1 = sharedFunctions.postToCbs("BlockAccountNumber", request, this.cbsUrl, this.userName, this.password);
					logger.info("Response from Cbs for BlockAccountNumber call "+response1);
					responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
					responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
				}
				else {
					portType.blockAccountNumber(idNumber, accountNumber, responseCode, responseMessage, errorMessage, returnValue);
				}
				break;
			} catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to lock account");
			cbsResponse.setResponseCode(responseCode.value);
			cbsResponse.setResponseMessage(responseMessage.value);
			logger.info("lock account response {} ", responseCode.value.trim());
			logger.info("lock account response message {} ", errorMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success lock account");
			} else {
				logger.info("Error fetch");
				cbsResponse.setErrorString(errorMessage.value);
			}
		} catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}






public CbsTransactionsResponseMapper spotcash_cbs_transactions_call(
		String phoneNumber, String startDate, String endDate,
		String startTime, String endTime) {

	AgencyBanking service = new AgencyBanking();
	AgencyBankingPort portType = service.getAgencyBankingPort();
	CbsTransactionsResponseMapper cbsresponse = new CbsTransactionsResponseMapper();

	Holder<String> agentPhoneNoHolder = new Holder<>(phoneNumber);
	Holder<String> start_dateHolder = new Holder<>(startDate);
	Holder<String> end_dateHolder = new Holder<>(endDate);
	Holder<String> start_timeHolder = new Holder<>(startTime);
	Holder<String> end_timeHolder = new Holder<>(endTime);
	Holder<String> transactionDataHolder = new Holder<>("");

	Holder<String> responseCode = new Holder<>("");
	Holder<String> responseMessage = new Holder<>("");
	Holder<String> errorMessage = new Holder<>("");

	((BindingProvider) portType).getRequestContext().put(
			BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

	logger.info("Initiating CBS transaction fetch for agent: {}", phoneNumber);

	int count = 1;
	int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
	long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

	while (count <= retryCount) {
		try {
			logger.info("Attempt #{} to CBS for agent transactions with phone: {}", count, phoneNumber);

			BigInteger useXmlTemplates = crudTransactionController
					.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
					.getUseXmlTemplate();

			ObjectMapper mapper = new ObjectMapper();

			if (BigInteger.ONE.equals(useXmlTemplates)) {

				HashMap<String, String> hashMap = new HashMap<>();
				hashMap.put("agentPhoneNumber", phoneNumber);
				hashMap.put("startDate", startDate);
				hashMap.put("endDate", endDate);
				hashMap.put("startTime", startTime);
				hashMap.put("endTime", endTime);
				SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetTransactions", this.clientId);
				String request = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
				logger.info("Request to CBS for GetTransactions call "+request);
				String response1 = sharedFunctions.postToCbs("GetTransactions", request, this.cbsUrl, this.userName, this.password);
				logger.info("Response from Cbs for GetTransactions Call "+response1);
				transactionDataHolder.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "transactionsData");

			} else if (BigInteger.valueOf(2).equals(useXmlTemplates)) {
				// === REST (BRIDGE) MODE ===
				SpVpnclientsConfigs bridgeConfig = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
				String authenticateUrl = bridgeConfig.getHostUrl() + "/authenticate";

				Map<String, String> authParams = new ObjectMapper().readValue(
						bridgeConfig.getApiParams(), new TypeReference<Map<String, String>>() {});

				String sessionId = Httpfunctions.obtainBridgeHandshake(
						authenticateUrl, authParams.get("cbsUser"), authParams.get("cbsPassword"));

				String processUrl = bridgeConfig.getHostUrl() + "/agency/GetTransactions";
				Map<String, String> requestMap = new HashMap<>();
				requestMap.put("agentPhoneNumber", phoneNumber);
				requestMap.put("startDate", startDate);
				requestMap.put("endDate", endDate);
				requestMap.put("startTime", startTime);
				requestMap.put("endTime", endTime);

				String restResponse = Httpfunctions.POSTRequestWithSession(processUrl, requestMap, sessionId);
				logger.info("CBS ZED Report response Response: {}", restResponse);




				JsonNode restRoot = mapper.readTree(restResponse);

// Get just the transactions array


// Build custom response JSON manually
				ObjectNode customJson = mapper.createObjectNode();


				customJson.put("start_timeHolder", startTime);
				customJson.put("end_timeHolder", endTime);
				// Access "transactionData"
				JsonNode transactionData = restRoot.path("transactionData");
				transactionDataHolder.value = mapper.writeValueAsString(transactionData);








			} else {
				// === DEFAULT SOAP MODE ===
				portType.getTransactions(agentPhoneNoHolder, start_dateHolder, end_dateHolder,
						start_timeHolder, end_timeHolder, transactionDataHolder);
			}

			break;

		} catch (SOAPFaultException ex) {
			logger.warn("SOAPFaultException: {}", ex.getMessage());
			if (ex.getMessage() != null && ex.getMessage().contains("locked by another user")) {
				logger.info("Retrying (attempt {} of {})", count, retryCount);
				count++;
				try {
					Thread.sleep(sleepTime);
				} catch (InterruptedException ie) {
					logger.error("Sleep interrupted: {}", ie.getMessage(), ie);
				}
			} else {
				break;
			}
		} catch (Exception ex) {
			logger.error("Exception fetching transactions: {}", ex.getMessage(), ex);
			break;
		}
	}

	// Parse CBS transaction data
	try {
		String rawData = StringUtils.trimToEmpty(transactionDataHolder.value);
		if (!rawData.isEmpty()) {
			logger.info("Parsing transaction data");

			String cleanedJson = rawData.replace("\\", "")
					.replace("?", "")
					.replace("},]", "}]");

			JSONObject jsonObject = new JSONObject(cleanedJson);
			JSONArray transList = jsonObject.optJSONArray("Transactions");

			List<Transaction> transactions = new ArrayList<>();
			if (transList != null) {
				for (int i = 0; i < transList.length(); i++) {
					JSONObject jObject = transList.getJSONObject(i);
					Transaction trans = new Transaction();
					trans.setCustomerPhoneNo(jObject.optString("customerPhoneNo"));
					trans.setAmount(jObject.optString("amount"));
					trans.setDescription(jObject.optString("description"));
					trans.setPostingDate(jObject.optString("postingDate"));
					trans.setTransactionID(jObject.optString("transactionID"));
					trans.setCustomerNo(jObject.optString("customerNo"));
					trans.setCustomerName(jObject.optString("customerName"));
					trans.setPostingTime(jObject.optString("postingTime"));
					transactions.add(trans);
				}
			}

			TransactionsReport report = new TransactionsReport();
			report.setTransactions(transactions);
			cbsresponse.setTransactions(report);
			cbsresponse.setErrorString("no error");
			cbsresponse.setResponseCode("000");

		} else {
			logger.warn("No transaction data returned");
			cbsresponse.setResponseCode("xxx");
			cbsresponse.setErrorString("No Results Found");
			cbsresponse.setTransactions(null);
		}

	} catch (Exception e) {
		logger.error("Exception while parsing transaction data: {}", e.getMessage(), e);
		cbsresponse.setErrorString(e.getMessage());
	}

	return cbsresponse;
}


	public CbsNonMemberAccountsResponseMapper spotcash_cbs_nmaccounts_call(String clientIDString) {
		logger.info("sp call non member accounts with clientId {}", clientIDString);
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		CbsNonMemberAccountsResponseMapper response = new CbsNonMemberAccountsResponseMapper();

		Holder<String> clientID = new Holder<>(clientIDString);
		Holder<String> responseMessage = new Holder<>("");
		Holder<Integer> returnValue = new Holder<>();

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		logger.info("Attempt for call to CBS for transaction of fetching non member accounts with clientId "+clientIDString);
		//portType.getNonMemberAccounts(clientID, responseMessage, returnValue);

		// Implementation to check for table locking.
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction of fetching non member accounts with clientId "+clientIDString);
				portType.getNonMemberAccounts(clientID, responseMessage, returnValue);
				break;
			} catch (SOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
		}

		try {
			logger.info("attempting to fetch non_member accounts");
			logger.info("fetch accounts response message {} ");
			logger.info("Success fetch");
			ObjectMapper mapper = new ObjectMapper();
			response.setAccountDetails(mapper.readValue(responseMessage.value, NonMemberAccounts.class));
			response.setResponseCode("000");
			response.setErrorString("Accounts not Found");
			logger.info("Result" +responseMessage.value);

		} catch (Exception e) {
			logger.warn("Exception thrown {} ", e);
		}
		return response;
	}





	public ReversalResponseMapper reverse_cbs_transaction(SpTransTempTable trx, SpTransTempTable originalTransaction) {
		String trxId = originalTransaction.getTrxId();
		logger.info("Initiating reversal for transaction ID: {}", trxId);

		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		ReversalResponseMapper cbsresponse = new ReversalResponseMapper();

		// Safely initialize holders
		Holder<String> requestID = new Holder<>(StringUtils.defaultIfBlank(trx.getCbsDocNo(), trx.getTrxId()));
		Holder<String> refNo = new Holder<>(trxId);
		Holder<String> phoneNo = new Holder<>(originalTransaction.getMsisdn());
		Holder<String> transactionType = new Holder<>(originalTransaction.getServiceId().toString());
		Holder<String> accountNumber = new Holder<>(StringUtils.defaultIfBlank(trx.getAccountNumber(), "0"));
		Holder<String> agentPhoneNo = new Holder<>(StringUtils.defaultIfBlank(trx.getAgentPhoneNumber(), "0"));

		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		((BindingProvider) portType).getRequestContext().put(
				BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt #{} to reverse transaction ID: {}", count, trxId);

				BigInteger useXmlTemplates = crudTransactionController
						.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue()))
						.getUseXmlTemplate();

				ObjectMapper mapper = new ObjectMapper();

				if (BigInteger.ONE.equals(useXmlTemplates)) {
					// XML TEMPLATE MODE
					HashMap<String, String> map = new HashMap<>();
					map.put("transactionId", trx.getTrxId());
					map.put("msisdn", originalTransaction.getMsisdn());
					map.put("reference", trxId);
					map.put("serviceId", originalTransaction.getServiceId().toString());
					map.put("account_number", trx.getAccountNumber());
					map.put("agentPhoneNumber", trx.getAgentPhoneNumber());

					SpXmlTemplates template = crudTransactionController.fetchXmlTemplate("ProcessReversal", this.clientId);
					String request = sharedFunctions.xmlRequestFormatter(template.getRequestXml(), map);
					logger.info("CBS Reversal XML Request: {}", request);

					String response = sharedFunctions.postToCbs("ProcessReversal", request, cbsUrl, userName, password);
					logger.info("CBS XML Response: {}", response);

					responseCode.value = sharedFunctions.responseFormatter(template, response, "responseCode");
					responseMessage.value = sharedFunctions.responseFormatter(template, response, "responseMessage");

				} else if (BigInteger.valueOf(2).equals(useXmlTemplates)) {
					// REST (BRIDGE) MODE
					SpVpnclientsConfigs bridgeConfig = crudTransactionController.fetchClientAppBridgeConfiguration(this.clientId);
					String authenticateUrl = bridgeConfig.getHostUrl() + "/authenticate";

					Map<String, String> authParams = mapper.readValue(
							bridgeConfig.getApiParams(), new TypeReference<Map<String, String>>() {});

					String sessionId = Httpfunctions.obtainBridgeHandshake(
							authenticateUrl, authParams.get("cbsUser"), authParams.get("cbsPassword"));

					String processUrl = bridgeConfig.getHostUrl() + "/agency/ProcessReversal"; // ✅ FIXED endpoint
					Map<String, String> requestMap = new HashMap<>();
					requestMap.put("transactionId", trx.getTrxId());
					requestMap.put("msisdn", originalTransaction.getMsisdn());
					requestMap.put("reference", trxId);
					requestMap.put("serviceId", originalTransaction.getServiceId().toString());
					requestMap.put("account_number", trx.getAccountNumber());
					requestMap.put("agentPhoneNumber", trx.getAgentPhoneNumber());

					String restResponse = Httpfunctions.POSTRequestWithSession(processUrl, requestMap, sessionId);
					logger.info("CBS REST Response: {}", restResponse);

					AgencyResponseData responseData = mapper.readValue(restResponse, AgencyResponseData.class);
					responseCode.value = String.valueOf(responseData.getReturnValue());
					responseMessage.value = mapper.writeValueAsString(responseData.getResponseMessage());

				} else {
					// DEFAULT SOAP
					portType.processReversal(requestID, refNo, phoneNo, transactionType,
							accountNumber, responseCode, responseMessage, agentPhoneNo);
				}

				break;

			} catch (SOAPFaultException ex) {
				logger.warn("SOAPFaultException: {}", ex.getMessage());
				if (ex.getMessage() != null && ex.getMessage().contains("locked by another user")) {
					count++;
					logger.info("Retrying (attempt {} of {})", count, retryCount);
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException ie) {
						logger.error("Interrupted during retry sleep: {}", ie.getMessage(), ie);
					}
				} else {
					break;
				}
			} catch (Exception ex) {
				logger.error("Exception during reversal call: {}", ex.getMessage(), ex);
				break;
			}
		}

		try {
			logger.info("Parsing CBS reversal response for trxId: {}", trxId);
			cbsresponse.setTransactionId(requestID.value);
			cbsresponse.setResponseCode(responseCode.value);

			String code = StringUtils.trimToEmpty(responseCode.value);
			String message = StringUtils.trimToEmpty(responseMessage.value);

			if (ResponseCodes.SUCCESS.getResponseCode().equals(code) ||
					ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(code)) {
				logger.info("Reversal succeeded for trxId: {}", trxId);
			} else {
				logger.warn("Reversal failed. Message: {}", message);
				cbsresponse.setResponseMessage(message);
			}

		} catch (Exception e) {
			logger.error("Exception parsing reversal response: {}", e.getMessage(), e);
		}

		return cbsresponse;
	}




	public GetMemberCbsResponse getMember(GetMemberRequest request) {
		logger.info("fetch member image for event authentication with id no {} ", request.getClientId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetMemberCbsResponse cbsResponse = new GetMemberCbsResponse();

		//CBS Request
		Holder<String> idNumber = new Holder<>(request.getIdNumber());
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, fetching member details with ID: "+request.getIdNumber());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", request.getIdNumber());
					hashMap.put("eventId", request.getEventId());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("FetchMemberDetails", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for FetchMemberDetails call " +request);
						String response1 = sharedFunctions.postToCbs("FetchMemberDetails", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for FetchMemberDetails call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
						}
					}
				}
				else portType.getMember(idNumber, eventId, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch member image");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("fetch member image response code: {}", responseCode.value.trim());
			//logger.info("fetch member image response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching member Details.......");
				cbsResponse.setResponseMessage(new Gson().fromJson(responseMessage.value, MemberDetailsCbsResponse.class));
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to fetch member from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public GetMemberCbsResponse getMemberImage(GetMemberRequest request) {
		logger.info("fetch member image for event authentication with id no {} ", request.getClientId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetMemberCbsResponse cbsResponse = new GetMemberCbsResponse();

		//CBS Request
		Holder<String> idNumber = new Holder<>(request.getIdNumber());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> errorMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, GetImage with ID: "+request.getIdNumber());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", request.getIdNumber());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetImage", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for GetImage call " +request);
						String response1 = sharedFunctions.postToCbs("FetchMemberImage", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for GetMemberImage call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
						}
					}
				}
				else portType.getMemberImage(idNumber, responseCode, responseMessage, errorMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch member image");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("fetch member image response code: {}", responseCode.value.trim());
			//logger.info("fetch member image response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching member Image.......");
				cbsResponse.setResponseMessage(new Gson().fromJson(responseMessage.value, MemberDetailsCbsResponse.class));
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to fetch member from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public GetEventsCbsResponse getEvents(GetEventsRequest request) {
		logger.info("fetching events for clientID {} ", request.getClientId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetEventsCbsResponse cbsResponse = new GetEventsCbsResponse();

		//CBS Request
		String storeUserMsisdn = crudTransactionController.fetchStoreUserMsisdn(request.getStoreUserId());
		Holder<String> devicePhoneNo = new Holder<>(storeUserMsisdn);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, fetching events for client id: "+request.getClientId());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("FetchEvents", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for FetchEvents call " +request);
						String response1 = sharedFunctions.postToCbs("FetchEvents", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for GetMemberImage call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
						}
					}
				}
				else portType.getEvents(devicePhoneNo, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch events from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("fetch events response {} ", responseCode.value.trim());
			logger.info("fetch events response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching member Details.......");
				logger.info("This is the Json data qazscw " + new Gson().fromJson(responseMessage.value, EventsCbsResponse.class) );
				cbsResponse.setResponseMessage(new Gson().fromJson(responseMessage.value, EventsCbsResponse.class));
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to fetch member from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}




//	public ValidateEventsResponse validateEventCode(ValidateEventCodeRequest request) {
//		logger.info("Attempting Events Validation for clientID {}", request.getClientId());
//
//		AgencyBanking service = new AgencyBanking();
//		AgencyBankingPort portType = service.getAgencyBankingPort();
//		ValidateEventsResponse cbsResponse = new ValidateEventsResponse();
//
//
//		Holder<String> idNumber = new Holder<>(request.getIdNumber());
//		Holder<String> eventId = new Holder<>(request.getEventId());
//		Holder<String> eventCode = new Holder<>(request.getEventCode());
//		String storeUserMsisdn = crudTransactionController.fetchStoreUserMsisdn(request.getStoreUserId());
//		Holder<String> devicePhoneNo = new Holder<>(storeUserMsisdn);
//		Holder<String> responseCode = new Holder<>("");
//
//		Holder<String> responseMessage = new Holder<>("");
//
//		// Set SOAP endpoint
//		BindingProvider bindingProvider = (BindingProvider) portType;
//		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);
//
//		// Retry config
//		int count = 1;
//		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
//		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));
//
//		while (count <= retryCount) {
//			try {
//				logger.info("Attempt number {} for event code validation, clientID {}", count, request.getClientId());
//
//				boolean useXmlTemplates = crudTransactionController
//						.fetchclient_type(new BigDecimal(request.getClientId()))
//						.getUseXmlTemplate()
//						.compareTo(BigInteger.ONE) == 0;
//
//				if (useXmlTemplates) {
//					// XML Template and Bridge config
//					HashMap<String, String> hashMap = new HashMap<>();
//					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ValidateMemberRegistered", request.getClientId());
//					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(request.getClientId());
//					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);
//
//					if ("0".equals(bridgeConfigs.getBridgeConfigs().toString())) {
//						logger.info("Sending request directly to CBS");
//						String response = sharedFunctions.postToCbs("ValidateMemberRegistered", cbsRequest, cbsUrl, userName, password);
//						logger.info("Response from CBS: {}", response);
//						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseMessage");
//						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseCode");
//					} else {
//						// Encrypted bridge route
//						logger.info("Sending encrypted request via bridge");
//						RestTemplate restTemplate = new RestTemplate();
//						HttpHeaders headers = new HttpHeaders();
//						headers.setContentType(MediaType.TEXT_XML);
//
//						String encryptedRequest = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
//						HttpEntity<String> entity = new HttpEntity<>(encryptedRequest, headers);
//						ResponseEntity<String> responseEntity = restTemplate.exchange(
//								bridgeConfigs.getHostUrl(), HttpMethod.POST, entity, String.class
//						);
//
//						String encryptedResponse = responseEntity.getBody();
//						if (encryptedResponse != null && !encryptedResponse.isEmpty()) {
//							String decrypted = Crypt.decrypt(encryptedResponse, environment.getRequiredProperty("bridgeEncryptionKey.key"));
//							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, decrypted, "responseMessage");
//							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, decrypted, "responseCode");
//						} else {
//							logger.warn("Empty response received from bridge.");
//						}
//					}
//				} else {
//					// Standard SOAP fallback
//					portType.validateMemberRegistered( idNumber, eventId, eventCode, responseCode, responseMessage);
//				}
//
//				break; // Success - exit retry loop
//
//			} catch (ServerSOAPFaultException soapEx) {
//				logger.warn("SOAP Fault: {}", soapEx.getMessage());
//				if (soapEx.getMessage().contains("locked by another user")) {
//					count++;
//					try {
//						Thread.sleep(sleepTime);
//					} catch (InterruptedException ie) {
//						logger.error("Sleep interrupted", ie);
//						Thread.currentThread().interrupt(); // preserve interrupt status
//					}
//				} else {
//					break; // non-retryable SOAP error
//				}
//			} catch (Exception e) {
//				logger.error("Unexpected exception during CBS event validation", e);
//				throw new RuntimeException("CBS validation failed", e);
//			}
//		}
//
//		// Prepare final response
//		try {
//			logger.info("Processing CBS response: code={}, message={}", responseCode.value, responseMessage.value);
//			cbsResponse.setResponseCode(responseCode.value);
//
//			if (ResponseCodes.SUCCESS.getResponseCode().equals(responseCode.value.trim()) ||
//					ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(responseCode.value.trim())) {
//
//				logger.info("Successful response from CBS");
//				ValidateEventsResponse parsedResponse = new Gson().fromJson(responseMessage.value, ValidateEventsResponse.class);
//				cbsResponse.setResponseMessage(String.valueOf(parsedResponse));
//			} else {
//				logger.warn("CBS responded with error code {}", responseCode.value);
//				cbsResponse.setResponseMessage(responseMessage.value);
//			}
//		} catch (Exception e) {
//			logger.error("Failed to parse CBS response", e);
//			cbsResponse.setResponseMessage("Failed to fetch member from CBS");
//		}
//
//		return cbsResponse;
//	}




	public ValidateEventsResponse validateEventCode(ValidateEventCodeRequest request) {
		logger.info("Attempting Events Validation for clientID {}", request.getClientId());

		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		ValidateEventsResponse cbsResponse = new ValidateEventsResponse();

		Holder<String> idNumber = new Holder<>(request.getIdNumber());
		Holder<String> eventNo = new Holder<>(request.getEventId());

		Holder<String> eventCode = new Holder<>(request.getEventCode());
		Holder<String> phoneNumber = new Holder<>(request.getPhoneNumber());
		String storeUserMsisdn = crudTransactionController.fetchStoreUserMsisdn(request.getStoreUserId());
		Holder<String> devicePhoneNo = new Holder<>(storeUserMsisdn);
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		Holder<String> agentPhoneNumber = new Holder<>(request.getAgentPhoneNumber());

		// Set SOAP endpoint
		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Retry configuration
		int count = 1;
		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number {} for event code validation, clientID {}", count, request.getClientId());

				boolean useXmlTemplates = crudTransactionController
						.fetchclient_type(new BigDecimal(request.getClientId()))
						.getUseXmlTemplate()
						.compareTo(BigInteger.ONE) == 0;

				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("validateMember", request.getClientId());
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(request.getClientId());
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if ("0".equals(bridgeConfigs.getBridgeConfigs().toString())) {
						logger.info("Sending request directly to CBS");
						String response = sharedFunctions.postToCbs("validateMember", cbsRequest, cbsUrl, userName, password);
						logger.info("Response from CBS: {}", response);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseCode");
					} else {
						logger.info("Sending encrypted request via bridge");
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);

						String encryptedRequest = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptedRequest, headers);
						ResponseEntity<String> responseEntity = restTemplate.exchange(
								bridgeConfigs.getHostUrl(), HttpMethod.POST, entity, String.class
						);

						String encryptedResponse = responseEntity.getBody();
						if (encryptedResponse != null && !encryptedResponse.isEmpty()) {
							String decrypted = Crypt.decrypt(encryptedResponse, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, decrypted, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, decrypted, "responseCode");
						} else {
							logger.warn("Empty response received from bridge.");
						}
					}
				} else {
					portType.validateMember( agentPhoneNumber,phoneNumber, eventNo, eventCode, responseCode, responseMessage);
				}
				break;
			} catch (ServerSOAPFaultException soapEx) {
				logger.warn("SOAP Fault: {}", soapEx.getMessage());
				if (soapEx.getMessage().contains("locked by another user")) {
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException ie) {
						logger.error("Sleep interrupted", ie);
						Thread.currentThread().interrupt();
					}
				} else {
					break;
				}
			} catch (Exception e) {
				logger.error("Unexpected exception during CBS event validation", e);
				throw new RuntimeException("CBS validation failed", e);
			}
		}

		// CBS response handling (simplified, no JSON parsing)
		try {
			logger.info("Processing CBS response: code={}, message={}", responseCode.value, responseMessage.value);
			cbsResponse.setResponseCode(responseCode.value);

			if (ResponseCodes.SUCCESS.getResponseCode().equals(responseCode.value.trim()) ||
					ResponseCodes.STANDARD_SUCCESS.getResponseCode().equals(responseCode.value.trim())) {

				logger.info("Successful code validation response from CBS");
				cbsResponse.setResponseMessage(responseMessage.value);  // Plain string, no JSON parsing
			} else {
				logger.warn("CBS responded with error code {}", responseCode.value);
				cbsResponse.setResponseMessage(responseMessage.value);
			}
		} catch (Exception e) {
			logger.error("Failed to handle CBS response", e);
			cbsResponse.setResponseMessage("Failed to fetch member from CBS");
			cbsResponse.setResponseCode("99");
		}

		return cbsResponse;
	}



	public EventRegistrationCbsResponse registerMemberToEvent(RegisterMemberRequest request) {
		logger.info("Registering member with ID number: {}, to eventID: {}, for clientID: {} ",
				request.getIdNumber(), request.getEventId(), request.getClientId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		EventRegistrationCbsResponse cbsResponse = new EventRegistrationCbsResponse();

		String storeUserMsisdn = crudTransactionController.fetchStoreUserMsisdn(request.getStoreUserId());

		//CBS Request
		Holder<String> idNumber = new Holder<>(request.getIdNumber());
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> authenticationMode = new Holder<>(request.getAuthenticationMode());
		Holder<String> agentDevicePhone = new Holder<>(storeUserMsisdn);
		Holder<String> regComment = new Holder<>(request.getRegComment());
		Holder<String> disbursementMethod = new Holder<>("");
		String disbursement = request.getDisbursementMethod();
		String value = "";
		if(disbursement != null) {
			if (disbursement.equalsIgnoreCase("Mpesa")) {
				value = "2";
			} else if (disbursement.equalsIgnoreCase("FOSA")) {
				value = "1";
			} else if (disbursement.equalsIgnoreCase("Cash")) {
				value = "0";
			}
			disbursementMethod = new Holder<>(value);
		}
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, event registration for client id: "+request.getClientId());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", request.getIdNumber());
					hashMap.put("eventId", request.getEventId());
					hashMap.put("authenticationMode", request.getAuthenticationMode());
					hashMap.put("agentDevicePhone", storeUserMsisdn);
					hashMap.put("disbursementMethods", disbursementMethod.toString());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("RegisterMember", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for RegisterMember call " +request);
						String response1 = sharedFunctions.postToCbs("RegisterMember", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for RegisterMember call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
						eventId.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "eventId");
						idNumber.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "idNumber");
						authenticationMode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "authenticationMode");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
							eventId.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "eventId");
							authenticationMode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "authenticationMode");
						}
					}
				}
				else portType.registerMemberToEvent(idNumber, authenticationMode, agentDevicePhone, eventId, regComment,disbursementMethod, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to register member to an event from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("Registering member to an event response code:{} ", responseCode.value.trim());
			logger.info("Registering member to an event response message:{} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success registering member to event.......");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
			else {
				logger.info("Registration Failed");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setResponseMessage("Failed to Register member to an event.");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public EventRegistrationCbsResponse registerNonMemberToEvent(RegisterNonMemberRequest request) {
		logger.info("Registering member with ID number: {}, to eventID: {}, for clientID: {} ",
				request.getIdNumber(), request.getEventId(), request.getClientId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		EventRegistrationCbsResponse cbsResponse = new EventRegistrationCbsResponse();

		String storeUserMsisdn = crudTransactionController.fetchStoreUserMsisdn(request.getStoreUserId());

		//CBS Request
		Holder<String> name = new Holder<>(request.getName());
		Holder<String> idNumber = new Holder<>(request.getIdNumber());
		Holder<String> phoneNumber = new Holder<>(request.getPhoneNumber());
		Holder<String> gender = new Holder<>(request.getGender());
		Holder<String> devicePhoneNo = new Holder<>(storeUserMsisdn);
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for transaction, event registration for client id: "+request.getClientId());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", request.getIdNumber());
					hashMap.put("eventId", request.getEventId());
					hashMap.put("phoneNumber", request.getPhoneNumber());
					hashMap.put("devicePhoneNo", storeUserMsisdn);
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("RegisterNonMember", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for RegisterMember call " +request);
						String response1 = sharedFunctions.postToCbs("RegisterNonMember", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for RegisterNonMember call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
						name.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "name");
						idNumber.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "idNumber");
						phoneNumber.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "phoneNumber");
						gender.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "gender");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
							name.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "name");
							idNumber.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "idNumber");
							phoneNumber.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "phoneNumber");
							gender.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "gender");
						}
					}
				}
				else portType.registerNonMemberToEvent(name,idNumber, phoneNumber,gender, eventId,devicePhoneNo, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to register non member to an event from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("Registering non member to an event response code:{} ", responseCode.value.trim());
			logger.info("Registering non member to an event response message:{} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success registering non member to event.......");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
			else {
				logger.info("Registration Failed");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setResponseMessage("Failed to Register non member to an event.");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public RedeemItemCbsResponse redeemableItems(RedeemItemsRequest request) {
		logger.info("fetching redeemable items for event id {} ", request.getEventId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		RedeemItemCbsResponse cbsResponse = new RedeemItemCbsResponse();

		//CBS Request
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> idNumber = new Holder<>(request.getCustomerId());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for redeemable items for event id: "+request.getEventId());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;

				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("eventId", request.getEventId());
					hashMap.put("idNumber", request.getCustomerId());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("RedeemRewards", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")) {
						logger.info("Request to CBS for RedeemRewards call " +request);
						String response1 = sharedFunctions.postToCbs("RedeemRewards", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for RedeemRewards call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String res = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> response = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						res =  response.getBody();
						if (res != null && !res.isEmpty()) {
							res = Crypt.decrypt(res, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseCode");
						}
					}
				}
				else portType.RedeemRewards(eventId, idNumber, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch redeemable items from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("fetch redeemable items response code {} ", responseCode.value.trim());
			logger.info("fetch redeemable items response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching items to redeem...");
				cbsResponse.setResponseMessage(new Gson().fromJson(responseMessage.value, RedeemsCbsResponse.class));
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to fetch redeemable items from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public GetCustomerRewardsCbsResponse selectedCustomerRewards(RedeemItem redeemItem, String clientId, String eventId, String customerId,String transactionId) {
		logger.info("Selected customer rewards for event id {} ", eventId);
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetCustomerRewardsCbsResponse cbsResponse = new GetCustomerRewardsCbsResponse();
		Holder<String> disbursementMethod = new Holder<>("");
		Holder<String> trxId = new Holder<>("");
		//CBS Request
		Holder<String> event_id = new Holder<>(eventId);
		Holder<String> idNumber = new Holder<>(customerId);
		Holder<Double> quantity = new Holder<>(redeemItem.getQuantity());
		Holder<String> rewardName = new Holder<>(redeemItem.getRewardName());
		String disbursement = redeemItem.getDisbursementMethod();
		if (transactionId !=null){
			trxId = new Holder<>(transactionId);
		}
		String value = "";
		if(disbursement != null) {
			if (disbursement.equalsIgnoreCase("Mpesa")) {
				value = "2";
			} else if (disbursement.equalsIgnoreCase("FOSA")) {
				value = "1";
			} else if (disbursement.equalsIgnoreCase("Cash")) {
				value = "0";
			}
			disbursementMethod = new Holder<>(value);
		}

		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));


		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS to redeem selected customer rewards for event id: "+eventId);
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;

				if (useXmlTemplates) {

					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("idNumber", customerId);
					hashMap.put("event_id", eventId);
					hashMap.put("quantity", String.valueOf(redeemItem.getQuantity()));
					hashMap.put("rewardName", redeemItem.getRewardName());
					hashMap.put("disbursementMethod", disbursementMethod.toString());
					hashMap.put("trxId", String.valueOf(trxId));
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("RedeemItemsSelected", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for RedeemItemsSelected call " );
						String response1 = sharedFunctions.postToCbs("RedeemItemsSelected", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for RedeemItemsSelected call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String response = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						response =  respnse.getBody();
						if (response != null && !response.isEmpty()) {
							response = Crypt.decrypt(response, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response, "responseCode");
						}
					}
				}
				else portType.RedeemItemsSelected(event_id, idNumber, quantity, rewardName,disbursementMethod,trxId, responseCode, responseMessage);
				break;
			} catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to redeem item...."+rewardName.value);
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("redeeming item response code {} ", responseCode.value.trim());
			logger.info("redeeming item response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Redeemed customer rewards successfully.......");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
			else {
				logger.info("Error fetch", cbsResponse.getErrorMessage());
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to redeem selected customer rewards");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	//fetch redeemed customer rewards
	public GetRedeemedCustomerRewardsCbsResponse redeemedCustomerRewards(RedeemedCustomerRewardsRequest request) {
		logger.info("fetching redeemed rewards for customer id ", request.getCustomerId());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetRedeemedCustomerRewardsCbsResponse cbsResponse = new GetRedeemedCustomerRewardsCbsResponse();

		//CBS Request
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> idNumber = new Holder<>(request.getCustomerId());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for redeemed rewards for event id: "+request.getEventId());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;

				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("eventId", request.getEventId());
					hashMap.put("idNumber", request.getCustomerId());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("ViewDetails", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")) {
						logger.info("Request to CBS for ViewDetails call " +request);
						String response1 = sharedFunctions.postToCbs("ViewDetails", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for ViewDetails call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String res = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> response = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						res =  response.getBody();
						if (res != null && !res.isEmpty()) {
							res = Crypt.decrypt(res, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseCode");
						}
					}
				}
				else portType.ViewDetails(eventId, idNumber, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch redeemed rewards from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("redeemed customer rewards response code {} ", responseCode.value.trim());
			logger.info("redeemed customer rewards response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Fetching redeemed customer rewards successful");
				cbsResponse.setResponseMessage(new Gson().fromJson(responseMessage.value, RedeemedCustomerRewardsCbsResponse.class));
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setErrorMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setErrorMessage("Failed to fetch redeemed rewards from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public FetchMemberByMpesaCbsResponse fetchMemberByMpesaCbsResponse(FetchMpesaMembersRequest request) {
		logger.info("Fetching mpesa members ");
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		FetchMemberByMpesaCbsResponse cbsResponse = new FetchMemberByMpesaCbsResponse();


		//CBS Request
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");
		Holder<String> newMembersPayload = new Holder<>("");


		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for fetch mpesa members ");
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("newMembersPayload", "");
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("FetchMemberByMpesa", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for RegisterMember call " +request);
						String response1 = sharedFunctions.postToCbs("FetchMemberByMpesa", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for RegisterMember call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
						newMembersPayload.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "newMembersPayload");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
							newMembersPayload.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "newMembersPayload");
						}
					}
				}
				else portType.FetchMemberByMpesa(responseCode, responseMessage, newMembersPayload);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch mpesa member  from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("Fetching mpesa member  response code:{} ", responseCode.value.trim());
			logger.info("Fetching Mpesa member   response message:{} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching mpesa members.......");
				cbsResponse.setResponseMessage(responseMessage.value);
				if (newMembersPayload.value.trim().length() > 0) {
					logger.info("Transactions Success fetch");
					ObjectMapper mapper = new ObjectMapper();
					String jsonResponse = (newMembersPayload.value).replace("\\", "").replace("?","").replace("},]","}]");
					JSONObject jsonObject = new JSONObject(jsonResponse);
					NewMembersPayload newMembersPayload1 = new NewMembersPayload();
					List<NewMembersMpesa> newMembersMpesa  = new ArrayList<NewMembersMpesa>();
					JSONArray newMembersMpesaList = (JSONArray) jsonObject.get("fetchMpesaMembers");
					for(int i = 0; i < newMembersMpesaList.length(); i ++){
						JSONObject jObject = (JSONObject) newMembersMpesaList.get(i);
						NewMembersMpesa newMembersMpesa1  = new NewMembersMpesa();
						newMembersMpesa1.setMemberNo(jObject.get("memberNo").toString());
						newMembersMpesa1.setMemberName(jObject.get("memberName").toString());
						newMembersMpesa1.setAmount(jObject.get("amount").toString());
						newMembersMpesa1.setEventName(jObject.get("eventName").toString());
						newMembersMpesa1.setEventId(jObject.get("eventId").toString());
						newMembersMpesa1.setPhoneNo(jObject.get("phoneNo").toString());
						newMembersMpesa1.setDocumentNo(jObject.getString("documentNo").toString());
						newMembersMpesa.add(newMembersMpesa1);
					}
					newMembersPayload1.setNewMembersMpesa(newMembersMpesa);
					cbsResponse.setNewMembersPayload(newMembersPayload1);
					cbsResponse.setResponseMessage("no error");
					cbsResponse.setResponseCode("00");
				} else {
					logger.info("Null Transactions fetch");
					ObjectMapper mapper = new ObjectMapper();
					cbsResponse.setResponseCode("xxx");
					cbsResponse.setResponseMessage("No Results Found");
					cbsResponse.setNewMembersPayload(null);
				}
			}
			else {
				logger.info("Registration Failed");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setResponseMessage("Failed to Register member to an event.");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

	public FetchTranscationIdResponse fetchTranscationIdResponse(FetchTranscationIdRequest request) {
		logger.info("Fetching mpesa members ");
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		FetchTranscationIdResponse cbsResponse = new FetchTranscationIdResponse();


		//CBS Request
		String requestTrxIdtrxId = request.getTrxId();
		String requestPhoneNo = request.getPhoneNo();
		Holder<String> phoneNo = new Holder<>(requestPhoneNo);
		Holder<String> trxId = new Holder<>(requestTrxIdtrxId);
		Holder<String> paymentReference = new Holder<>(request.getPaymentReference());
		Holder<String> eventId = new Holder<>(request.getEventId());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");


		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt number " + count + " for call to CBS for post completed mpesa transactions ");
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;
				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("phoneNo", phoneNo.value);
					hashMap.put("trxId", trxId.toString());
					hashMap.put("paymentReference", request.getPaymentReference());
					hashMap.put("responseCode", "");
					hashMap.put("responseMessage", "");
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("FetchTranscationId", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")){
						logger.info("Request to CBS for post completed mpesa transactions call " +request);
						String response1 = sharedFunctions.postToCbs("FetchTranscationId", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for completed mpesa transactions call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String respons = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> respnse = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						respons =  respnse.getBody();
						if (respons != null && !respons.isEmpty()) {
							respons = Crypt.decrypt(respons, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, respons, "responseCode");
						}
					}
				}
				else portType.FetchTranscationId(phoneNo, trxId, paymentReference, eventId, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch mpesa completed mpesa transaction   from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("Fetching mpesa completed mpesa transaction  response code:{} ", responseCode.value.trim());
			logger.info("Fetching mpesa completed mpesa transaction   response message:{} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Success fetching  completed mpesa transaction .......");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
			else {
				logger.info(" Failed to fetch mpesa completed mpesa transaction ");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setResponseMessage("Failed to Register member to an event.");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}
	public GetServiceChargeCbsResponse getServiceChargeResponse(ServiceChargeRequest request) {
		logger.info("fetching service  charges for customer phone ", request.getCustomerPhoneNumber());
		AgencyBanking service = new AgencyBanking();
		AgencyBankingPort portType = service.getAgencyBankingPort();
		GetServiceChargeCbsResponse cbsResponse = new GetServiceChargeCbsResponse();

		//CBS Request
		Holder<String> phoneNo = new Holder<>(request.getCustomerPhoneNumber());
		Holder<String> amount = new Holder<>(request.getAmount());
		Holder<String> transactionType = new Holder<>(request.getTransactionType());
		Holder<String> responseCode = new Holder<>("");
		Holder<String> responseMessage = new Holder<>("");

		BindingProvider bindingProvider = (BindingProvider) portType;
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, host);

		// Implementation to check for table locking.
		int count = 1;

		int retryCount = Integer.parseInt(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableCount"));
		long sleepTime = Long.parseLong(environment.getRequiredProperty("datasource.spotcash.retryCbsTrxLockedTableSleepTime"));

		while (count <= retryCount) {
			try {
				logger.info("Attempt service charge number " + count + " for call to CBS for redeemed rewards for event id: "+request.getCustomerPhoneNumber());
				useXmlTemplates = crudTransactionController.fetchclient_type(BigDecimal.valueOf(this.clientId.longValue())).getUseXmlTemplate().compareTo(BigInteger.ONE) == 0;

				if (useXmlTemplates) {
					HashMap<String, String> hashMap = new HashMap<>();
					hashMap.put("phoneNo", request.getCustomerPhoneNumber());
					hashMap.put("amount", String.valueOf(request.getAmount()));
					hashMap.put("transcationType", request.getTransactionType());
					SpXmlTemplates spXmlTemplates = crudTransactionController.fetchXmlTemplate("GetServiceCharge", this.clientId);
					SpVpnclientsConfigs bridgeConfigs = crudTransactionController.fetch_vpn_configs(this.clientId);
					String cbsRequest = sharedFunctions.xmlRequestFormatter(spXmlTemplates.getRequestXml(), hashMap);

					if(bridgeConfigs.getBridgeConfigs().toString().equalsIgnoreCase("0")) {
						logger.info("Request to CBS for GetServiceCharge call " +request);
						String response1 = sharedFunctions.postToCbs("GetServiceCharge", cbsRequest, this.cbsUrl, this.userName, this.password);
						logger.info("Response from Cbs for GetServiceCharge call "+response1);
						responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseMessage");
						responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, response1, "responseCode");
					}
					else {
						String res = "";
						RestTemplate restTemplate = new RestTemplate();
						HttpHeaders headers = new HttpHeaders();
						headers.setContentType(MediaType.TEXT_XML);
						// Encrypt and return the response
						String encryptRequestMap = Crypt.encrypt(cbsRequest, sharedFunctions.getSecretKey(environment));
						HttpEntity<String> entity = new HttpEntity<>(encryptRequestMap, headers);
						ResponseEntity<String> response = restTemplate.exchange(bridgeConfigs.getHostUrl() , HttpMethod.POST, entity, String.class);

						res =  response.getBody();
						if (res != null && !res.isEmpty()) {
							res = Crypt.decrypt(res, environment.getRequiredProperty("bridgeEncryptionKey.key"));
							responseMessage.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseMessage");
							responseCode.value = sharedFunctions.responseFormatter(spXmlTemplates, res, "responseCode");
						}
					}
				}
				else portType.getServiceCharge(phoneNo, amount, transactionType, responseCode, responseMessage);
				break;
			}
			catch (ServerSOAPFaultException serverSOAPFaultException) {
				logger.info("Exception Message -- "  + serverSOAPFaultException.getMessage());
				if(serverSOAPFaultException.getMessage().contains("locked by another user")){
					logger.info("Count is at " + count);
					count++;
					try {
						Thread.sleep(sleepTime);
					} catch (InterruptedException e) {
						e.printStackTrace();
						logger.error("Exception Message -- " + e.getMessage());
					}
				} else {
					break;
				}
			}
			catch (Exception e) { throw new RuntimeException(e); }
		}

		try {
			logger.info("attempting to fetch service charge from CBS");
			cbsResponse.setResponseCode(responseCode.value);
			logger.info("get service charge response code {} ", responseCode.value.trim());
			logger.info("get service charge  response message {} ", responseMessage.value.trim());
			if (responseCode.value.trim().matches(ResponseCodes.SUCCESS.getResponseCode()) || responseCode.value.trim().matches(ResponseCodes.STANDARD_SUCCESS.getResponseCode())) {
				logger.info("Fetching service charge  successful");
				cbsResponse.setResponseMessage(responseMessage.value);
			}
			else {
				logger.info("Error fetch");
				cbsResponse.setResponseCode(responseMessage.value);
			}
		}
		catch (Exception e) {
			cbsResponse.setResponseCode("Failed to fetch redeemed rewards from CBS");
			logger.warn("Exception thrown {} ", e);
		}
		return cbsResponse;
	}

}
