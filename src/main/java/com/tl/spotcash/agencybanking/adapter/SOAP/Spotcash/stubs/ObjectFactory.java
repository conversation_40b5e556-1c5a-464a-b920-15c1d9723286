package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the schemas.dynamics.microsoft.codeunit.agency_banking package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 *
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: schemas.dynamics.microsoft.codeunit.agency_banking
     *
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Spotcash }
     *
     */
    public Spotcash createSpotcash() {
        return new Spotcash();
    }

    /**
     * Create an instance of {@link SpotcashResult }
     *
     */
    public SpotcashResult createSpotcashResult() {
        return new SpotcashResult();
    }

    /**
     * Create an instance of {@link CalcAvailableBalance }
     *
     */
    public CalcAvailableBalance createCalcAvailableBalance() {
        return new CalcAvailableBalance();
    }

    /**
     * Create an instance of {@link CalcAvailableBalanceResult }
     *
     */
    public CalcAvailableBalanceResult createCalcAvailableBalanceResult() {
        return new CalcAvailableBalanceResult();
    }

    /**
     * Create an instance of {@link GetMemberAccounts }
     *
     */
    public GetMemberAccounts createGetMemberAccounts() {
        return new GetMemberAccounts();
    }

    /**
     * Create an instance of {@link GetMemberAccountsResult }
     *
     */
    public GetMemberAccountsResult createGetMemberAccountsResult() {
        return new GetMemberAccountsResult();
    }

    /**
     * Create an instance of {@link MemberRegistration }
     *
     */
    public MemberRegistration createMemberRegistration() {
        return new MemberRegistration();
    }

    /**
     * Create an instance of {@link MemberRegistrationResult }
     *
     */
    public MemberRegistrationResult createMemberRegistrationResult() {
        return new MemberRegistrationResult();
    }

    /**
     * Create an instance of {@link AgentInfo }
     *
     */
    public AgentInfo createAgentInfo() {
        return new AgentInfo();
    }

    /**
     * Create an instance of {@link AgentInfoResult }
     *
     */
    public AgentInfoResult createAgentInfoResult() {
        return new AgentInfoResult();
    }

    /**
     * Create an instance of {@link MemberMinistatement }
     *
     */
    public MemberMinistatement createMemberMinistatement() {
        return new MemberMinistatement();
    }

    /**
     * Create an instance of {@link MemberMinistatementResult }
     *
     */
    public MemberMinistatementResult createMemberMinistatementResult() {
        return new MemberMinistatementResult();
    }

    /**
     * Create an instance of {@link GetMemberBalance }
     *
     */
    public GetMemberBalance createGetMemberBalance() {
        return new GetMemberBalance();
    }

    /**
     * Create an instance of {@link GetMemberBalanceResult }
     *
     */
    public GetMemberBalanceResult createGetMemberBalanceResult() {
        return new GetMemberBalanceResult();
    }

    /**
     * Create an instance of {@link ValidateAccountDetails }
     *
     */
    public ValidateAccountDetails createValidateAccountDetails() {
        return new ValidateAccountDetails();
    }

    /**
     * Create an instance of {@link ValidateAccountDetailsResult }
     *
     */
    public ValidateAccountDetailsResult createValidateAccountDetailsResult() {
        return new ValidateAccountDetailsResult();
    }

    /**
     * Create an instance of {@link GetMemberImage }
     *
     */
    public GetMemberImage createGetMemberImage() {
        return new GetMemberImage();
    }

    /**
     * Create an instance of {@link GetMemberImageResult }
     *
     */
    public GetMemberImageResult createGetMemberImageResult() {
        return new GetMemberImageResult();
    }
    /**
     * Create an instance of {@link GetNonMemberAccounts }
     *
     * @return
     */
    public GetNonMemberAccounts createGetNonMemberAccounts() {
        return new GetNonMemberAccounts();
    }

    /**
     * Create an instance of {@link GetNonMemberAccountsResult }
     *
     * @return
     */
    public GetNonMemberAccountsResult createGetNonMemberAccountsResult() {
        return new GetNonMemberAccountsResult();
    }

    /**
     * Create an instance of {@link ProcessReversal }
     *
     */
    public ProcessReversal createProcessReversal() {
        return new ProcessReversal();
    }

    /**
     * Create an instance of {@link ProcessReversalResult }
     *
     */
    public ProcessReversalResult createProcessReversalResult() {
        return new ProcessReversalResult();
    }
}