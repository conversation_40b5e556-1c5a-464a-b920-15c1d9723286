package com.tl.spotcash.agencybanking.adapter;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;

/**
 *
 * <AUTHOR>
 */
public interface CorebankingSystemInvoker {
    CorebankingResponse invokecorebankingsystem(CrudTransactionController crudTransactions, CbsRequestData vpnConfigs);
}
