package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.Holder;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.8
 * 2019-05-17T09:06:29.440+03:00
 * Generated source version: 3.1.8
 *
 */
@WebService(targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", name = "MobileBanking_Port")
@XmlSeeAlso({ObjectFactory.class})
public interface MobileBankingPort {

    @WebMethod(operationName = "Spotcash", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:Spotcash")
    @RequestWrapper(localName = "Spotcash", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.Spotcash")
    @ResponseWrapper(localName = "Spotcash_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.SpotcashResult")
    public void spotcash(
            @WebParam(mode = WebParam.Mode.INOUT, name = "request_id", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> requestId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "transaction_type", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<Integer> transactionType,
            @WebParam(mode = WebParam.Mode.INOUT, name = "amount", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> amount,
            @WebParam(mode = WebParam.Mode.INOUT, name = "trnx_charges", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> trnxCharges,
            @WebParam(mode = WebParam.Mode.INOUT, name = "account__number", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "cr_account", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> crAccount,
            @WebParam(mode = WebParam.Mode.INOUT, name = "status", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> status,
            @WebParam(mode = WebParam.Mode.INOUT, name = "f_key", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> fKey,
            @WebParam(mode = WebParam.Mode.INOUT, name = "balance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> balance,
            @WebParam(mode = WebParam.Mode.INOUT, name = "message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> message,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> response,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response_message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> responseMessage,
            /*@WebParam(mode = WebParam.Mode.INOUT, name = "agent_phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    Holder<String> agentPhoneNo,*/
            @WebParam(name = "customerType", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    String customerType,
            @WebParam(name = "description", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                    String description,
            @WebParam(mode = WebParam.Mode.INOUT, name = "startDate", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                Holder<String> startDate,
            @WebParam(mode = WebParam.Mode.INOUT, name = "endDate", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
                Holder<String> endDate,
            @WebParam(mode = WebParam.Mode.INOUT, name = "startTime", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            Holder<String> startTime,
            @WebParam(mode = WebParam.Mode.INOUT, name = "endTime", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            Holder<String> endTime,
            @WebParam(mode = WebParam.Mode.INOUT, name = "emailaddress", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            Holder<String> emailaddress
    );

    @WebMethod(operationName = "GetMemberDetails", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberDetails")
    @RequestWrapper(localName = "GetMemberDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetMemberDetails")
    @ResponseWrapper(localName = "GetMemberDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetMemberDetailsResult")
    public void getMemberDetails(
            @WebParam(mode = WebParam.Mode.INOUT, name = "phoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "GetMemberAccounts", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetMemberAccounts")
    @RequestWrapper(localName = "GetMemberAccounts", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetMemberAccounts")
    @ResponseWrapper(localName = "GetMemberAccounts_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetMemberAccountsResult")
    public void getMemberAccounts(
            @WebParam(mode = WebParam.Mode.INOUT, name = "mobilePhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> mobilePhoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "GetSavingsAccounts", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetSavingsAccounts")
    @RequestWrapper(localName = "GetSavingsAccounts", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetSavingsAccounts")
    @ResponseWrapper(localName = "GetSavingsAccounts_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetSavingsAccountsResult")
    public void getSavingsAccounts(
            @WebParam(mode = WebParam.Mode.INOUT, name = "mobilePhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> mobilePhoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "ValidateAccountDetails", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateAccountDetails")
    @RequestWrapper(localName = "ValidateAccountDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ValidateAccountDetails")
    @ResponseWrapper(localName = "ValidateAccountDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ValidateAccountDetailsResult")
    public void validateAccountDetails(
            @WebParam(mode = WebParam.Mode.INOUT, name = "accountNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage
    );

    @WebMethod(operationName = "ValidateAccountDetailsWithImageAMust", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ValidateAccountDetailsWithImageAMust")
    @RequestWrapper(localName = "ValidateAccountDetailsWithImageAMust", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ValidateAccountDetailsWithImageAMust")
    @ResponseWrapper(localName = "ValidateAccountDetailsWithImageAMust_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ValidateAccountDetailsWithImageAMustResult")
    public void validateAccountDetailsWithImageAMust(
            @WebParam(mode = WebParam.Mode.INOUT, name = "accountNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage
    );

    @WebMethod(operationName = "ProcessReversal", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:ProcessReversal")
    @RequestWrapper(localName = "ProcessReversal", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ProcessReversal")
    @ResponseWrapper(localName = "ProcessReversal_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.ProcessReversalResult")
    public void processReversal(
            @WebParam(mode = WebParam.Mode.INOUT, name = "requestID", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> requestId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage
    );

    @WebMethod(operationName = "GetAgentBalance", action = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking:GetAgentBalance")
    @RequestWrapper(localName = "GetAgentBalance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetAgentBalance")
    @ResponseWrapper(localName = "GetAgentBalance_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking", className = "schemas.dynamics.microsoft.codeunit.mobilebanking.GetAgentBalanceResult")
    public void getAgentBalance(
            @WebParam(mode = WebParam.Mode.INOUT, name = "msisdn", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> msisdn,
            @WebParam(mode = WebParam.Mode.OUT, name = "balance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.math.BigDecimal> balance,
            @WebParam(mode = WebParam.Mode.OUT, name = "response", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> response,
            @WebParam(mode = WebParam.Mode.OUT, name = "response_message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

}
