package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import javax.xml.bind.annotation.*;
import java.math.BigDecimal;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="return_value" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="actualBalance" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="availableBal" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="docNo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="responseCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sTM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="vISA" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="pOS" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="responseMessage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="numTransactions" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "returnValue",
    "actualBalance",
    "availableBal",
    "docNo",
    "responseCode",
    "description",
    "stm",
    "visa",
    "pos",
    "responseMessage",
    "numTransactions"
})
@XmlRootElement(name = "MemberMinistatement_Result")
public class MemberMinistatementResult {

    @XmlElement(name = "return_value", required = true)
    protected String returnValue;
    @XmlElement(required = true)
    protected BigDecimal actualBalance;
    @XmlElement(required = true)
    protected BigDecimal availableBal;
    @XmlElement(required = true)
    protected String docNo;
    @XmlElement(required = true)
    protected String responseCode;
    @XmlElement(required = true)
    protected String description;
    @XmlElement(name = "sTM", required = true)
    protected String stm;
    @XmlElement(name = "vISA")
    protected boolean visa;
    @XmlElement(name = "pOS")
    protected boolean pos;
    @XmlElement(required = true)
    protected String responseMessage;
    protected int numTransactions;

    /**
     * Gets the value of the returnValue property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReturnValue() {
        return returnValue;
    }

    /**
     * Sets the value of the returnValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReturnValue(String value) {
        this.returnValue = value;
    }

    /**
     * Gets the value of the actualBalance property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActualBalance() {
        return actualBalance;
    }

    /**
     * Sets the value of the actualBalance property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActualBalance(BigDecimal value) {
        this.actualBalance = value;
    }

    /**
     * Gets the value of the availableBal property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAvailableBal() {
        return availableBal;
    }

    /**
     * Sets the value of the availableBal property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAvailableBal(BigDecimal value) {
        this.availableBal = value;
    }

    /**
     * Gets the value of the docNo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocNo() {
        return docNo;
    }

    /**
     * Sets the value of the docNo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocNo(String value) {
        this.docNo = value;
    }

    /**
     * Gets the value of the responseCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResponseCode() {
        return responseCode;
    }

    /**
     * Sets the value of the responseCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResponseCode(String value) {
        this.responseCode = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the stm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSTM() {
        return stm;
    }

    /**
     * Sets the value of the stm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSTM(String value) {
        this.stm = value;
    }

    /**
     * Gets the value of the visa property.
     * 
     */
    public boolean isVISA() {
        return visa;
    }

    /**
     * Sets the value of the visa property.
     * 
     */
    public void setVISA(boolean value) {
        this.visa = value;
    }

    /**
     * Gets the value of the pos property.
     * 
     */
    public boolean isPOS() {
        return pos;
    }

    /**
     * Sets the value of the pos property.
     * 
     */
    public void setPOS(boolean value) {
        this.pos = value;
    }

    /**
     * Gets the value of the responseMessage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResponseMessage() {
        return responseMessage;
    }

    /**
     * Sets the value of the responseMessage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResponseMessage(String value) {
        this.responseMessage = value;
    }

    /**
     * Gets the value of the numTransactions property.
     * 
     */
    public int getNumTransactions() {
        return numTransactions;
    }

    /**
     * Sets the value of the numTransactions property.
     * 
     */
    public void setNumTransactions(int value) {
        this.numTransactions = value;
    }

}
