package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.Holder;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.1.8
 * 2019-05-17T09:06:29.440+03:00
 * Generated source version: 3.1.8
 *
 */
@WebService(targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", name = "AgencyBanking_Port")
@XmlSeeAlso({ObjectFactory.class})
public interface AgencyBankingPort {

    /**
     * @param passportPhoto
     * @param backID
     * @param address
     * @param occupation
     * @param returnValue
     * @param gender
     * @param signature
     * @param applicationNo
     * @param dateOfBirth
     * @param iDNo
     * @param pinNo
     * @param phoneNo
     * @param responseCode
     * @param branchCode
     * @param frontID
     * @param firstName
     * @param agencyUserAccount
     * @param surname
     * @param middleName
     * @param responseMessage
     * @param email
     * @param maritalStatus
     * @param iDIssueDate
     */
    @WebMethod(operationName = "MemberRegistration", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:MemberRegistration")
    @RequestWrapper(localName = "MemberRegistration", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.MemberRegistration")
    @ResponseWrapper(localName = "MemberRegistration_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.MemberRegistrationResult")
    public void memberRegistration(
            @WebParam(name = "payrollNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> payrollNo,
            @WebParam(name = "agentPhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> agentPhoneNo,
            @WebParam(name = "residence", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> residence,
            @WebParam(name = "registrationForm", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> registrationForm,
            @WebParam(name = "firstName", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> firstName,
            @WebParam(name = "middleName", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> middleName,
            @WebParam(name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> responseMessage,
            @WebParam(name = "branchCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> branchCode,
            @WebParam(name = "surname", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> surname,
            @WebParam(name = "iDNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> iDNo,
            @WebParam(name = "pinNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> pinNo,
            @WebParam(name = "address", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> address,
            @WebParam(name = "applicationNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> applicationNo,
            @WebParam(name = "gender", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> gender,
            @WebParam(name = "occupation", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> occupation,
            @WebParam(name = "phoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> phoneNo,
            @WebParam(name = "email", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> email,
            @WebParam(name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> responseCode,
            @WebParam(name = "passportPhoto", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> passportPhoto,
            @WebParam(name = "frontID", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> frontID,
            @WebParam(name = "backID", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> backID,
            @WebParam(name = "signature", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> signature,
            @WebParam(name = "agencyUserAccount", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<String> agencyUserAccount,
            @WebParam(name = "dateOfBirth", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<java.lang.String> dateOfBirth,
            @WebParam(name = "maritalStatus", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<java.lang.String> maritalStatus,
            @WebParam(name = "iDIssueDate", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.INOUT)
                    Holder<java.lang.String> iDIssueDate,
            @WebParam(name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", mode = WebParam.Mode.OUT)
                    Holder<Integer> returnValue);

    @WebMethod(operationName = "GetMemberImage", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberImage")
    @RequestWrapper(localName = "GetMemberImage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberImage")
    @ResponseWrapper(localName = "GetMemberImage_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberImageResult")
    public void getMemberImage(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> errorMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "GetAgentBalance", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetAgentBalance")
    @RequestWrapper(localName = "GetAgentBalance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetAgentBalance")
    @ResponseWrapper(localName = "GetAgentBalance_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetAgentBalanceResult")
    public void getAgentBalance(
            @WebParam(mode = WebParam.Mode.INOUT, name = "agentPhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> agentPhoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.math.BigDecimal> returnValue
    );

    @WebMethod(operationName = "ValidateAccountDetails", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ValidateAccountDetails")
    @RequestWrapper(localName = "ValidateAccountDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ValidateAccountDetails")
    @ResponseWrapper(localName = "ValidateAccountDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ValidateAccountDetailsResult")
    public void validateAccountDetails(
            @WebParam(mode = WebParam.Mode.INOUT, name = "accountNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> errorMessage
    );

    @WebMethod(operationName = "BlockAccountNumber", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:BlockAccountNumber")
    @RequestWrapper(localName = "BlockAccountNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.BlockAccountNumber")
    @ResponseWrapper(localName = "BlockAccountNumber_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.BlockAccountNumberResult")
    public void blockAccountNumber(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "accountNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> errorMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "Spotcash", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:Spotcash")
    @RequestWrapper(localName = "Spotcash", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.Spotcash")
    @ResponseWrapper(localName = "Spotcash_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.SpotcashResult")
    public void spotcash(
            @WebParam(mode = WebParam.Mode.INOUT, name = "request_id", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> requestId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "transaction_type", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.Integer> transactionType,
            @WebParam(mode = WebParam.Mode.INOUT, name = "amount", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> amount,
            @WebParam(mode = WebParam.Mode.INOUT, name = "trnx_charges", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> trnxCharges,
            @WebParam(mode = WebParam.Mode.INOUT, name = "account__number", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "cr_account", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> crAccount,
            @WebParam(mode = WebParam.Mode.INOUT, name = "status", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> status,
            @WebParam(mode = WebParam.Mode.INOUT, name = "f_key", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> fKey,
            @WebParam(mode = WebParam.Mode.INOUT, name = "balance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> balance,
            @WebParam(mode = WebParam.Mode.INOUT, name = "message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> message,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> response,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response_message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "agent_phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> agentPhoneNo,
            @WebParam(name = "customerType", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    java.lang.String customerType,
            @WebParam(name = "description", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    java.lang.String description
    );

    @WebMethod(operationName = "GetMemberAccounts", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberAccounts")
    @RequestWrapper(localName = "GetMemberAccounts", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberAccounts")
    @ResponseWrapper(localName = "GetMemberAccounts_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberAccountsResult")
    public void getMemberAccounts(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> errorMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "GetMemberBalance", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetMemberBalance")
    @RequestWrapper(localName = "GetMemberBalance", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberBalance")
    @ResponseWrapper(localName = "GetMemberBalance_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetMemberBalanceResult")
    @WebResult(name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
    public java.lang.String getMemberBalance(
            @WebParam(name = "accountNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    java.lang.String accountNo
    );

    @WebMethod(operationName = "GetTransactions", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetTransactions")
    @RequestWrapper(localName = "GetTransactions", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetTransactions")
    @ResponseWrapper(localName = "GetTransactions_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetTransactionsResult")
    public void getTransactions(
            @WebParam(mode = WebParam.Mode.INOUT, name = "agentPhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> agentPhoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "startDate", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> startDate,
            @WebParam(mode = WebParam.Mode.INOUT, name = "endDate", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> endDate,
            @WebParam(mode = WebParam.Mode.INOUT, name = "startTime", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> startTime,
            @WebParam(mode = WebParam.Mode.INOUT, name = "endTime", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> endTime,
            @WebParam(mode = WebParam.Mode.INOUT, name = "transactionData", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> transactionData
    );



    @WebMethod(operationName = "GetNonMemberAccounts", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetNonMemberAccounts")
    @RequestWrapper(localName = "GetNonMemberAccounts", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetNonMemberAccounts")
    @ResponseWrapper(localName = "GetNonMemberAccounts_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetNonMemberAccountsResult")
    public void getNonMemberAccounts(
            @WebParam(mode = WebParam.Mode.INOUT, name = "clientID", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> clientID,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.OUT, name = "return_value", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.Integer> returnValue
    );

    @WebMethod(operationName = "ProcessReversal", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ProcessReversal")
    @RequestWrapper(localName = "ProcessReversal", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ProcessReversal")
    @ResponseWrapper(localName = "ProcessReversal_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ProcessReversalResult")
    public void processReversal(
            @WebParam(mode = WebParam.Mode.INOUT, name = "request_id", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> requestId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "ref_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> refNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "transaction_type", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> transactionType,
            @WebParam(mode = WebParam.Mode.INOUT, name = "account__number", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> accountNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> response,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response_message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "agent_phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> agentPhoneNo
    );

    @WebMethod(operationName = "FetchMemberDetails", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchMemberDetails")
    @RequestWrapper(localName = "FetchMemberDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchMemberDetails")
    @ResponseWrapper(localName = "FetchMemberDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchMemberDetails")
    public void getMember(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "GetImage", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetImage")
    @RequestWrapper(localName = "GetImage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetImage")
    @ResponseWrapper(localName = "GetImage_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetImage")
    public void getMemberImage(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "errorMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> errorMessage
    );

    @WebMethod(operationName = "FetchEvents", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchEvents")
    @RequestWrapper(localName = "FetchEvents", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchEvents")
    @ResponseWrapper(localName = "FetchEvents_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchEvents")
    public void getEvents(
            @WebParam(mode = WebParam.Mode.INOUT, name = "devicePhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> devicePhone,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );




    @WebMethod(operationName = "validateMemberRegistered", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:validateMemberRegistered")
    @RequestWrapper(localName = "validateMemberRegistered", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.validateMemberRegistered")
    @ResponseWrapper(localName = "validateMemberRegistered_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.validateMemberRegistered")
    public void validateMemberRegistered(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
                    javax.xml.ws.Holder<java.lang.String> responseMessage
    );



    @WebMethod(operationName = "validateMember", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:validateMember")
    @RequestWrapper(localName = "validateMember", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.validateMember")
    @ResponseWrapper(localName = "validateMember_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.validateMember")
    public void validateMember(
            @WebParam(mode = WebParam.Mode.INOUT, name = "agentphoneNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> agentPhoneNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "phoneNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> phoneNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "RegisterMember", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RegisterMember")
    @RequestWrapper(localName = "RegisterMember", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RegisterMember")
    @ResponseWrapper(localName = "RegisterMember_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RegisterMember")
    public void registerMemberToEvent(
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "authenticationMode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> authenticationMode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "agentDevicePhone", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> agentDevicePhone,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "regComment", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> regComment,
            @WebParam(mode = WebParam.Mode.INOUT, name = "disbursementMethods", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> disbursementMethod,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "NonMemberDetails", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:NonMemberDetails")
    @RequestWrapper(localName = "NonMemberDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.NonMemberDetails")
    @ResponseWrapper(localName = "NonMemberDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.NonMemberDetails")
    public void registerNonMemberToEvent(
            @WebParam(mode = WebParam.Mode.INOUT, name = "name", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> name,
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "phoneNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> phoneNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "gender", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> gender,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "devicePhoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> devicePhoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );


    @WebMethod(operationName = "RedeemRewards", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RedeemRewards")
    @RequestWrapper(localName = "RedeemRewards", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RedeemRewards")
    @ResponseWrapper(localName = "RedeemRewards_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RedeemRewards")
    public void RedeemRewards(
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "RedeemItemsSelected", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:RedeemItemsSelected")
    @RequestWrapper(localName = "RedeemItemsSelected", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RedeemItemsSelected")
    @ResponseWrapper(localName = "RedeemItemsSelected_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.RedeemItemsSelected")
    public void RedeemItemsSelected(
            @WebParam(mode = WebParam.Mode.INOUT, name = "event_id", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> event_id,
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "quantity", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.Double> quantity,
            @WebParam(mode = WebParam.Mode.INOUT, name = "rewardName", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> rewardName,
            @WebParam(mode = WebParam.Mode.INOUT, name = "disbursementMethod", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> disbursementMethod,
            @WebParam(mode = WebParam.Mode.INOUT, name = "trxId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> trxId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );

    @WebMethod(operationName = "ViewDetails", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:ViewDetails")
    @RequestWrapper(localName = "ViewDetails", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ViewDetails")
    @ResponseWrapper(localName = "ViewDetails_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.ViewDetails")
    public void ViewDetails(
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "idNumber", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> idNumber,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );
    @WebMethod(operationName = "FetchMemberByMpesa", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchMemberByMpesa")
    @RequestWrapper(localName = "FetchMemberByMpesa", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchMemberByMpesa")
    @ResponseWrapper(localName = "FetchMemberByMpesa_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchMemberByMpesa")
    public void FetchMemberByMpesa(
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage,
            @WebParam(mode = WebParam.Mode.INOUT, name = "newMembersPayload", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> newMembersPayload


    );
    @WebMethod(operationName = "FetchTranscationId", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:FetchTranscationId")
    @RequestWrapper(localName = "FetchTranscationId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchTranscationId")
    @ResponseWrapper(localName = "FetchTranscationId_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.FetchTranscationId")
    public void FetchTranscationId(
            @WebParam(mode = WebParam.Mode.INOUT, name = "phoneNo", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "trxId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> trxId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "paymentReference", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> paymentReference,
            @WebParam(mode = WebParam.Mode.INOUT, name = "eventId", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> eventId,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseCode", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "responseMessage", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage

    );
    @WebMethod(operationName = "GetServiceCharge", action = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking:GetServiceCharge")
    @RequestWrapper(localName = "GetServiceCharge", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetServiceCharge")
    @ResponseWrapper(localName = "GetServiceCharge_Result", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", className = "schemas.dynamics.microsoft.codeunit.agencybanking.GetServiceCharge")
    public void getServiceCharge(
            @WebParam(mode = WebParam.Mode.INOUT, name = "agent_phone_no", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> phoneNo,
            @WebParam(mode = WebParam.Mode.INOUT, name = "amount", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> amount,
            @WebParam(mode = WebParam.Mode.INOUT, name = "transaction_Type", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> transcationType,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response_Code", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseCode,
            @WebParam(mode = WebParam.Mode.INOUT, name = "response_Message", targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
            javax.xml.ws.Holder<java.lang.String> responseMessage
    );
}
