package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import java.net.URL;

/**
 * This class was generated by Apache CXF 3.1.8
 * 2019-05-17T09:06:29.500+03:00
 * Generated source version: 3.1.8
 *
 */
@WebServiceClient(name = "MobileBanking",
        wsdlLocation = "classpath:wsdl/Mobile_banking.xml",
        targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/MobileBanking")
public class MobileBanking extends Service {

    public final static URL WSDL_LOCATION;
private static final Logger LOGGER = LoggerFactory.getLogger(MobileBanking.class);
    public final static QName SERVICE = new QName("urn:microsoft-dynamics-schemas/codeunit/MobileBanking", "MobileBanking");
    public final static QName MobileBankingPort = new QName("urn:microsoft-dynamics-schemas/codeunit/MobileBanking", "MobileBanking_Port");
    static {
        URL url = MobileBanking.class.getClassLoader().getResource("wsdl/Mobile_banking.xml");
        if (url == null) {
//            java.util.logging.LoggerFactory.getLogger(MobileBanking.class.getName())
//                    .log(java.util.logging.Level.INFO,
//                            "Can not initialize the default wsdl from {0}", "classpath:wsdl/Agency_banking.xml");
            LOGGER.error("Can not initialize the default wsdl from {0}", "classpath:wsdl/Mobile_banking.xml");
        }
        WSDL_LOCATION = url;
    }

    public MobileBanking(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public MobileBanking(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public MobileBanking() {
        super(WSDL_LOCATION, SERVICE);
    }

    public MobileBanking(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public MobileBanking(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public MobileBanking(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns MobileBankingPort
     */
    @WebEndpoint(name = "MobileBanking_Port")
    public MobileBankingPort getMobileBankingPort() {
        return super.getPort(MobileBankingPort, MobileBankingPort.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns MobileBankingPort
     */
    @WebEndpoint(name = "MobileBanking_Port")
    public MobileBankingPort getMobileBankingPort(WebServiceFeature... features) {
        return super.getPort(MobileBankingPort, MobileBankingPort.class, features);
    }
}