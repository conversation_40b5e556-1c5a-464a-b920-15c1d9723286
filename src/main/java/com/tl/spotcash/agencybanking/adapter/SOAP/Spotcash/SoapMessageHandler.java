package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.namespace.QName;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import javax.xml.ws.handler.MessageContext;
import javax.xml.ws.handler.soap.SOAPHandler;
import javax.xml.ws.handler.soap.SOAPMessageContext;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class SoapMessageHandler implements SOAPHandler<SOAPMessageContext> {

	private static final Logger LOGGER = LoggerFactory.getLogger(SoapMessageHandler.class);

	@Override
	public boolean handleMessage(SOAPMessageContext context) {
		try {
			SOAPMessage message = context.getMessage();
			message.saveChanges();
			StringWriter writer = new StringWriter();
			message.writeTo(new StringOutputStream(writer));
			LOGGER.info("SOAP message: \n" + writer.toString());
		} catch (SOAPException e) {
			LOGGER.error("Error occurred while adding credentials to SOAP header.", e);
		} catch (IOException e) {
			LOGGER.error("Error occurred while writing message to output stream.", e);
		}
		return true;
	}

	@Override
	public Set<QName> getHeaders() {
		//LOGGER.info("getHeaders has been invoked.");
		return null;
	}

	@Override
	public boolean handleFault(SOAPMessageContext context) {
		// LOGGER.info("handleFault has been invoked.");
		return true;
	}

	@Override
	public void close(MessageContext context) {
		//LOGGER.info("close has been invoked.");
	}

	//TODO: remove this class after testing is finished
	private static class StringOutputStream extends OutputStream {

		private StringWriter writer;

		public StringOutputStream(StringWriter writer) {
			this.writer = writer;
		}

		@Override
		public void write(int b) throws IOException {
			writer.write(b);
		}
	}
}
