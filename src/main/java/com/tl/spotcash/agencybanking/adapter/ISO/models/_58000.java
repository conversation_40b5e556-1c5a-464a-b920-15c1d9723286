
package com.tl.spotcash.agencybanking.adapter.ISO.models;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "currency",
    "availableBalance",
    "actualBalance",
    "creditDebit",
    "creditDebitActual"
})
public class _58000 {

    @JsonProperty("currency")
    private Currency_ currency;
    @JsonProperty("availableBalance")
    private AvailableBalance availableBalance;
    @JsonProperty("actualBalance")
    private ActualBalance actualBalance;
    @JsonProperty("creditDebit")
    private CreditDebit creditDebit;
    @JsonProperty("creditDebitActual")
    private CreditDebitActual creditDebitActual;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonProperty("currency")
    public Currency_ getCurrency() {
        return currency;
    }

    @JsonProperty("currency")
    public void setCurrency(Currency_ currency) {
        this.currency = currency;
    }

    @JsonProperty("availableBalance")
    public AvailableBalance getAvailableBalance() {
        return availableBalance;
    }

    @JsonProperty("availableBalance")
    public void setAvailableBalance(AvailableBalance availableBalance) {
        this.availableBalance = availableBalance;
    }

    @JsonProperty("actualBalance")
    public ActualBalance getActualBalance() {
        return actualBalance;
    }

    @JsonProperty("actualBalance")
    public void setActualBalance(ActualBalance actualBalance) {
        this.actualBalance = actualBalance;
    }

    @JsonProperty("creditDebit")
    public CreditDebit getCreditDebit() {
        return creditDebit;
    }

    @JsonProperty("creditDebit")
    public void setCreditDebit(CreditDebit creditDebit) {
        this.creditDebit = creditDebit;
    }

    @JsonProperty("creditDebitActual")
    public CreditDebitActual getCreditDebitActual() {
        return creditDebitActual;
    }

    @JsonProperty("creditDebitActual")
    public void setCreditDebitActual(CreditDebitActual creditDebitActual) {
        this.creditDebitActual = creditDebitActual;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
