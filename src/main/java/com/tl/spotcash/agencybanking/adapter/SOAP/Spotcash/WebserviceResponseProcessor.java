package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.*;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.AgentIdAndBalanceResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.CbsMinistatementsResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.MiniStatement;
import com.tl.spotcash.agencybanking.custommodels.cbsIntegrator.MiniStatements;
import com.tl.spotcash.agencybanking.entity.*;
import com.tl.spotcash.agencybanking.service.ReserveFundsConfiguration;
import com.tl.spotcash.agencybanking.utils.Httpfunctions;
import com.tl.spotcash.agencybanking.utils.SharedFunctions;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.*;

/**
 * <AUTHOR>
 */
public class WebserviceResponseProcessor {

	private CrudTransactionController crudTransactions;
	private static final Logger LOGGER = LoggerFactory.getLogger(WebserviceResponseProcessor.class);
	SharedFunctions sharedFunctions = new SharedFunctions();
	private Environment env;
	private ReserveFundsConfiguration reverseFundsConfiguration;

	public WebserviceResponseProcessor(CrudTransactionController crudTransactions) {
		this.crudTransactions = crudTransactions;
	}

	public WebserviceResponseProcessor(CrudTransactionController crudTransactions, Environment env, ReserveFundsConfiguration reverseFundsConfiguration) {
		this.crudTransactions = crudTransactions;
		this.env = env;
		this.reverseFundsConfiguration = reverseFundsConfiguration;
	}

	/**
	 * @param cbsrequestdata
	 * @param cbs_client_config_data
	 * @return
	 * @throws IOException
	 */
	public CorebankingResponse process_soap_requests(CbsRequestData cbsrequestdata, SpVpnclientsConfigs cbs_client_config_data) throws IOException {
		CbsResponseData cbsResponse = null;
		ObjectMapper objectMapper = new ObjectMapper();
		SOACbsAuthParams authParams = null;
		LOGGER.info("cbsrequestdata {}",new ObjectMapper().writeValueAsString(cbsrequestdata));
		BigDecimal transId = null;
		SpTransTempTable transatempObj = new SpTransTempTable();
		//Invoke corebanking webservice and fetch result , store it in cbsResponse field
		CorebankingResponse corebankingresponse = new CorebankingResponse();
		SpMsgTemplates messageTemplate = new SpMsgTemplates();
		SpClientsAppConfigurations clientsAppConfigurations = new SpClientsAppConfigurations();
		clientsAppConfigurations  = crudTransactions.fetchAppData(new BigInteger(cbsrequestdata.getClientId()));

		try {
			if (cbsrequestdata.getTranstemptableid().equals("")) {
				transId = new BigDecimal("99");
			} else {
				transId = new BigDecimal(cbsrequestdata.getTranstemptableid());
			}
			/**
			 * {"cbsUser":"user-name","cbsPassword":"password"} <br>
			 * De-serialize the json string above to POJO, the json string is stored
			 * in <br>
			 * SpVpnclientsConfigs object see cbs_client_config_data.getApiParams()
			 * below
			 */
			if (null != cbs_client_config_data.getApiParams()) {
				authParams = objectMapper.readValue(cbs_client_config_data.getApiParams(), SOACbsAuthParams.class);
			}
			else{
				LOGGER.info("CBS_CLIENT_CONFIG_DATA IS NULL");
			}

			SpotcashTransactionService service_id = SpotcashTransactionService.getvalueof(cbsrequestdata.getServiceId());

			if(cbs_client_config_data.getUseMobileBanking() == 1){
				MobileWebserviceStubsImpl stubsresults = new MobileWebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
						cbs_client_config_data.getHostUrl(), env, crudTransactions, cbs_client_config_data.getHostUrl(),
						new BigInteger(cbsrequestdata.getClientId()));
				stubsresults.authenticate_to_corebanking();

				SpStoreUsers storeUser = crudTransactions.fetchStoreUser(cbsrequestdata.getAgent_msisdn(), cbsrequestdata.getClientId());
				if(storeUser != null){
					cbsrequestdata.setMobileServiceId(cbsrequestdata.getServiceId()); //Set the mobile serviceId

					//Balance Agent ID and responseMessage
					if (SpotcashTransactionService.AGENT_BALANCE.getServiceId().equals(cbsrequestdata.getServiceId())) {
						LOGGER.info("Agent balance request initiated to Mobile Banking Webservice");

						AgentIdAndBalanceResponseMapper agentResponseMapper = stubsresults.getAgentBalance(cbsrequestdata.getAgentPhoneNumber());
						corebankingresponse.setPOSresponsecode("0000");
						corebankingresponse.setCorebankingResponseCode("00");
						if (null != agentResponseMapper && !ObjectUtils.isEmpty(agentResponseMapper.getBalance())) {
							corebankingresponse.setCorebankingResponseMessage(agentResponseMapper.getBalance());
							corebankingresponse.setSpotcash_agent_id(agentResponseMapper.getAgentId());
						}
						else {
							LOGGER.info("BALANCE IS NULL");
							corebankingresponse.setCorebankingResponseMessage("00");
							corebankingresponse.setSpotcash_agent_id("Agent Not Found.");
						}
						corebankingresponse.setSpotcash_transaction_id("AGBAL");

						return corebankingresponse;
					}

					//Call core banking endpoint and wait for results
					LOGGER.info("REQUEST DATA BEFORE CALLING MOBILE BANKING CBS<><><>");
					LOGGER.info("cbsrequestdata {}",new ObjectMapper().writeValueAsString(cbsrequestdata));

					/*int agentCommission = 0;
					try {if(!ObjectUtils.isEmpty(cbsrequestdata.getService_charge())) agentCommission = Integer.parseInt(cbsrequestdata.getService_charge());}
					catch (Exception e) {e.printStackTrace();}*/
					CbsRequestData mobileCbsRequestData =  new Gson().fromJson(new Gson().toJson(cbsrequestdata), CbsRequestData.class);
					if (SpotcashTransactionService.CLIENT_DEPOSIT.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.CLIENT_WITHDRAWAL.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.MPESA_FLOAT_PURCHASE.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.EXTERNAL_DEPOSIT.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.CLIENT_MINISTATEMENT.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.CLIENT_BALANCE.getServiceId().equals(cbsrequestdata.getServiceId())
							|| SpotcashTransactionService.NM_DEPOSIT.getServiceId().equals(cbsrequestdata.getServiceId())){
						//Phone number of the agent should always be passed
						mobileCbsRequestData.setMsisdn(cbsrequestdata.getAgentPhoneNumber());
					}
					mobileCbsRequestData.setFKey(cbsrequestdata.getTransactionId());

					LOGGER.info("REQUEST DATA BEFORE CALLING CBS<><><>");
					LOGGER.info("cbsrequestdata {}",new ObjectMapper().writeValueAsString(mobileCbsRequestData));
					cbsResponse = stubsresults.spotcash_cbs_call(mobileCbsRequestData);
					System.out.println("MOBILE BANKING cbsResponse <><><> "+new ObjectMapper().writeValueAsString(cbsResponse));
				}
				else{
					cbsResponse = new CbsResponseData();
					cbsResponse.setResponseMessage("Sorry, your agent commission account has not yet been set up. " +
							"Please contact support for assistance.");
				}
			}
			else{
				WebserviceStubsImpl stubsresults = new WebserviceStubsImpl(authParams.getCbsUser(), authParams.getCbsPassword(),
						cbs_client_config_data.getHostUrl(), env, crudTransactions, cbs_client_config_data.getHostUrl(),
						new BigInteger(cbsrequestdata.getClientId()));
				stubsresults.authenticate_to_corebanking();

				if (SpotcashTransactionService.AGENT_BALANCE.getServiceId().equals(cbsrequestdata.getServiceId())) {
					LOGGER.info("Agent balance request initiated");
					//BigDecimal balance = stubsresults.getAgentBalance(cbsrequestdata.getAgentPhoneNumber());
					AgentIdAndBalanceResponseMapper agentResponseMapper = stubsresults.getAgentIdAndBalance(cbsrequestdata.getAgentPhoneNumber());
					corebankingresponse.setPOSresponsecode("0000");
					corebankingresponse.setCorebankingResponseCode("00");
					if (null != agentResponseMapper || null != agentResponseMapper.getBalance()) {
						//corebankingresponse.setCorebankingResponseMessage(balance.toString());
						corebankingresponse.setCorebankingResponseMessage(agentResponseMapper.getBalance());
						corebankingresponse.setSpotcash_agent_id(agentResponseMapper.getAgentId());
					} else {
						LOGGER.info("BALANCE IS NULL");
						corebankingresponse.setCorebankingResponseMessage("00");
						corebankingresponse.setSpotcash_agent_id("Agent Not Found.");
					}
					corebankingresponse.setSpotcash_transaction_id("AGBAL");

					return corebankingresponse;
				}
				//Call core banking endpoint and wait for results
				LOGGER.info("REQUEST DATA BEFORE CALLING CBS<><><>");
				LOGGER.info("cbsrequestdata {}",new ObjectMapper().writeValueAsString(cbsrequestdata));
				cbsResponse = stubsresults.spotcash_cbs_call(cbsrequestdata);
				System.out.println("cbsResponse <><><> "+new ObjectMapper().writeValueAsString(cbsResponse));
			}

			if(cbsResponse.getResponse() == null || cbsResponse.getResponse().equalsIgnoreCase("")){
				LOGGER.info("No Response From CBS.");
				try{
					corebankingresponse.setCorebankingResponseMessage(cbsResponse.getResponseMessage());
				}catch (Exception xx){
					corebankingresponse.setCorebankingResponseMessage("No Response from CBS.");
				}
				corebankingresponse.setPOSresponsecode("9942");
				corebankingresponse.setCorebankingResponseCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
				try{
					corebankingresponse.setCorebankingResponseMessage(cbsResponse.getResponseMessage());
				}catch (Exception e){
					corebankingresponse.setCorebankingResponseMessage("No Response From CBS. ");
				}
				corebankingresponse.setSpotcash_transaction_id(cbsrequestdata.getTransactionId());
				try{
					corebankingresponse.setCorebankingResponseDescription(cbsResponse.getResponseMessage());
				}catch (Exception e){
					corebankingresponse.setCorebankingResponseDescription("No Response From CBS");
				}

				if (cbsrequestdata.getTranstemptableid().equals("")) {
					transatempObj.setId(new BigDecimal("99"));
				} else {
					transatempObj.setId(new BigDecimal(cbsrequestdata.getTranstemptableid()));
				}
				transatempObj.setRespCode(ResponseCodes.FAILED_REVERSIBLE_TRANSACTION.getResponsecode());
				transatempObj.setOriginalTxnId(cbsrequestdata.getTransactionId());
				try{
					transatempObj.setDescription(cbsResponse.getResponseMessage());
				}catch (Exception e){
					transatempObj.setDescription("No Response From CBS");
				}
				transatempObj.setIntermediateStatus(new BigInteger("5"));
				transatempObj.setTrxStatus(new BigInteger("2"));
				LOGGER.info("Trans Temp Tbl >>>" + transatempObj.toString());
				crudTransactions.updateTransTempTable(transatempObj);
				return corebankingresponse;
			}
			else {
				LOGGER.info("cbsResponse >>>" + cbsResponse.toString());
				transatempObj.setRespCode(cbsResponse.getResponse());
				ResponseCodes response_code = ResponseCodes.getvalueof(cbsResponse.getResponse());
				corebankingresponse.setCorebankingResponseCode(cbsResponse.getResponse());
				corebankingresponse.setCorebankingResponseMessage(cbsResponse.getResponseMessage());
				corebankingresponse.setSpotcash_transaction_id(cbsResponse.getRequestId());
				corebankingresponse.setCorebankingBalance(cbsResponse.getBalance());

				if (cbsrequestdata.getTranstemptableid().equals("") && (!cbsResponse.getRequestId().equals(""))) {
					transatempObj.setId(new BigDecimal("99"));
				} else {
					transatempObj.setId(new BigDecimal(cbsrequestdata.getTranstemptableid()));
				}
				if (!SpotcashTransactionService.CLIENT_AIRTIME.getServiceId().equals(cbsrequestdata.getServiceId())) {
					transatempObj.setMsisdn(cbsrequestdata.getMsisdn());
				}
				transatempObj.setOriginalTxnId(cbsResponse.getRequestId());

				if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
					corebankingresponse.setPOSresponsecode("0000");
				}
				else {
					corebankingresponse.setPOSresponsecode("9942");
					switch (response_code) {
						case MEMBER_FUNDS_INSUFFICIENT:
							corebankingresponse.setCorebankingResponseDescription("Member funds insufficient");
							break;
						case INACTIVE_ACCOUNT:
						case DEACTIVATED_ACCOUNT:
							corebankingresponse.setCorebankingResponseDescription("Inactive Member account");
							break;
						case SYSTEM_ERROR:
						case REQUEST_ERROR:
							corebankingresponse.setCorebankingResponseDescription("System error");
							break;
						case AMNT_LESS_THAN_MIN:
							corebankingresponse.setCorebankingResponseDescription("Amount less than minimum allowed");
							break;
						case AMNT_MORE_THAN_MAX:
							corebankingresponse.setCorebankingResponseDescription("Amount higher than maximum allowed");
							break;
						case DAILY_MAX_EXCEEDED:
							corebankingresponse.setCorebankingResponseDescription("Maximum daily amount exceeded");
							break;
						case EXPIRED_TRANSACTION:
							corebankingresponse.setCorebankingResponseDescription("Transaction expired");
							break;
						case DUPLICATE_TRANSACTION:
							corebankingresponse.setCorebankingResponseDescription("Duplicate Transaction");
							break;
						case ACCOUNT_WILL_FALL_LOWER:
						case BAL_WILL_FALL_LOWER:
							corebankingresponse.setCorebankingResponseDescription("Account will fall below minimum balance");
							break;
						case PHONE_NUMBER_NOT_LINKED:
							corebankingresponse.setCorebankingResponseDescription("You are not registered for M-Banking");
							break;
						case MEMBER_EXISTS:
							corebankingresponse.setCorebankingResponseDescription("Member Already Registered");
							break;
						case ACCOUNT_NOT_FOUND:
							corebankingresponse.setCorebankingResponseDescription("Account not found");
							break;
						case AGENT_FUNDS_INSUFFICIENT:
							corebankingresponse.setCorebankingResponseDescription("Agent Funds Insufficient");
							break;
						case TRANSACTION_NOT_POSTED:
							corebankingresponse.setCorebankingResponseDescription("Transaction Not Posted");
							break;
						case INVALID_EMAIL_ADDRESS:
							corebankingresponse.setCorebankingResponseDescription("Invalid email Address");
							break;
						case FAILED_NO_RESPONSE:
							corebankingresponse.setCorebankingResponseDescription("Invalid Transaction Input");
						case ACCOUNT_NOT_OK:
							corebankingresponse.setCorebankingResponseDescription("Account not Ok");
						default:
							corebankingresponse.setCorebankingResponseDescription("Transaction failed to complete");
							break;
					}
				}
				/**
				 * Invoke the webservice, get response code and reconstruct the api
				 * response object, secondly update sp_transtemp_table with the
				 * result, thirdly, insert an alert with the client balance to
				 * SpTransResponseTable if the transaction type is either balance or
				 * ministatement request
				 */
				SpTransResponseTable msgObject = new SpTransResponseTable();
				SpTransResponseTable msgObjectAgent1 = new SpTransResponseTable();

				SpMessages msgObjectAgent = new SpMessages();

				if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
					LOGGER.info("Persisting messages for mini statement, client balance or non member deposits");
					switch (service_id) {
						case CLIENT_MINISTATEMENT:
							LOGGER.info("Persisting messages for mini statement");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								StringBuilder sbstmt = new StringBuilder();
								StringBuilder sb = new StringBuilder();
								int count = 0;
								if (cbsrequestdata.getNewPrintDesign().equals("0")) {//Print New Design == False
									//piped response
									String[] statementArray = cbsResponse.getResponseMessage().split("\\|");
									for (String statementArray1 : statementArray) {
										count++;
										if (!"DR".equals(statementArray1.trim()) || !"CR".equals(statementArray1.trim())) {
											sb.append("|").append(statementArray1);
										}
										if (count == 3) {
											sbstmt.append(sb.append("\n").toString());
											sb = new StringBuilder();
											count = 0;
										}
									}
									sbstmt.append(sb);
									corebankingresponse.setCorebankingResponseMessage(sbstmt.toString());
								} else {
									//json response
									CbsMinistatementsResponseMapper cbsMinistatementsResponseMapper = new CbsMinistatementsResponseMapper();
									ObjectMapper mapper = new ObjectMapper();

									LOGGER.info("cbs response................json format");
									LOGGER.info(cbsResponse.toString());

									cbsMinistatementsResponseMapper.setMiniStatements(mapper.readValue(cbsResponse.getResponseMessage(), MiniStatements.class));
									List<MiniStatement> miniStatementList = cbsMinistatementsResponseMapper.getMiniStatements().getMiniStatements();
									int i = 0;
									for (MiniStatement statement : miniStatementList) {
										i++;
										count++;
										String response = statement.getDate() + " " + statement.getDescription() + " DR: " + statement.getDebit() + " CR: " + statement.getCredit();
										if (i == 0)
											sb.append("| ").append(response);
										else
											sb.append(" | ").append(response);
										if (count == 3) {
											sbstmt.append(sb.append("\n").toString());
											sb = new StringBuilder();
											count = 0;
										}
									}
									sbstmt.append(sb);
									corebankingresponse.setCorebankingResponseMessage(sbstmt.toString());
								}
								corebankingresponse.setIsPrintable(Boolean.TRUE);
								String msg;
								if (cbsrequestdata.getAccountName() != null && !cbsrequestdata.getAccountName().equals("")) {
									msg = "Dear Customer, your Ministatement of Account :" + cbsrequestdata.getAccountName() + " is " + sbstmt.toString();
								} else {
									msg = "Dear Customer, your Ministatement is " + sbstmt.toString();
								}
								msgObject.setMessage(msg);
								msgObject.setTrxId(cbsrequestdata.getTransactionId());


								//Implement message templates
								if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
									String messageType = "AB_" + service_id +"_"+ response_code;

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}

										msgResponse.setMiniStatement(sbstmt.toString());
										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObject.setMessage(msg);
										}
									}
								}
								if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
									//send agent message
									String messageType = "AGENT_TRANSACTION_MSG";

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
										SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
										if (null != spClients) {
											msgResponse.setClientName(spClients.getClientName());
										}
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}

										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObjectAgent.setMessage(msg);
											msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
											msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
											msgObjectAgent.setMessageType("TRANSACTION");
											msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
											msgObjectAgent.setStatus(BigInteger.ZERO);
											msgObjectAgent.setSmsEmail("SMS");
											msgObjectAgent.setDateTime(new Date());
											msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
											crudTransactions.insertAgentMsgs(msgObjectAgent);
										}
									}
								}
								msgObject.setTrxId(cbsrequestdata.getTransactionId());

							}
							break;
						case CLIENT_BALANCE:
							LOGGER.info("Persisting messages for client balance");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								String msg;
								if (cbsrequestdata.getAccountName() != null && !cbsrequestdata.getAccountName().equals("")) {
									msg = "Dear Customer, your account balances is " + cbsResponse.getResponseMessage() + " AccountName: " + cbsrequestdata.getAccountName();
								} else {
									msg = "Dear Customer, your account balances is " + cbsResponse.getResponseMessage();
								}
								msgObject.setMessage(msg);
								//Implement message templates
								if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
									String messageType = "AB_" + service_id +"_"+ response_code;

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}
										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObject.setMessage(msg);
										}
									}
								}
								if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
									//send agent message
									String messageType = "AGENT_TRANSACTION_MSG";

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
										SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
										if (null != spClients) {
											msgResponse.setClientName(spClients.getClientName());
										}
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}

										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObjectAgent.setMessage(msg);
											msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
											msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
											msgObjectAgent.setMessageType("TRANSACTION");
											msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
											msgObjectAgent.setStatus(BigInteger.ZERO);
											msgObjectAgent.setSmsEmail("SMS");
											msgObjectAgent.setDateTime(new Date());
											msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
											crudTransactions.insertAgentMsgs(msgObjectAgent);
										}
									}
								}

								msgObject.setTrxId(cbsrequestdata.getTransactionId());
								String accountBalance = cbsResponse.getResponseMessage();
								corebankingresponse.setCorebankingResponseMessage(accountBalance);
								corebankingresponse.setIsPrintable(Boolean.TRUE);
							}
							break;
						case EXTERNAL_DEPOSIT:
							LOGGER.info("Persisting messages for non member deposits");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse()) && !cbsrequestdata.getMsisdn().equals("*********")) {
								String msg;
								if (cbsrequestdata.getAccountName() != null && !cbsrequestdata.getAccountName().equals("")) {
									if(ObjectUtils.isEmpty(cbsrequestdata.getDepositorName())){
										msg = cbsrequestdata.getTransactionId() + "." + " Dear " + cbsrequestdata.getCustomer_name()
												+ " a deposit to account no: ********" + cbsrequestdata.getAccount_number().substring(5)
												+ " AccountName: " + cbsrequestdata.getAccountName()
												+ " for Ksh: " + cbsrequestdata.getAmount() + " is Successful."
												+ (!ObjectUtils.isEmpty(cbsrequestdata.getDescription())
												? " Narration: " + cbsrequestdata.getDescription() : "");
									}
									else {
										msg = cbsrequestdata.getTransactionId() + "." + " Dear " + cbsrequestdata.getCustomer_name()
												+ ", " + cbsrequestdata.getDepositorName() + " has deposited "
												+ "KSH "+ cbsrequestdata.getAmount() + " to your account "
												+ "********" + cbsrequestdata.getAccount_number().substring(5)
												+ " (" + cbsrequestdata.getAccountName() + ") "
												+ "with the narration: " + cbsrequestdata.getDescription();
									}
								}
								else {
									if(ObjectUtils.isEmpty(cbsrequestdata.getDepositorName())){
										msg = cbsrequestdata.getTransactionId() + "." + " Dear " + cbsrequestdata.getCustomer_name()
												+ " a deposit to account no: ********" + cbsrequestdata.getAccount_number().substring(5)
												+ " for Ksh: " + cbsrequestdata.getAmount() + " is Successful."
												+ (!ObjectUtils.isEmpty(cbsrequestdata.getDescription())
												? " Narration: " + cbsrequestdata.getDescription() : "");
									}
									else {
										msg = cbsrequestdata.getTransactionId() + "." + " Dear " + cbsrequestdata.getCustomer_name()
												+ ", " + cbsrequestdata.getDepositorName() + " has deposited "
												+ "KSH "+ cbsrequestdata.getAmount() + " to your account "
												+ "********" + cbsrequestdata.getAccount_number().substring(5)
												+ " with the narration: " + cbsrequestdata.getDescription();
									}
								}
								msgObject.setMessage(msg);
								//Implement message templates
								if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
									String messageType = "AB_" + service_id +"_"+ response_code;

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}
										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObject.setMessage(msg);
										}
									}
								}
								if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
									//send agent message
									String messageType = "AGENT_TRANSACTION_MSG";

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
										SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
										if (null != spClients) {
											msgResponse.setClientName(spClients.getClientName());
										}
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}

										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObjectAgent.setMessage(msg);
											msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
											msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
											msgObjectAgent.setMessageType("TRANSACTION");
											msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
											msgObjectAgent.setStatus(BigInteger.ZERO);
											msgObjectAgent.setSmsEmail("SMS");
											msgObjectAgent.setDateTime(new Date());
											msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
											crudTransactions.insertAgentMsgs(msgObjectAgent);
										}
									}
								}

								msgObject.setTrxId(cbsrequestdata.getTransactionId());
								corebankingresponse.setCorebankingResponseMessage(msg);
								corebankingresponse.setIsPrintable(Boolean.TRUE);
							}
							break;
						case KPLC_TOKENS:
							LOGGER.info("Processing Kplc Tokens and Sending Message to Consumer");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								ApiRespons pdsl_response = Httpfunctions.processPdslResponse(Httpfunctions.makePaymentToPdsl(env, cbsrequestdata, cbsResponse));
								LOGGER.info("********** PROCESSED PSDL RESPONSE AS ***** " + pdsl_response);
								String respMessage = pdsl_response.getMessage();
								String respCode = pdsl_response.getResponseCode();
								String token = "";
								Map<String,String> response = new HashMap<>();
								JSONObject pdslResponse = pdsl_response.getResponseBody() != null ? (JSONObject) pdsl_response.getResponseBody() : null;
								if (respCode.equalsIgnoreCase("01")) { // reverse funds
									LOGGER.info("------------- PDSL SERVICE PAYMENT FAILED --------");
									transatempObj.setRespCode("33");
								} else { // successfull call
									LOGGER.info("----------- PDSL SERVICE PAYMENT SUCCESSFULL -----------------");
									JSONObject entity = pdslResponse.getJSONObject("entity");
									JSONObject ipayMsg = entity.getJSONObject("ipayMsg");
									JSONObject elecMsg = ipayMsg.getJSONObject("elecMsg");
									JSONObject vendRes;
									if (elecMsg.has("vendRevRes")) {
										JSONObject vendRevRes = elecMsg.getJSONObject("vendRevRes");
										vendRes = vendRevRes.getJSONObject("vendRes");
									} else {
										vendRes = elecMsg.getJSONObject("vendRes");
									}
									JSONObject res = vendRes.getJSONObject("res");
									JSONObject stdToken = vendRes.getJSONObject("stdToken");
									token = stdToken.getString("content");
									String msg = "Meter Number :" + cbsrequestdata.getDescription() + "\nToken :" + sharedFunctions.formatToken(stdToken.getString("content")) + "\nDate :" + new Date() + "\nReceipt :" + stdToken.get("rctNum")
											+ "\nAmount: " + cbsrequestdata.getAmount() + "\nUnits :" + stdToken.get("units");
									msgObject.setMessage(msg);
									//Implement message templates
									if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
										String messageType = "AB_" + service_id +"_"+ response_code;

										messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
										if(null != messageTemplate){
											//Additional Data that may be required for the message and is not part of the cbs request and response data
											AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();

											String generatedToken = sharedFunctions.formatToken(stdToken.getString("content"));
											String generatedTokenReceipt = stdToken.getString("rctNum");
											String tokenUnits = stdToken.getString("units");

											msgResponse.setGeneratedTokenReceipt(generatedTokenReceipt);
											msgResponse.setTokenUnits(tokenUnits);
											msgResponse.setGeneratedToken(generatedToken);

											msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
											if (null != msg) {
												msgObject.setMessage(msg);
											}
										}
									}

									LOGGER.info("Msg: " + msg);

									msgObject.setTrxId(cbsrequestdata.getTransactionId());
									corebankingresponse.setIsPrintable(Boolean.TRUE);
									response.put("token",sharedFunctions.formatToken(stdToken.getString("content")));
									response.put("units", String.valueOf(stdToken.get("units")));
									response.put("receipt", String.valueOf(stdToken.get("rctNum")));
								}
								corebankingresponse.setCorebankingResponseCode(respCode);
								corebankingresponse.setCorebankingResponseDescription("Token : " + token);
								corebankingresponse.setCorebankingResponseMessage(new ObjectMapper().writeValueAsString(response));
								transatempObj.setIntermediateStatus(new BigInteger("5"));
							}
							break;
						case KPLC_POSPAID:
							LOGGER.info("Processing Kplc postpaid and Sending Message to Consumer");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								ApiRespons post_paid_response = Httpfunctions.processPdslResponse(Httpfunctions.makePaymentToPdsl(env, cbsrequestdata, cbsResponse));
								LOGGER.info("********** PROCESSED RESPONSE AS ***** " + post_paid_response);
								String resMessage = post_paid_response.getMessage();
								String resCode = post_paid_response.getResponseCode();
								String customerMsg = null;
								JSONObject pdsl_post_paid_response = post_paid_response.getResponseBody() != null ? (JSONObject) post_paid_response.getResponseBody() : null;
								if (resCode.equalsIgnoreCase("01")) { // reverse funds
									LOGGER.info("------------- PDSLPOST PAID PAYMENT FAILED --------");
									transatempObj.setRespCode("33");
								} else { // successfull call
									LOGGER.info("----------- PDSL POST PAID PAYMENT SUCCESSFULL -----------------");
									JSONObject entity = pdsl_post_paid_response.getJSONObject("entity");
									JSONObject ipayMsg = entity.getJSONObject("ipayMsg");
									JSONObject billPayMsg = ipayMsg.getJSONObject("billPayMsg");
									JSONObject payRes = billPayMsg.getJSONObject("payRes");
									customerMsg = payRes.has("customerMsg") ? "\nMessage :" + payRes.getString("customerMsg") : "";
									String msg = "Meter Number :" + cbsrequestdata.getDescription() + "\nDate :" + new Date() + "\nTransaction Ref :" + payRes.get("rctNum")
											+ "\nAmount: " + cbsrequestdata.getAmount() + customerMsg;
									//persist message
									msgObject.setMessage(msg);
									//Implement message templates
									if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
										String messageType = "AB_" + service_id +"_"+ response_code;

										messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
										if(null != messageTemplate){
											//Additional Data that may be required for the message and is not part of the cbs request and response data
											AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();

											String customerMessage = customerMsg;
											String Receipt = payRes.getString("rctNum");

											msgResponse.setKplcPostPayCustomerMessage(customerMessage);
											msgResponse.setKplcPostPayReceipt(Receipt);

											msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
											if (null != msg) {
												msgObject.setMessage(msg);
											}
										}
									}

									LOGGER.info("Msg \n\n " + msg);
									customerMsg = msg;
									msgObject.setTrxId(cbsrequestdata.getTransactionId());
									corebankingresponse.setIsPrintable(Boolean.TRUE);
								}
								corebankingresponse.setCorebankingResponseCode(resCode);
								corebankingresponse.setCorebankingResponseDescription(resMessage);
								corebankingresponse.setCorebankingResponseMessage(customerMsg);
								transatempObj.setIntermediateStatus(new BigInteger("5"));
							}
							break;
						case MPESA_FLOAT_PURCHASE:
							LOGGER.info("Persisting mpesa float purchase messages for client withdrawals");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								String sms_enabled = "1";
								LOGGER.info("Withdrawal enabled: "+sms_enabled);
                                String msg = "Dear Customer, you have withdrawn Ksh " + cbsrequestdata.getAmount() + " from account " + cbsrequestdata.getAccountName();
                                msgObject.setMessage(msg);
                                //Implement message templates
                                if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
                                    String messageType = "AB_" + service_id +"_"+ response_code;

                                    messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
                                    if(null != messageTemplate){
                                        //Additional Data that may be required for the message and is not part of the cbs request and response data
                                        AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}
										SpTransTempTable spTransTempTable = crudTransactions.getTransaction(cbsrequestdata.getTransactionId());
										cbsrequestdata.setCustomer_name(spTransTempTable.getCustomerName());
										cbsrequestdata.setAgentNo(spTransTempTable.getRefId());

                                        msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
                                        if (null != msg) {
                                            msgObject.setMessage(msg);
                                        }
                                    }

                                }
								if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
									//send agent message
									String messageType = "AGENT_TRANSACTION_MSG";

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
										SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
										if (null != spClients) {
											msgResponse.setClientName(spClients.getClientName());
										}
										SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
										if (null != spStoreUsers) {
											msgResponse.setAgentName(spStoreUsers.getContactName());
										}


										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObjectAgent1.setMessage(msg);
											msgObjectAgent1.setTrxId(cbsrequestdata.getTransactionId()+"_AGENT");
											crudTransactions.insertAgencyMsgs(msgObjectAgent1);

										}
									}
								}


                                msgObject.setTrxId(cbsrequestdata.getTransactionId());
                                corebankingresponse.setCorebankingResponseMessage(msg);
                                corebankingresponse.setIsPrintable(Boolean.TRUE);
                                transatempObj.setIntermediateStatus(BigInteger.ONE);
								transatempObj.setToAccount(cbsrequestdata.getAgentNo());
								transatempObj.setPaymentReference(cbsrequestdata.getStoreNo());

							}

							break;
						case CLIENT_WITHDRAWAL:
							LOGGER.info("Persisting messages for mpesa float purchase");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								String sms_enabled = cbsrequestdata.getWithdrawal_sms();
								LOGGER.info("Mpesa Float SMS enabled: "+sms_enabled);
								if(sms_enabled.equals("1")) {
									String msg ="Dear Customer, you have deposited  Ksh " + cbsrequestdata.getAmount() +" to your mpesa float " + " from account " +cbsrequestdata.getAccountName();
									msgObject.setMessage(msg);
									//Implement message templates
									if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
										String messageType = "AB_" + service_id +"_"+ response_code;

										messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
										if(null != messageTemplate){
											//Additional Data that may be required for the message and is not part of the cbs request and response data
											AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
											SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
											if (null != spStoreUsers) {
												msgResponse.setAgentName(spStoreUsers.getContactName());
											}

											msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
											if (null != msg) {
												msgObject.setMessage(msg);
											}
										}
									}
									if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
										//send agent message
										String messageType = "AGENT_TRANSACTION_MSG";

										messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
										if(null != messageTemplate){
											//Additional Data that may be required for the message and is not part of the cbs request and response data
											AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
											cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
											SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
											if (null != spClients) {
												msgResponse.setClientName(spClients.getClientName());
											}
											SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
											if (null != spStoreUsers) {
												msgResponse.setAgentName(spStoreUsers.getContactName());
											}

											msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
											if (null != msg) {
												msgObjectAgent.setMessage(msg);
												msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
												msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
												msgObjectAgent.setMessageType("TRANSACTION");
												msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
												msgObjectAgent.setStatus(BigInteger.ZERO);
												msgObjectAgent.setSmsEmail("SMS");
												msgObjectAgent.setDateTime(new Date());
												msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
												crudTransactions.insertAgentMsgs(msgObjectAgent);
											}
										}
									}


									msgObject.setTrxId(cbsrequestdata.getTransactionId());
									corebankingresponse.setCorebankingResponseMessage(msg);
									corebankingresponse.setIsPrintable(Boolean.TRUE);
								}
							}
							break;
						case INTER_ACCOUNT_TRANSFER:
							LOGGER.info("Persisting inter account transfer  messages for client withdrawals");
							if (ResponseCodes.SUCCESSFULL_TRANSACTION.getResponsecode().equals(cbsResponse.getResponse())) {
								String sms_enabled = "1";
								LOGGER.info("Inter Account Transfer enabled: "+sms_enabled);
								String msg = "Dear Customer, you have transfered Ksh " + cbsrequestdata.getAmount() + " from account " + cbsrequestdata.getAccountName();
								msgObject.setMessage(msg);
								//Implement message templates
								if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
									String messageType = "AB_" + service_id +"_"+ response_code;

									messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
									if(null != messageTemplate){
										//Additional Data that may be required for the message and is not part of the cbs request and response data
										AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
										cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());

										msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
										if (null != msg) {
											msgObject.setMessage(msg);
										}
									}
									if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
										//send agent message
										 messageType = "AGENT_TRANSACTION_MSG";

										messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
										if(null != messageTemplate){
											//Additional Data that may be required for the message and is not part of the cbs request and response data
											AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
											cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());
											SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
											if (null != spClients) {
												msgResponse.setClientName(spClients.getClientName());
											}
											SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getMsisdn(), cbsrequestdata.getClientId());
											if (null != spStoreUsers) {
												msgResponse.setAgentName(spStoreUsers.getContactName());
											}

											msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
											if (null != msg) {
												msgObjectAgent.setMessage(msg);
												msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
												msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
												msgObjectAgent.setMessageType("TRANSACTION");
												msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
												msgObjectAgent.setStatus(BigInteger.ZERO);
												msgObjectAgent.setSmsEmail("SMS");
												msgObjectAgent.setDateTime(new Date());
												msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
												crudTransactions.insertAgentMsgs(msgObjectAgent);

											}
										}
									}
								}


								msgObject.setTrxId(cbsrequestdata.getTransactionId());
								corebankingresponse.setCorebankingResponseMessage(msg);
								corebankingresponse.setIsPrintable(Boolean.TRUE);
								transatempObj.setIntermediateStatus(BigInteger.valueOf(5));
//
							}

							break;
						case CLIENT_DEPOSIT:
							if (cbsrequestdata.getAgent_transaction_sms().equalsIgnoreCase("1")){
								//send agent message
								String messageType = "AGENT_TRANSACTION_MSG";

								messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
								if(null != messageTemplate){
									//Additional Data that may be required for the message and is not part of the cbs request and response data
									AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
									SpClients spClients = crudTransactions.fetchclient_type(new BigDecimal(cbsrequestdata.getClientId()));
									if (null != spClients) {
										msgResponse.setClientName(spClients.getClientName());
									}
									SpStoreUsers spStoreUsers = crudTransactions.fetchStoreUser(cbsrequestdata.getAgent_msisdn(), cbsrequestdata.getClientId());
									if (null != spStoreUsers) {
										msgResponse.setAgentName(spStoreUsers.getContactName());
									}
									cbsrequestdata.setCustomer_name(transatempObj.getCustomerName());

									String msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
									if (null != msg) {
										msgObjectAgent.setMessage(msg);
										msgObjectAgent.setClientId(new BigInteger(cbsrequestdata.getClientId()));
										msgObjectAgent.setServiceId(new BigInteger(cbsrequestdata.getServiceId()));
										msgObjectAgent.setMessageType("TRANSACTION");
										msgObjectAgent.setMsisdn(cbsrequestdata.getMsisdn());
										msgObjectAgent.setStatus(BigInteger.ZERO);
										msgObjectAgent.setSmsEmail("SMS");
										msgObjectAgent.setDateTime(new Date());
										msgObjectAgent.setTrxId(cbsrequestdata.getTransactionId());
										crudTransactions.insertAgentMsgs(msgObjectAgent);

									}
								}
							}
							break;
					}
					crudTransactions.insertAgencyMsgs(msgObject);
				}
				else {
					LOGGER.info("Persisting messages for mst or client balance | 3");
					LOGGER.info("CBS Response: " + cbsResponse.getResponse());
					if (ResponseCodes.AGENT_FUNDS_INSUFFICIENT.getResponsecode().equals(cbsResponse.getResponse())) {
						transatempObj.setMsisdn(cbsrequestdata.getAgent_msisdn());
					}
					String msg = corebankingresponse.getCorebankingResponseDescription();

					msgObject.setMessage(msg);
					//Implement message templates
					if (clientsAppConfigurations.getUseMsgTemplate().equalsIgnoreCase("1")){
						String messageType = "AB_" + service_id +"_"+ response_code;

						messageTemplate = crudTransactions.fetchMessageTemplate(clientsAppConfigurations.getClientId(),messageType);
						if(null != messageTemplate){
							//Additional Data that may be required for the message and is not part of the cbs request and response data
							AdditionalMsgResponse msgResponse = new AdditionalMsgResponse();
							msg = sharedFunctions.messageTemplateFormatter(messageTemplate,cbsrequestdata, cbsResponse,msgResponse);
							if (null != msg) {
								msgObject.setMessage(msg);
							}
						}
					}


					msgObject.setTrxId(cbsrequestdata.getTransactionId());
					crudTransactions.insertAgencyMsgs(msgObject);
				}

				transatempObj.setTrxStatus(BigInteger.valueOf(2));
				if(crudTransactions.updateTransTempTable(transatempObj) != null){
					//Record Agency Banking Transaction
					crudTransactions.createAgencyTransaction(transatempObj);
				}
				else{
					corebankingresponse.setPOSresponsecode("9942");
					corebankingresponse.setCorebankingResponseDescription("Failed to process request. Please try again!");
					corebankingresponse.setCorebankingResponseCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
					corebankingresponse.setCorebankingResponseMessage("Failed to process request. Please try again!");
				}

				return corebankingresponse;
			}
		}
		catch (Exception e) {
			e.printStackTrace();
			LOGGER.error("Error processing SOA request " + e.toString());
			corebankingresponse.setPOSresponsecode("9942");
			try{
				corebankingresponse.setCorebankingResponseDescription(cbsResponse.getResponseMessage());
			}catch (Exception exx){
				corebankingresponse.setCorebankingResponseDescription("No Response From CBS.");
			}
			corebankingresponse.setCorebankingResponseCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
			try{
				corebankingresponse.setCorebankingResponseMessage(cbsResponse.getResponseMessage());
			}catch (Exception ej){
				corebankingresponse.setCorebankingResponseMessage("No Response From CBS.");
			}
			corebankingresponse.setSpotcash_transaction_id(cbsrequestdata.getTransactionId());

			transatempObj.setId(transId);
			transatempObj.setRespCode(ResponseCodes.EXPIRED_TRANSACTION.getResponsecode());
			transatempObj.setOriginalTxnId(cbsrequestdata.getTransactionId());
			try{
				transatempObj.setDescription(cbsResponse.getResponseMessage());
			}catch (Exception exc){
				transatempObj.setDescription("No Response From CBS");
			}
			transatempObj.setTrxStatus(BigInteger.valueOf(4));
			crudTransactions.updateTransTempTable(transatempObj);
			return corebankingresponse;
		}
	}

	private String generateRandomString() {
		String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		int LENGTH = 20;
		SecureRandom random = new SecureRandom();
		StringBuilder sb = new StringBuilder(LENGTH);
		for (int i = 0; i < LENGTH; i++) sb.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
		return sb.toString();
	}

}
