package com.tl.spotcash.agencybanking.adapter.ISO.models;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "ISOSTRING",
    "37000",
    "58000"
})
public class ParentServiceMapper {

    @JsonProperty("ISOSTRING")
    private ISOSTRING iSOSTRING;
    @JsonProperty("37000")
    private com.tl.spotcash.agencybanking.adapter.ISO.models._37000 _37000;
    @JsonProperty("58000")
    private com.tl.spotcash.agencybanking.adapter.ISO.models._58000 _58000;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonProperty("ISOSTRING")
    public ISOSTRING getISOSTRING() {
        return iSOSTRING;
    }

    @JsonProperty("ISOSTRING")
    public void setISOSTRING(ISOSTRING iSOSTRING) {
        this.iSOSTRING = iSOSTRING;
    }

    @JsonProperty("37000")
    public com.tl.spotcash.agencybanking.adapter.ISO.models._37000 get37000() {
        return _37000;
    }

    @JsonProperty("37000")
    public void set37000(com.tl.spotcash.agencybanking.adapter.ISO.models._37000 _37000) {
        this._37000 = _37000;
    }

    @JsonProperty("58000")
    public com.tl.spotcash.agencybanking.adapter.ISO.models._58000 get58000() {
        return _58000;
    }

    @JsonProperty("58000")
    public void set58000(com.tl.spotcash.agencybanking.adapter.ISO.models._58000 _58000) {
        this._58000 = _58000;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
