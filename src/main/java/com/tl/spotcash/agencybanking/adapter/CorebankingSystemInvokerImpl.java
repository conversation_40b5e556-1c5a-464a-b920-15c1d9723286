package com.tl.spotcash.agencybanking.adapter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solab.iso8583.IsoMessage;
import com.tl.spotcash.agencybanking.adapter.ISO.Iso8583serverinvoker;
import com.tl.spotcash.agencybanking.adapter.ISO.IsoResponseProcessor;
import com.tl.spotcash.agencybanking.adapter.ISO.models.ParentServiceMapper;
import com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.WebserviceResponseProcessor;
import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.IsoResponseMapper;
import com.tl.spotcash.agencybanking.custommodels.ResponseCodes;
import com.tl.spotcash.agencybanking.entity.SpTransTempTable;
import com.tl.spotcash.agencybanking.entity.SpVpnclientsConfigs;
import com.tl.spotcash.agencybanking.service.ReserveFundsConfiguration;
import com.tl.spotcash.agencybanking.xiputils.CorebankingResponse;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 */
public class CorebankingSystemInvokerImpl {

    private static final Logger LOGGER = LoggerFactory.getLogger(CorebankingSystemInvokerImpl.class);
    private CrudTransactionController crudTransactions;
    private ReserveFundsConfiguration reserveFundsConfiguration;

    public CorebankingSystemInvokerImpl(CrudTransactionController crudTransactions,ReserveFundsConfiguration reserveFundsConfiguration) {
        this.crudTransactions = crudTransactions;
        this.reserveFundsConfiguration = reserveFundsConfiguration;
    }

    /**
     *
     * @param requestData
     * @return
     * @throws IOException
     * @throws Exception
     */
    public CorebankingResponse callCBS(CbsRequestData requestData, Environment environment) throws IOException, Exception {
        CorebankingResponse corebankingresponse = new CorebankingResponse();
        LOGGER.info("CLIENT ID TO FETCH FROM CBS "+requestData.getClientId());

        SpVpnclientsConfigs cbs_client_config_data = crudTransactions.fetch_vpn_configs(new BigInteger(requestData.getClientId()));
        LOGGER.info("cbs endpoint type : "+cbs_client_config_data.getCbsEndpointType());
        switch (cbs_client_config_data.getCbsEndpointType().toUpperCase()) {
            case "ISO":
                LOGGER.info("ENDPOINT_IS_ISO");
                ParentServiceMapper respoMapper;
                ObjectMapper mapper = new ObjectMapper();
                respoMapper = mapper.readValue(cbs_client_config_data.getIsoParseParams(), ParentServiceMapper.class);
                JSONObject resobj = new JSONObject(cbs_client_config_data.getApiParams());
                LOGGER.info("SERVICE ID >>> " + requestData.getServiceId());
                LOGGER.info("CbsRequestData >>> " + requestData.toString());
                String isoObject = resobj.getJSONObject("service_id").getJSONObject(requestData.getServiceId()).optString("FIELDS");
                JSONObject j8583innerjsonObj = new JSONObject(isoObject);

                Iso8583serverinvoker isoresponseString = new Iso8583serverinvoker(j8583innerjsonObj, requestData, cbs_client_config_data, respoMapper);
                IsoMessage isoMessage = isoresponseString.createISO();
                IsoResponseMapper theresponse = isoresponseString.sendRequest(isoMessage);

                if (theresponse != null) {
                    LOGGER.info("RECEIVED RESPONSE MAP OBJECT:>>> " + theresponse);
                    corebankingresponse = new IsoResponseProcessor(crudTransactions, requestData).processISOmapData(respoMapper, theresponse);
                } else {
                    corebankingresponse.setCorebankingResponseCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());   // Xip transaction response code for connection time out
                    corebankingresponse.setPOSresponsecode("3344"); // Xip response code for connection time out
                    corebankingresponse.setCorebankingResponseDescription("Connection timed out");
                    SpTransTempTable transatempObj = new SpTransTempTable();

                    transatempObj.setId(new BigDecimal(requestData.getTranstemptableid()));
                    transatempObj.setRespCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
                    transatempObj.setOriginalTxnId(requestData.getTransactionId());
                    transatempObj.setDescription("Connection to CBS failed");
                    transatempObj.setTrxStatus(BigInteger.valueOf(2));
                    crudTransactions.updateTransTempTable(transatempObj);
                    LOGGER.error("ERROR COMMUNICATING WITH CBS # CONNECTION TIME OUT POSSIBLY");
                }
                break;
            case "T24":
                break;
            case "SOAP":
                LOGGER.info("ENDPOINT_IS_SOAP");
                corebankingresponse = new WebserviceResponseProcessor(crudTransactions, environment,reserveFundsConfiguration).process_soap_requests(requestData, cbs_client_config_data);
                break;
            default:
                break;
        }
        return corebankingresponse;
    }

    public CorebankingResponse callCbs(CbsRequestData requestData, Environment environment) throws Exception {
        CorebankingResponse corebankingresponse = new CorebankingResponse();
        LOGGER.info("CLIENT ID TO FETCH FROM CBS " + requestData.getClientId());

        SpVpnclientsConfigs cbs_client_config_data = crudTransactions.fetch_vpn_configs(new BigInteger(requestData.getClientId()));

        switch (cbs_client_config_data.getCbsEndpointType().toUpperCase()) {
            case "ISO":
                LOGGER.info("ENDPOINT_IS_ISO");
                ParentServiceMapper respoMapper;
                ObjectMapper mapper = new ObjectMapper();
                respoMapper = mapper.readValue(cbs_client_config_data.getIsoParseParams(), ParentServiceMapper.class);
                JSONObject resobj = new JSONObject(cbs_client_config_data.getApiParams());
                LOGGER.info("SERVICE ID >>> " + requestData.getServiceId());
                LOGGER.info("CbsRequestData >>> " + requestData.toString());
                String isoObject = resobj.getJSONObject("service_id").getJSONObject(requestData.getServiceId()).optString("FIELDS");
                JSONObject j8583innerjsonObj = new JSONObject(isoObject);

                Iso8583serverinvoker isoresponseString = new Iso8583serverinvoker(j8583innerjsonObj, requestData, cbs_client_config_data, respoMapper);
                IsoMessage isoMessage = isoresponseString.createISO();
                IsoResponseMapper theresponse = isoresponseString.sendRequest(isoMessage);

                if (theresponse != null) {
                    LOGGER.info("RECEIVED RESPONSE MAP OBJECT:>>> " + theresponse);
                    corebankingresponse = new IsoResponseProcessor(crudTransactions, requestData).processISOmapData(respoMapper, theresponse);
                } else {
                    corebankingresponse.setCorebankingResponseCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
                    corebankingresponse.setPOSresponsecode("3344"); // Connection time out Response Code
                    corebankingresponse.setCorebankingResponseDescription("Connection timed out");
                    SpTransTempTable transatempObj = new SpTransTempTable();

                    transatempObj.setId(new BigDecimal(requestData.getTranstemptableid()));
                    transatempObj.setRespCode(ResponseCodes.SYSTEM_ERROR.getResponsecode());
                    transatempObj.setOriginalTxnId(requestData.getTransactionId());
                    transatempObj.setDescription("Connection to CBS failed");
                    transatempObj.setTrxStatus(BigInteger.valueOf(2));
                    crudTransactions.updateTransTempTable(transatempObj);
                    LOGGER.error("ERROR COMMUNICATING WITH CBS # CONNECTION TIME OUT POSSIBLY");
                }
                break;
            case "T24":
                break;
            case "SOAP":
                LOGGER.info("ENDPOINT_IS_SOAP");
                corebankingresponse = new WebserviceResponseProcessor(crudTransactions, environment,reserveFundsConfiguration).process_soap_requests(requestData, cbs_client_config_data);
                break;
            default:
                break;
        }
        return corebankingresponse;
    }
}
