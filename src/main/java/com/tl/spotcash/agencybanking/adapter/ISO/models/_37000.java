
package com.tl.spotcash.agencybanking.adapter.ISO.models;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "currency",
    "firststm",
    "secondstm",
    "thirdstm"
})
public class _37000 {

    @JsonProperty("currency")
    private Currency currency;
    @JsonProperty("firststm")
    private Firststm firststm;
    @JsonProperty("secondstm")
    private Secondstm secondstm;
    @JsonProperty("thirdstm")
    private Thirdstm thirdstm;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonProperty("currency")
    public Currency getCurrency() {
        return currency;
    }

    @JsonProperty("currency")
    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    @JsonProperty("firststm")
    public Firststm getFirststm() {
        return firststm;
    }

    @JsonProperty("firststm")
    public void setFirststm(Firststm firststm) {
        this.firststm = firststm;
    }

    @JsonProperty("secondstm")
    public Secondstm getSecondstm() {
        return secondstm;
    }

    @JsonProperty("secondstm")
    public void setSecondstm(Secondstm secondstm) {
        this.secondstm = secondstm;
    }

    @JsonProperty("thirdstm")
    public Thirdstm getThirdstm() {
        return thirdstm;
    }

    @JsonProperty("thirdstm")
    public void setThirdstm(Thirdstm thirdstm) {
        this.thirdstm = thirdstm;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
