package com.tl.spotcash.agencybanking.adapter.SOAP.Spotcash.stubs;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import java.net.URL;

/**
 * This class was generated by Apache CXF 3.1.8
 * 2019-05-17T09:06:29.500+03:00
 * Generated source version: 3.1.8
 *
 */
@WebServiceClient(name = "AgencyBanking",
        wsdlLocation = "classpath:wsdl/Agency_banking.xml",
        targetNamespace = "urn:microsoft-dynamics-schemas/codeunit/AgencyBanking")
public class AgencyBanking extends Service {

    public final static URL WSDL_LOCATION;
private static final Logger LOGGER = LoggerFactory.getLogger(AgencyBanking.class);
    public final static QName SERVICE = new QName("urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", "AgencyBanking");
    public final static QName AgencyBankingPort = new QName("urn:microsoft-dynamics-schemas/codeunit/AgencyBanking", "AgencyBanking_Port");
    static {
        URL url = AgencyBanking.class.getClassLoader().getResource("wsdl/Agency_banking.xml");
        if (url == null) {
//            java.util.logging.LoggerFactory.getLogger(AgencyBanking.class.getName())
//                    .log(java.util.logging.Level.INFO,
//                            "Can not initialize the default wsdl from {0}", "classpath:wsdl/Agency_banking.xml");
            LOGGER.error("Can not initialize the default wsdl from {0}", "classpath:wsdl/Agency_banking.xml");
        }
        WSDL_LOCATION = url;
    }

    public AgencyBanking(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public AgencyBanking(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public AgencyBanking() {
        super(WSDL_LOCATION, SERVICE);
    }

    public AgencyBanking(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public AgencyBanking(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public AgencyBanking(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns AgencyBankingPort
     */
    @WebEndpoint(name = "AgencyBanking_Port")
    public AgencyBankingPort getAgencyBankingPort() {
        return super.getPort(AgencyBankingPort, AgencyBankingPort.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns AgencyBankingPort
     */
    @WebEndpoint(name = "AgencyBanking_Port")
    public AgencyBankingPort getAgencyBankingPort(WebServiceFeature... features) {
        return super.getPort(AgencyBankingPort, AgencyBankingPort.class, features);
    }
}