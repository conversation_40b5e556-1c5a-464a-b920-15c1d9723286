
package com.tl.spotcash.agencybanking.adapter.ISO.models;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "lowerlimit",
    "upperlimit"
})
public class ActualBalance {

    @JsonProperty("lowerlimit")
    private String lowerlimit;
    @JsonProperty("upperlimit")
    private String upperlimit;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonProperty("lowerlimit")
    public String getLowerlimit() {
        return lowerlimit;
    }

    @JsonProperty("lowerlimit")
    public void setLowerlimit(String lowerlimit) {
        this.lowerlimit = lowerlimit;
    }

    @JsonProperty("upperlimit")
    public String getUpperlimit() {
        return upperlimit;
    }

    @JsonProperty("upperlimit")
    public void setUpperlimit(String upperlimit) {
        this.upperlimit = upperlimit;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

}
