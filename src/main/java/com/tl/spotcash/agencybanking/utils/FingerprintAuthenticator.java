package com.tl.spotcash.agencybanking.utils;

import com.machinezoo.sourceafis.FingerprintMatcher;
import com.machinezoo.sourceafis.FingerprintTemplate;

public class FingerprintAuthenticator {

    public static boolean authenticate(byte[] received, byte[] existing)
    {
        FingerprintTemplate probe = new FingerprintTemplate()
                .dpi(500)
                .create(received);

        FingerprintTemplate candidate = new FingerprintTemplate()
                .dpi(500)
                .create(existing);
        double score = new FingerprintMatcher()
                .index(probe)
                .match(candidate);
        double threshold = 40;

        boolean matches = score >= threshold;
        return matches;
    }
}
