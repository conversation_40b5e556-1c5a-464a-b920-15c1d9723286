
package com.tl.spotcash.agencybanking.utils;

import com.tl.spotcash.agencybanking.custommodels.ApiRespons;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.CbsResponseData;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 *
 * <AUTHOR>
 */
public class Httpfunctions {
    private static final Logger LOGGER = LoggerFactory.getLogger(Httpfunctions.class);

    public static JSONObject makePaymentToPdsl(Environment env, CbsRequestData cbsrequestdata, CbsResponseData cbsResponse) {
        LOGGER.info("REQUEST TO MAKE PAYMENTS TO PDSL FOR ID " + cbsrequestdata.getTransactionId() + " SERVICE ID AS " + cbsrequestdata.getServiceId());
        Map<String, String> payload = new HashMap<>();
        String pdsl_uri = null;
        try {
            if (cbsrequestdata.getServiceId().equalsIgnoreCase("71")) {
                pdsl_uri = env.getProperty("datasource.spotcash.kplcPostpaidUrl");
            } else {
                pdsl_uri = env.getRequiredProperty("datasource.spotcash.kplcTokensUrl");
            }
            URI uri = new URI(pdsl_uri);
            // Model request to send to pdsl
            payload.put("amount", cbsrequestdata.getAmount());
            payload.put("meterNumber", cbsrequestdata.getDescription());
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.set("WatchDog", env.getRequiredProperty("datasource.spotcash.pdslAuthToken"));
            LOGGER.info("PDSL URL >><< " + uri);

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(payload, headers);

            ResponseEntity<String> result = restTemplate.postForEntity(uri, entity, String.class);

            LOGGER.info("Response status pdsl {} " + result.getStatusCode());
            LOGGER.info("Response body pdsl {} " + result.getBody());
            JSONObject pdslj1 = new JSONObject(result.getBody());
            LOGGER.info("pdslj1 "+pdslj1);

            return pdslj1;
        }  catch(Exception e){
            e.printStackTrace();
            LOGGER.info("PDSL PAYMENT ERROR OCCURRED " + e.getLocalizedMessage());
            return null;
        }
    }

    public static ApiRespons processPdslResponse(JSONObject pdslResponse){
        ApiRespons apiResponse = new ApiRespons();
        apiResponse.setResponseCode("01");
        // check for nullity of response
        if(pdslResponse == null){
            apiResponse.setMessage("Your request could not be completed");
            apiResponse.setResponseBody(null);
        } else{
            try {
                if(pdslResponse.getString("responseCode").equalsIgnoreCase("01")){ // unsuccessfull call
                    apiResponse.setMessage(pdslResponse.getString("responseDescription"));
                    apiResponse.setResponseBody(null);
                }else{ //successfull call
                    apiResponse.setResponseCode("00");
                    apiResponse.setMessage(pdslResponse.getString("responseDescription"));
                    apiResponse.setResponseBody(pdslResponse);
                }
            } catch (JSONException ex) {
                java.util.logging.Logger.getLogger(Httpfunctions.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return apiResponse;
    }
    public static String obtainBridgeHandshake(String authenticationUrl, String username, String password) {
        String sessionId = "";
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers1 = new HttpHeaders();
            headers1.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> parametersMap = new LinkedMultiValueMap<>();
            parametersMap.add("username", username);
            parametersMap.add("password", password);

            HttpEntity<MultiValueMap<String, String>> request1 = new HttpEntity<>(parametersMap, headers1);
            ResponseEntity<String> response1 = restTemplate.postForEntity(authenticationUrl, request1, String.class);
            LOGGER.info("Spotcash Bridge Services Authentication Response: " + response1);
            List<String> cookies = response1.getHeaders().get("Set-Cookie");
            if (cookies != null) {
                for (String cookie : cookies) {
                    LOGGER.info("CookieP Test:" + cookie);
                    sessionId = cookie.substring(cookie.indexOf("JSESSIONID=") + "JSESSIONID=".length(), cookie.indexOf(";"));
                }
            }
            LOGGER.info("Session id:" + sessionId);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("Error Occurred during Auth: ", e);
            sessionId = e.getMessage();
        }
        return sessionId;
    }



    public static String POSTRequestWithSession(String URL, Object requestBody, String sessionId) {
        try {
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.add("Cookie", "JSESSIONID=" + sessionId);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.getMessageConverters().add(new MappingJackson2HttpMessageConverter());
            HttpEntity<Object> request = new HttpEntity<>(requestBody, requestHeaders);
            String response = restTemplate.postForObject(URL, request, String.class);
            return response;
        } catch (Exception ex) {
            ex.printStackTrace();
            LOGGER.error("Error Occured:" + ex.getMessage());
            return null;
        }
    }
}

