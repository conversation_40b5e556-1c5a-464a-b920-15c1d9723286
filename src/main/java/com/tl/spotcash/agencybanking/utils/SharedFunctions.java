package com.tl.spotcash.agencybanking.utils;

import com.google.gson.Gson;
import com.tl.spotcash.agencybanking.custommodels.AdditionalMsgResponse;
import com.tl.spotcash.agencybanking.custommodels.CbsRequestData;
import com.tl.spotcash.agencybanking.custommodels.CbsResponseData;
import com.tl.spotcash.agencybanking.entity.SpMsgTemplates;
import com.tl.spotcash.agencybanking.entity.SpXmlTemplates;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.CredentialsProvider;
import org.apache.hc.client5.http.auth.NTCredentials;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;

import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.message.StatusLine;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.ssl.TrustStrategy;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.*;

import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

public class SharedFunctions {

    private static final Logger LOGGER = LoggerFactory.getLogger(SharedFunctions.class);

    @Autowired
    Environment environment;


    public static String makeGetRequest(String endpoint) { //Use PHP file to do TRX
        String response = "";
        LOGGER.info("endpoint: " + endpoint);
        try {
            URL url = new URL(endpoint);
            URLConnection url_connection = url.openConnection();
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(
                            url_connection.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                response = response + inputLine;
            }

            LOGGER.info("Response :" + response);

            return response;
        } catch (Exception ex) {
            return null;
        }
    }

    public static String makeGetRequest(String endpoint, Environment environment) { //Use PHP file to do TRX
        String response = "";
        LOGGER.info("endpoint: " + endpoint);

        try {
            String ip = InetAddress.getLocalHost().toString().split("/")[1];
            LOGGER.info("IP ADDRESS: " + ip);

            if(ip.equals(environment.getRequiredProperty("datasource.spotcash.testServerAddress"))) {
                endpoint = environment.getRequiredProperty("datasource.spotcash.smsSendingUrlTest");
                LOGGER.info("MOCK IP ENDPOINT: " + endpoint);
            }

            URL url = new URL(endpoint);
            URLConnection url_connection = url.openConnection();
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(
                            url_connection.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                response = response + inputLine;
            }

            LOGGER.info("Response :" + response);

            return response;
        } catch (IOException e) {
            return null;
        }
    }

    public XMLGregorianCalendar dateStringToXMLGregorianCalendarDate(String dateInput, String dateFormat) {
        LOGGER.info("Converting Date :" + dateInput + " with Format " + dateFormat);
        String dFormat = dateFormat.length() == 0 ? "yyyy-MM-dd hh:mm:ss" : dateFormat;

        XMLGregorianCalendar xmlGregCal;
        try {
            DateFormat format = new SimpleDateFormat(dFormat);
            Date date = format.parse(dateInput);

            GregorianCalendar cal = new GregorianCalendar();
            cal.setTime(date);

            xmlGregCal = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        } catch (DatatypeConfigurationException | ParseException ex) {
            ex.printStackTrace();
            LOGGER.error(ex.getMessage(), ex);
            xmlGregCal = null;
        }
        return xmlGregCal;
    }

    ///Customer agent transaction ID, Special transfer
    public String xmlRequestFormatter(String xmlTemplate, HashMap<String, String> requestMap){
        Set<String> requestKeys = new HashSet<>(Arrays.asList("phoneNumber", "transactionId", "msisdn", "serviceId", "amount",
                "trnx_charges", "account_number", "otherAccountNumber", "fkey", "idNumber", "agentPhoneNumber", "customerType",
                "description", "startDate", "endDate", "startTime", "endTime", "firstName", "middleName", "branchCode", "surname",
                "iDNo", "pinNo", "address", "applicationNo", "gender", "occupation", "phoneNo", "email", "passportPhoto", "frontID",
                "backID", "signature", "dateOfBirth", "introducer", "maritalStatus", "iDIssueDate", "reference", "emailAddress",
                "cr_account", "status", "mobileServiceId", "mobilePhoneNo", "requestID", "identifier"));

        for(String requestKey : requestKeys){
            xmlTemplate = requestMap.containsKey(requestKey) && !ObjectUtils.isEmpty(requestMap.get(requestKey))
                    ? xmlTemplate.replace(String.format("${%s}", requestKey) ,requestMap.get(requestKey))
                    : xmlTemplate.replace(String.format("${%s}", requestKey),"");
        }

        return xmlTemplate;
    }

    public String responseFormatter(SpXmlTemplates spXmlTemplates, String response, String key) throws JSONException {
        JSONObject jsonObject = new JSONObject(spXmlTemplates.getResponseKeys());
        String keytoFetch = jsonObject.getString(key);
        String[] nestLevel = keytoFetch.split("><");
        List<String> al = new ArrayList<String>();
        al = Arrays.asList(nestLevel);
        String firstString = al.get(0)+">";
        String secondString = "<"+al.get(1);
        if (response.contains(firstString)) {
            response = response.substring(response.indexOf(firstString) + firstString.length(), response.indexOf(secondString));
        }
        else response="";
        return response;
    }


    public String postToCbs(String functionName,String request,String Url, String cbsUser, String cbsPassword){

        try {
            CredentialsProvider credsProvider = new BasicCredentialsProvider();
            AuthScope authScope = new AuthScope(null,null,-1,null,null);
            String separator = "\\\\";
            String[] cbsUser1 = cbsUser.split(separator);

            String cbsDomain = "";
            if(cbsUser1.length > 1){
                cbsDomain = cbsUser1[0];
                cbsUser = cbsUser1[1];
            }

            ((BasicCredentialsProvider) credsProvider).setCredentials(authScope,new NTCredentials(cbsUser, cbsPassword.toCharArray(), "localhost",cbsDomain));
            HttpClient httpClient = HttpClientBuilder.create().setDefaultCredentialsProvider(credsProvider).build();
            CloseableHttpClient httpClient1 = HttpClients.custom()
                    .setConnectionManager(PoolingHttpClientConnectionManagerBuilder.create()
                            .setSSLSocketFactory(SSLConnectionSocketFactoryBuilder.create()
                                    .setSslContext(SSLContextBuilder.create()
                                            .loadTrustMaterial(TrustAllStrategy.INSTANCE)
                                            .build())
                                    .setHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                                    .build())
                            .build()).setDefaultCredentialsProvider(credsProvider).build();




            HttpPost post = new HttpPost(Url);
            StringEntity input = new StringEntity(request);
            post.setEntity(input);

            post.setHeader("Content-type", "text/xml");
            post.setHeader("Content-Encoding","UTF-8");
            post.setHeader("SOAPAction",functionName);
            CloseableHttpResponse response = (CloseableHttpResponse) httpClient1.execute(post);
            response.getEntity().getContent();
            String response2 = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            return response2;


        }
        catch(Exception e){
            e.printStackTrace();
            return null;
        }




    }

    // Replace tags with actual reference
    public String messageTemplateFormatter(SpMsgTemplates messageTemplate, CbsRequestData cbsRequestData, CbsResponseData cbsResponseData, AdditionalMsgResponse msgResponse){
        try {
            String msg = null;
            if(null != messageTemplate.getTemplateMessage()) {
                msg = messageTemplate.getTemplateMessage();
            }
            else {
                return null;
            }
            if (messageTemplate.getStatus().toString().equals("1")) {
                msg = msg.replace("<customer_name>", cbsRequestData.getCustomer_name() == null ? "" : cbsRequestData.getCustomer_name());
                msg = msg.replace("<transaction_id>", cbsRequestData.getTransactionId() == null ? "" : cbsRequestData.getTransactionId());
                msg = msg.replace("<account_number>", cbsRequestData.getAccount_number() == null ? "" : cbsRequestData.getAccount_number());
                msg = msg.replace("<amount>", (cbsRequestData.getServiceId().equalsIgnoreCase("700") || cbsRequestData.getServiceId().equalsIgnoreCase("701") || cbsRequestData.getServiceId().equalsIgnoreCase("702") || cbsRequestData.getServiceId().equalsIgnoreCase("703") || cbsRequestData.getServiceId().equalsIgnoreCase("704") || cbsRequestData.getServiceId().equalsIgnoreCase("739") || cbsRequestData.getServiceId().equalsIgnoreCase("751") || cbsRequestData.getServiceId().equalsIgnoreCase("752") || cbsRequestData.getServiceId().equalsIgnoreCase("753") || cbsRequestData.getServiceId().equalsIgnoreCase("754")) && !cbsResponseData.getResponseMessage().equalsIgnoreCase("")?cbsResponseData.getResponseMessage(): cbsResponseData.getAmount());
                msg = msg.replace("<account_name>", cbsRequestData.getAccountName() == null ? "" : cbsRequestData.getAccountName());
                msg = msg.replace("<destination_account>", cbsRequestData.getDestinationAccount() == null ? "" : cbsRequestData.getDestinationAccount());
                msg = msg.replace("<client_name>", msgResponse.getClientName() == null ? "" : msgResponse.getClientName());
                msg= msg.replace("agent_name", msgResponse.getAgentName() == null ? "" : msgResponse.getAgentName());
                msg = msg.replace("<agent_store_code>", cbsRequestData.getAgent_store_code() == null ? "" : cbsRequestData.getAgent_store_code());
                msg = msg.replace("<transaction_charge>", cbsRequestData.getTrnx_charges() == null ? "" : cbsRequestData.getTrnx_charges());
                msg = msg.replace("<message>", cbsResponseData.getMessage() == null?"" : cbsResponseData.getMessage());
                msg = msg.replace("<customer_type>", cbsRequestData.getCustomerType() == null?"" : cbsRequestData.getCustomerType());
                msg = msg.replace("<balance>", cbsResponseData.getResponseMessage().equalsIgnoreCase("")?cbsResponseData.getResponseMessage(): cbsResponseData.getBalance());
                msg = msg.replace("<service_id>", cbsRequestData.getServiceId() == null?"": cbsRequestData.getServiceId());
                msg = msg.replace("<customer_phone_number>", cbsRequestData.getMsisdn() == null?"": cbsRequestData.getMsisdn());
                msg = msg.replace("<agent_phone_number>", cbsRequestData.getAgentPhoneNumber() == null?"": cbsRequestData.getAgentPhoneNumber());
                msg = msg.replace("<description>", cbsRequestData.getDescription() == null?"": cbsRequestData.getDescription());
                msg = msg.replace("<depositor_name>", cbsRequestData.getDepositorName() == null?"": cbsRequestData.getDepositorName());
                msg = msg.replace("<service_charge>", cbsRequestData.getService_charge() == null?"": cbsRequestData.getService_charge());
                msg = msg.replace("<transaction_type>", cbsResponseData.getTransactionType() == null?"": cbsResponseData.getTransactionType());
                msg = msg.replace("<status>", cbsResponseData.getStatus() == null? "": cbsResponseData.getStatus());
                msg = msg.replace("cbs_message", cbsResponseData.getMessage() == null? "": cbsResponseData.getMessage());
                msg = msg.replace("<cbs_response_message>", cbsResponseData.getResponseMessage() == null?"": cbsResponseData.getResponseMessage());
                msg = msg.replace("<ministatement>", msgResponse.getMiniStatement() == null?"": msgResponse.getMiniStatement());
                msg = msg.replace("<token>", msgResponse.getGeneratedToken() == null?"": msgResponse.getGeneratedToken());
                msg = msg.replace("<token_receipt>", msgResponse.getGeneratedTokenReceipt() == null?"": msgResponse.getGeneratedTokenReceipt());
                msg = msg.replace("<token_units>", msgResponse.getTokenUnits() == null?"": msgResponse.getTokenUnits());
                msg = msg.replace("<kplc_postpaid_customer_message>",msgResponse.getKplcPostPayCustomerMessage() == null?"":msgResponse.getKplcPostPayCustomerMessage());
                msg = msg.replace("<dest_account>", cbsRequestData.getStoreNo() == null ? "" : cbsRequestData.getStoreNo());
                msg = msg.replace("<date_time>", LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss")));
                msg = msg.replace("<ref_id>", (cbsRequestData.getAgentNo() == null || cbsRequestData.getAgentNo().isEmpty()) ?"":cbsRequestData.getAgentNo());
                msg = msg.replace("<dest_account>", (cbsRequestData.getAgentNo() == null || cbsRequestData.getAgentNo().isEmpty()) ?"":cbsRequestData.getAgentNo());
                msg = msg.replace("<kplc_postpaid_receipt>", msgResponse.getKplcPostPayReceipt() == null?"": msgResponse.getKplcPostPayReceipt());

                return msg;
            }
            else {
                return null;
            }
        } catch (Exception e){
            e.printStackTrace();
            return null;
        }
    };

    public String messageTemplateFormatter(SpMsgTemplates messageTemplate, HashMap<String, String> payload){
        String msg = "";
        try {
            if (messageTemplate != null && messageTemplate.getStatus().toString().equals("1")
                    && !ObjectUtils.isEmpty(messageTemplate.getTemplateMessage())){
                String msgTemplate = messageTemplate.getTemplateMessage();

                for (Map.Entry<String, String> entry : payload.entrySet()) {
                    String placeholder = "<" + entry.getKey() + ">";
                    msg = msgTemplate.replaceAll(placeholder, entry.getValue());
                }
            }
        }
        catch (Exception e){e.printStackTrace(); msg = "";}

        return msg;
    };

    public XMLGregorianCalendar timeStringToXMLGregorianCalendarTime(String timeInput) {
        LOGGER.info("Converting Date :" + timeInput + " with Format HH:mm:ss");

        String[] arrOfStr = timeInput.split(":");

        int hours = Integer.parseInt(arrOfStr[0]);
        int minutes = Integer.parseInt(arrOfStr[1]);
        int seconds = Integer.parseInt(arrOfStr[2]);

        XMLGregorianCalendar xmlGregCal;
        try {
            xmlGregCal = DatatypeFactory.newInstance()
                    .newXMLGregorianCalendarTime(hours, minutes, seconds, 0);
        } catch (DatatypeConfigurationException ex) {
            ex.printStackTrace();
            LOGGER.error(ex.getMessage(), ex);
            xmlGregCal = null;
        }
        return xmlGregCal;
    }

    /**
     * mobile number should be start with a 7
     *
     * @param phoneNumber | The phone number
     * @return String
     */
    public static boolean validPhoneNumber(String phoneNumber) {
        boolean valid = false;
        if (!phoneNumber.equals("")) {
            if ((phoneNumber.substring(0, 3).matches("254") || phoneNumber.substring(0, 2).matches("07") || phoneNumber.substring(0, 2).matches("01")) && (phoneNumber.length() == 10 || phoneNumber.length() == 12)) {
                valid = true;
            }
        }
        return valid;
    }

    public static List<String> splitStringEqually(String text, int size) {
        List<String> result = new ArrayList<>((text.length() + size - 1) / size);
        for (int i = 0; i < text.length(); i += size) {
            result.add(text.substring(i, Math.min(text.length(), i + size)));
        }
        return result;
    }

    public String formatToken(String token) {
        List<String> response = splitStringEqually(token, 4);
        String lc = "";
        for (String s : response) {
            lc = lc + "-" + s;
        }
        return lc.substring(1);
    }

    public static String getRemoteIpAddress(HttpServletRequest request, Environment environment) {
        String ipAddress = request.getRemoteAddr();
        if (ipAddress.equals(environment.getRequiredProperty("datasource.spotcash.proxyIp"))) {
            // probably behind a proxy
            ipAddress = !StringUtils.isEmpty(request.getHeader("X-FORWARDED-FOR")) ? request.getHeader("X-FORWARDED-FOR") : ipAddress;
        }
        return ipAddress;
    }

    public static ResponseEntity<String> postRequest(String url, Object requestBody, String token){
        Gson gson = new Gson();
        String json = gson.toJson(requestBody);
        System.out.println(json);
        try{
            HttpHeaders headers = new HttpHeaders();
            //headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set("X-Requested-With", "XMLHttpRequest");
            headers.set("Content-Type", "application/json");
            if (null != token) {
                headers.set("ULINZI", "Bearer " + token);
            }
            HttpEntity<Object> requestHeaders = new HttpEntity(json,headers);
            // make an HTTP POST request with headers
            LOGGER.info("Making request ---> url: {}", url);
            LOGGER.info("Making request ---> headers: {}", headers);
            LOGGER.info("Making request ---> body: {}", requestBody.toString());
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestHeaders, String.class);
            LOGGER.info("Request response ---> {}",  response.toString());
            return response;
        } catch (Exception e) {
            LOGGER.error("Failed makeRequest Exception ",  e);
            return null;
        }
    }
    public static ResponseEntity<String> makeGetRequest(String url, Object requestBody, String token){
        Gson gson = new Gson();
        String json = gson.toJson(requestBody);
        try{
            HttpHeaders headers = new HttpHeaders();
            //headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set("X-Requested-With", "XMLHttpRequest");
            headers.set("Content-Type", "application/json");
            if (null != token) {
                headers.set("ULINZI", "Bearer " + token);
            }
            HttpEntity<Object> requestHeaders = new HttpEntity(json,headers);
            // make an HTTP POST request with headers
            LOGGER.info("Making request ---> url: {}", url);
            LOGGER.info("Making request ---> headers: {}", headers);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestHeaders, String.class);
            LOGGER.info("Request response ---> {}",  response.toString());
            return response;
        } catch (Exception e) {
            LOGGER.error("Failed makeRequest Exception ",  e);
            return null;
        }
    }

    public String getSecretKey(Environment environment) {
        return Crypt.encodeKey(environment.getRequiredProperty("bridgeEncryptionKey.key"));
    }
    //    public  String createChainTrx(HashMap<String, String> maps) {
//        try {
//            // Chain trx fxn
//            String chainKey = Crypt.encodeKey(env.getRequiredProperty("chain.key"));
//            ObjectMapper mapper = new ObjectMapper();
//            // Update JSON with values from maps
//            for (Map.Entry<String, String> entry : maps.entrySet()) {
//                String key = entry.getKey();
//                String value = entry.getValue();
//                jsonChain.put(key, value);
//            }
//
//            String updatedChain = jsonChain.toString();
//            LOGGER.debug("Updated chain is "+updatedChain);
//            String encryptedUpdatedChain = Crypt.encrypt(updatedChain,chainKey);
//            return encryptedUpdatedChain;
//        }
//        catch (Exception e){
//            e.printStackTrace();
//            LOGGER.error("Error on Chain Trx {}"+e.getMessage());
//        }
//        return null;
//    }

    public String updateChainTrx(String chain, HashMap<String, String> maps,String enckey1, String encKey2) {
        try {
            String chainKey = TrxVal.pushKey(formatKeyLength(enckey1+"-"+encKey2));
            String decodedChain = "";

            JSONObject jsonChain;

            // Parse decodedChain into a JSONObject
            if (chain!=null) {
                decodedChain = TrxVal.undoval(chain, chainKey);
                jsonChain = new JSONObject(decodedChain);
            }
            else{
                jsonChain = new JSONObject();
            }
            // Update JSON with values from maps
            for (Map.Entry<String, String> entry : maps.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (jsonChain.has(key)){
                    jsonChain.put(key+"1", value);
                }
                else{
                    jsonChain.put(key, value);
                }
            }

            String updatedChain = jsonChain.toString();
            //LOGGER.info("Updated chain is "+updatedChain);
            String encryptedUpdatedChain = TrxVal.doval(updatedChain,chainKey);
            return encryptedUpdatedChain;
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("Error occurred while updating chain: " + e.getMessage());
            return null;
        }
    }

    public String formatKeyLength(String key){
        String originalString = key;
        int desiredLength = 32;
        String paddedString = "";
// Pad the string with "0" characters to the left
        if(key.length()<=desiredLength) {
            paddedString= org.apache.commons.lang3.StringUtils.leftPad(originalString, desiredLength, '-');
        }
        else{
            paddedString = key.substring(0, desiredLength);
        }
        return paddedString;
    }

    public static ResponseEntity<String> postJSONRequest(String url, Object requestBody, String token){
        Gson gson = new Gson();
        String json = gson.toJson(requestBody);
        System.out.println(json);
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.set("Content-Type", "application/json");
            if (!ObjectUtils.isEmpty(token)) headers.set("Authorization", "Bearer " + token);
            HttpEntity<Object> requestHeaders = new HttpEntity(json,headers);
            // make an HTTP POST request with headers
            LOGGER.info("Making request ---> url: {}", url);
            LOGGER.info("Making request ---> headers: {}", headers);
            LOGGER.info("Making request ---> body: {}", requestBody.toString());
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestHeaders, String.class);
            LOGGER.info("Request response ---> {}", response);
            return response;
        }
        catch (Exception e) {
            LOGGER.error("Failed makeRequest Exception ",  e);
            return null;
        }
    }

}
