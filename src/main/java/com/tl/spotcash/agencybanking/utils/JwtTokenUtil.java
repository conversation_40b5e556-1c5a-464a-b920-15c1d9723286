package com.tl.spotcash.agencybanking.utils;

import com.tl.spotcash.agencybanking.crudservice.CrudTransactionController;
import com.tl.spotcash.agencybanking.entity.SpAgencyEncryptionKey;
import com.tl.spotcash.agencybanking.entity.SpAgentStores;
import com.tl.spotcash.agencybanking.entity.SpStoreUsers;
import com.tl.spotcash.agencybanking.enums.StoreUser;
import com.tl.spotcash.agencybanking.service.WorkingHoursService;
import io.jsonwebtoken.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Date;
import java.util.HashMap;

@Component
public class JwtTokenUtil {
    @Value("${app.jwt.accessTokenExpiration}")
    private String ACCESS_TOKEN_EXPIRATION;

    @Value("${app.jwt.refreshTokenExpiration}")
    private String REFRESH_TOKEN_EXPIRATION;

    @Value("${app.jwt.secret}")
    private String SECRET_KEY;
    @Value("${app.jwt.signingKey}")
    private String SIGNING_KEY;

    @Autowired
    WorkingHoursService workingHoursService;
    @Autowired
    Environment environment;
    @Autowired
    private CrudTransactionController crudTransactions;

    private static final Logger LOGGER = LoggerFactory.getLogger(JwtTokenUtil.class);

    public String generateAccessToken(SpStoreUsers storeUser, String agentId1, String dataKey1,
                                      String encryptionKey, boolean includeDeviceId) throws Exception {
        //Fetch the created dynamic encryption key and use it to encrypt the claims of the token
        SpAgentStores agentStore = crudTransactions.fetchAgentWithStoreId(storeUser.getAgentStoreId());
        if(agentStore != null){
            /*SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    agentStore.getDeviceId(), storeUser.getClientId().toString());
            //If the dynamic key was not created, use the static key inorder to support older APK which were using the static key
            String encryptionKey = spAgencyEncryptionKey != null
                    ? spAgencyEncryptionKey.getEncryptionKey()
                    : SECRET_KEY;*/

            String workingHours1 = workingHoursService.fetchAgentWorkingHours(storeUser.getAgentStoreId());
            String storeUserId = Crypt.encrypt(String.valueOf(storeUser.getStoreUserId()), encryptionKey);
            String agentStoreId = Crypt.encrypt(String.valueOf(storeUser.getAgentStoreId()), encryptionKey);
            String agentId = Crypt.encrypt(agentId1, encryptionKey);
            String clientId = Crypt.encrypt(String.valueOf(storeUser.getClientId()), encryptionKey);
            String contactMsisdn = Crypt.encrypt(storeUser.getContactMsisdn(), encryptionKey);
            String workingHours = Crypt.encrypt(workingHours1, encryptionKey);
            String dataKey = Crypt.encrypt(dataKey1, encryptionKey);

            HashMap<String, Object> claims = new HashMap<>();
            claims.put(StoreUser.STORE_USER_ID.getValue(), storeUserId);
            claims.put(StoreUser.AGENT_STORE_ID.getValue(), agentStoreId);
            claims.put(StoreUser.AGENT_ID.getValue(), agentId);
            claims.put(StoreUser.CLIENT_ID.getValue(), clientId);
            claims.put(StoreUser.CONTACT_MSISDN.getValue(), contactMsisdn);
            claims.put(StoreUser.WORKING_HOURS.getValue(), workingHours);
            claims.put(StoreUser.DATA_KEY.getValue(), dataKey);
            if(includeDeviceId/*spAgencyEncryptionKey != null*/){
                //DeviceID and clientId are not encrypted if a dynamic encryption key is used
                claims.put(StoreUser.CLIENT_ID.getValue(), storeUser.getClientId());
                claims.put(StoreUser.DEVICE_ID.getValue(), agentStore.getDeviceId());
            }

            return Jwts.builder()
                    .setClaims(claims)
                    //.setSubject(String.format("%s,%s", storeUser.getStoreUserId(), storeUser.getContactName()))
                    //.setIssuer("agencySwitch")
                    .setIssuedAt(new Date())
                    .setExpiration(new java.sql.Date(System.currentTimeMillis() + Long.parseLong(ACCESS_TOKEN_EXPIRATION)))
                    .signWith(SignatureAlgorithm.HS512, SIGNING_KEY)
                    .compact();
        }
        else return null;
    }

    public String generateRefreshToken(SpStoreUsers storeUser, String agentId) throws Exception {
        //Fetch the created dynamic encryption key and use it to encrypt the claims of the token
        SpAgentStores agentStore = crudTransactions.fetchAgentWithStoreId(storeUser.getAgentStoreId());
        if(agentStore != null) {
            SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(
                    agentStore.getDeviceId(), storeUser.getClientId().toString());

            String encryptionKey = spAgencyEncryptionKey != null
                    ? spAgencyEncryptionKey.getEncryptionKey()
                    : SECRET_KEY;

            HashMap<String, Object> claims = new HashMap<>();
            String storeUserId = Crypt.encrypt(String.valueOf(storeUser.getStoreUserId()), encryptionKey);
            agentId = Crypt.encrypt(agentId, encryptionKey);

            claims.put(StoreUser.STORE_USER_ID.getValue(), storeUserId);
            claims.put(StoreUser.AGENT_ID.getValue(), agentId);
            if(spAgencyEncryptionKey != null){
                //DeviceID and clientId are not encrypted if a dynamic encryption key is used
                claims.put(StoreUser.CLIENT_ID.getValue(), storeUser.getClientId());
                claims.put(StoreUser.DEVICE_ID.getValue(), agentStore.getDeviceId());
            }

            return Jwts.builder()
                    .setClaims(claims)
                    .setIssuedAt(new Date())
                    .setExpiration(new java.sql.Date(System.currentTimeMillis() + Long.parseLong(REFRESH_TOKEN_EXPIRATION)))
                    .signWith(SignatureAlgorithm.HS512, SIGNING_KEY)
                    .compact();
        }
        else return null;
    }

    public boolean validateAccessToken(String token) {
        try {
            Jwts.parser().setSigningKey(SIGNING_KEY).parseClaimsJws(token);
            return true;
        } catch (ExpiredJwtException ex) {
            LOGGER.error("JWT expired", ex.getMessage());
        } catch (IllegalArgumentException ex) {
            LOGGER.error("Token is null, empty or only whitespace", ex.getMessage());
        } catch (MalformedJwtException ex) {
            LOGGER.error("JWT is invalid", ex);
        } catch (UnsupportedJwtException ex) {
            LOGGER.error("JWT is not supported", ex);
        } catch (SignatureException ex) {
            LOGGER.error("Signature validation failed");
        }

        return false;
    }

    public String getSubject(String token) {
        //return parseClaims(token).getSubject();
        return parseClaims(token).get(StoreUser.STORE_USER_ID.getValue(), String.class);
    }

    public Claims parseClaims(String token) {
        return Jwts.parser()
                .setSigningKey(SIGNING_KEY)
                .parseClaimsJws(token)
                .getBody();
    }

    public String getClaim(String token, String claimKey){
        //Note: Values of the claims are encrypted using a static secret key(Older APKS 1.8 and below) or dynamic secret Key(new APK version 1.9+)
        Claims claims = parseClaims(token);
        String claim;

        boolean deviceIdPresent = claims.containsKey(StoreUser.DEVICE_ID.getValue());
        boolean fetchUnEncryptedClaims =
                (claimKey.equals(StoreUser.DEVICE_ID.getValue()) || claimKey.equals(StoreUser.CLIENT_ID.getValue()))
                && deviceIdPresent;

        String encryptionKey = SECRET_KEY;

        if(fetchUnEncryptedClaims){
            //Only new APK's version 1.9+ have the device id in the token
            //(NB: the deviceID and ClientId are un encrypted in this case. ALl the other claims are encrypted)
            // Retrieve the claim as an Object
            Object claimValue = claims.get(claimKey);
            if (claimValue instanceof Integer) { claim = String.valueOf(claimValue);}
            else claim = claims.get(claimKey, String.class);
        }
        else {
            //Change the encryption Key if the deviceId is present
            if(deviceIdPresent){
                //Fetch the dynamic secret Key using deviceId and ClientId(which were added in the token on generation with no encryption)
                // as all tokens which contain deviceId are for APK's version 1.9+
                String deviceId = claims.get(StoreUser.DEVICE_ID.getValue(), String.class);
                String clientId = String.valueOf(claims.get(StoreUser.CLIENT_ID.getValue(), Integer.class));

                SpAgencyEncryptionKey spAgencyEncryptionKey = crudTransactions.fetchEncryptionKey(deviceId, clientId);
                //If the dynamic key was not created, use the static key inorder to support older APK which were using the static key
                if(spAgencyEncryptionKey != null) encryptionKey = spAgencyEncryptionKey.getEncryptionKey();
            }

            if(claimKey.equals(StoreUser.DEVICE_ID.getValue())) claim = "";
            else claim = Crypt.decrypt(claims.get(claimKey, String.class).replace("\n", ""), encryptionKey);
        }

        LOGGER.info("This is the claim -----" + claim);

        return claim;
    }

    public boolean HasTokenExpired(String token){
        try {
            Claims claims = Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token).getBody();
            long expireTime = claims.getExpiration().getTime();
            long currentTime = new Date().getTime();
            long timeToExpiry = expireTime - currentTime;

            //2 min leeway
            return timeToExpiry <= 120000;

        } catch (ExpiredJwtException ex) {
            LOGGER.error("JWT expired", ex.getMessage());
        } catch (IllegalArgumentException ex) {
            LOGGER.error("Token is null, empty or only whitespace", ex.getMessage());
        } catch (MalformedJwtException ex) {
            LOGGER.error("JWT is invalid", ex);
        } catch (UnsupportedJwtException ex) {
            LOGGER.error("JWT is not supported", ex);
        } catch (SignatureException ex) {
            LOGGER.error("Signature validation failed");
        }

        return true;
    }

    public HttpHeaders getUpdatedTokenHeaders(HttpServletResponse response) {
        HttpHeaders headers = new HttpHeaders();
        String accessToken = response.getHeader("WatchDog");
        String refreshToken = response.getHeader("Refresh-Token");

        if(accessToken != null) headers.add("WatchDog", accessToken);
        if(refreshToken != null) headers.add("Refresh-Token", refreshToken);

        return headers;
    }

    public String generateRandomKey(int length) {
        final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_"; //!@#$%^&*()_+[]{}|;:,.<>?
        SecureRandom random = new SecureRandom();
        StringBuilder key = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHARACTERS.length());
            char randomChar = CHARACTERS.charAt(randomIndex);
            key.append(randomChar);
        }

        return key.toString();
    }

}
