package com.tl.spotcash.agencybanking.utils;

import org.springframework.stereotype.Component;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * Utility class to convert Oracle-specific SQL syntax to MySQL syntax.
 * This class provides methods to automatically convert common Oracle functions
 * and syntax to their MySQL equivalents.
 * 
 * <AUTHOR>
 */
@Component
public class OracleToMySQLConverter {

    /**
     * Convert Oracle ROWNUM to MySQL LIMIT syntax.
     * 
     * @param query Original query with ROWNUM
     * @return Query with MySQL LIMIT syntax
     */
    public String convertRownum(String query) {
        // Pattern to match "AND ROWNUM <= n" or "WHERE ROWNUM <= n"
        Pattern rownumPattern = Pattern.compile("(?i)\\s+(and\\s+|where\\s+)?rownum\\s*<=\\s*(\\d+)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = rownumPattern.matcher(query);
        
        if (matcher.find()) {
            String limit = matcher.group(2);
            // Remove the ROWNUM clause and add LIMIT at the end
            String convertedQuery = matcher.replaceAll("");
            
            // Add ORDER BY if not present (MySQL LIMIT without ORDER BY can be unpredictable)
            if (!convertedQuery.toLowerCase().contains("order by")) {
                // Try to find a primary key or id column to order by
                if (convertedQuery.toLowerCase().contains("from sp")) {
                    convertedQuery += " ORDER BY id";
                }
            }
            
            convertedQuery += " LIMIT " + limit;
            return convertedQuery;
        }
        
        return query;
    }

    /**
     * Convert Oracle SYSDATE to MySQL NOW() or CURDATE().
     * 
     * @param query Original query with SYSDATE
     * @param includeTime Whether to include time (NOW()) or just date (CURDATE())
     * @return Query with MySQL date function
     */
    public String convertSysdate(String query, boolean includeTime) {
        String replacement = includeTime ? "NOW()" : "CURDATE()";
        return query.replaceAll("(?i)sysdate", replacement);
    }

    /**
     * Convert Oracle TO_CHAR date formatting to MySQL DATE_FORMAT.
     * 
     * @param query Original query with TO_CHAR
     * @return Query with MySQL DATE_FORMAT
     */
    public String convertToChar(String query) {
        // Convert TO_CHAR(date, 'DD-MM-YYYY') to DATE_FORMAT(date, '%d-%m-%Y')
        Pattern toCharPattern = Pattern.compile("(?i)to_char\\s*\\(\\s*([^,]+)\\s*,\\s*'([^']+)'\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = toCharPattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String dateExpression = matcher.group(1);
            String oracleFormat = matcher.group(2);
            String mysqlFormat = convertDateFormat(oracleFormat);
            
            String replacement = "DATE_FORMAT(" + dateExpression + ", '" + mysqlFormat + "')";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * Convert Oracle TO_TIMESTAMP to MySQL STR_TO_DATE.
     * 
     * @param query Original query with TO_TIMESTAMP
     * @return Query with MySQL STR_TO_DATE
     */
    public String convertToTimestamp(String query) {
        // Convert TO_TIMESTAMP('value', 'format') to STR_TO_DATE('value', 'format')
        Pattern toTimestampPattern = Pattern.compile("(?i)to_timestamp\\s*\\(\\s*'([^']+)'\\s*,\\s*'([^']+)'\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = toTimestampPattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String dateValue = matcher.group(1);
            String oracleFormat = matcher.group(2);
            String mysqlFormat = convertDateFormat(oracleFormat);
            
            String replacement = "STR_TO_DATE('" + dateValue + "', '" + mysqlFormat + "')";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * Convert Oracle NVL to MySQL IFNULL.
     * 
     * @param query Original query with NVL
     * @return Query with MySQL IFNULL
     */
    public String convertNvl(String query) {
        // Convert NVL(expr1, expr2) to IFNULL(expr1, expr2)
        return query.replaceAll("(?i)nvl\\s*\\(", "IFNULL(");
    }

    /**
     * Convert Oracle SUBSTR to MySQL SUBSTRING or RIGHT.
     * 
     * @param query Original query with SUBSTR
     * @return Query with MySQL SUBSTRING or RIGHT
     */
    public String convertSubstr(String query) {
        // Convert SUBSTR(string, -n, n) to RIGHT(string, n) for negative positions
        Pattern substrNegPattern = Pattern.compile("(?i)substr\\s*\\(\\s*([^,]+)\\s*,\\s*-\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher negMatcher = substrNegPattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (negMatcher.find()) {
            String stringExpr = negMatcher.group(1);
            String length = negMatcher.group(3);
            
            String replacement = "RIGHT(" + stringExpr + ", " + length + ")";
            negMatcher.appendReplacement(result, replacement);
        }
        negMatcher.appendTail(result);
        
        // Convert remaining SUBSTR to SUBSTRING
        result = new StringBuffer(result.toString().replaceAll("(?i)substr\\s*\\(", "SUBSTRING("));
        
        return result.toString();
    }

    /**
     * Convert Oracle DECODE to MySQL CASE WHEN.
     * 
     * @param query Original query with DECODE
     * @return Query with MySQL CASE WHEN
     */
    public String convertDecode(String query) {
        // This is a complex conversion, simplified version
        // DECODE(expr, search1, result1, search2, result2, ..., default)
        // becomes CASE WHEN expr = search1 THEN result1 WHEN expr = search2 THEN result2 ... ELSE default END
        
        Pattern decodePattern = Pattern.compile("(?i)decode\\s*\\(([^)]+)\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = decodePattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String decodeContent = matcher.group(1);
            String caseWhen = convertDecodeToCase(decodeContent);
            matcher.appendReplacement(result, caseWhen);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * Convert Oracle date arithmetic to MySQL INTERVAL syntax.
     * 
     * @param query Original query with Oracle date arithmetic
     * @return Query with MySQL INTERVAL syntax
     */
    public String convertDateArithmetic(String query) {
        // Convert "SYSDATE - (n / 60) / (24 * 60)" to "DATE_SUB(NOW(), INTERVAL n SECOND)"
        Pattern dateArithPattern = Pattern.compile("(?i)(sysdate|now\\(\\))\\s*-\\s*\\(\\s*(\\d+)\\s*/\\s*60\\s*\\)\\s*/\\s*\\(\\s*24\\s*\\*\\s*60\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = dateArithPattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String seconds = matcher.group(2);
            String replacement = "DATE_SUB(NOW(), INTERVAL " + seconds + " SECOND)";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * Convert Oracle INSTR to MySQL LOCATE.
     * 
     * @param query Original query with INSTR
     * @return Query with MySQL LOCATE
     */
    public String convertInstr(String query) {
        // Convert INSTR(string, substring) to LOCATE(substring, string)
        Pattern instrPattern = Pattern.compile("(?i)instr\\s*\\(\\s*([^,]+)\\s*,\\s*([^)]+)\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher matcher = instrPattern.matcher(query);
        
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String string = matcher.group(1);
            String substring = matcher.group(2);
            
            String replacement = "LOCATE(" + substring + ", " + string + ")";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);
        
        return result.toString();
    }

    /**
     * Convert Oracle LENGTH to MySQL CHAR_LENGTH.
     * 
     * @param query Original query with LENGTH
     * @return Query with MySQL CHAR_LENGTH
     */
    public String convertLength(String query) {
        return query.replaceAll("(?i)length\\s*\\(", "CHAR_LENGTH(");
    }

    /**
     * Perform comprehensive Oracle to MySQL conversion.
     * 
     * @param query Original Oracle query
     * @return MySQL-compatible query
     */
    public String convertQuery(String query) {
        String result = query;
        
        // Apply all conversions
        result = convertRownum(result);
        result = convertSysdate(result, true);
        result = convertToChar(result);
        result = convertToTimestamp(result);
        result = convertNvl(result);
        result = convertSubstr(result);
        result = convertDecode(result);
        result = convertDateArithmetic(result);
        result = convertInstr(result);
        result = convertLength(result);
        
        return result;
    }

    /**
     * Convert Oracle date format to MySQL date format.
     * 
     * @param oracleFormat Oracle date format string
     * @return MySQL date format string
     */
    private String convertDateFormat(String oracleFormat) {
        String mysqlFormat = oracleFormat;
        
        // Common Oracle to MySQL format conversions
        mysqlFormat = mysqlFormat.replace("YYYY", "%Y");
        mysqlFormat = mysqlFormat.replace("YY", "%y");
        mysqlFormat = mysqlFormat.replace("MM", "%m");
        mysqlFormat = mysqlFormat.replace("DD", "%d");
        mysqlFormat = mysqlFormat.replace("HH24", "%H");
        mysqlFormat = mysqlFormat.replace("HH", "%h");
        mysqlFormat = mysqlFormat.replace("MI", "%i");
        mysqlFormat = mysqlFormat.replace("SS", "%s");
        mysqlFormat = mysqlFormat.replace("FF", "%f");
        
        return mysqlFormat;
    }

    /**
     * Convert DECODE function content to CASE WHEN statement.
     * 
     * @param decodeContent Content inside DECODE function
     * @return CASE WHEN statement
     */
    private String convertDecodeToCase(String decodeContent) {
        String[] parts = decodeContent.split(",");
        if (parts.length < 3) {
            return "CASE " + decodeContent + " END"; // Fallback
        }
        
        StringBuilder caseWhen = new StringBuilder("CASE ");
        String expression = parts[0].trim();
        
        for (int i = 1; i < parts.length - 1; i += 2) {
            if (i + 1 < parts.length) {
                String condition = parts[i].trim();
                String result = parts[i + 1].trim();
                caseWhen.append("WHEN ").append(expression).append(" = ").append(condition)
                       .append(" THEN ").append(result).append(" ");
            }
        }
        
        // Add ELSE clause if odd number of parameters (last one is default)
        if (parts.length % 2 == 0) {
            caseWhen.append("ELSE ").append(parts[parts.length - 1].trim()).append(" ");
        }
        
        caseWhen.append("END");
        return caseWhen.toString();
    }

    /**
     * Check if a query contains Oracle-specific syntax.
     * 
     * @param query SQL query to check
     * @return true if Oracle-specific syntax is found
     */
    public boolean containsOracleSyntax(String query) {
        String lowerQuery = query.toLowerCase();
        
        return lowerQuery.contains("rownum") ||
               lowerQuery.contains("sysdate") ||
               lowerQuery.contains("to_char") ||
               lowerQuery.contains("to_timestamp") ||
               lowerQuery.contains("nvl(") ||
               lowerQuery.contains("decode(") ||
               lowerQuery.contains("instr(") ||
               lowerQuery.contains("substr(");
    }

    /**
     * Get conversion suggestions for a query.
     * 
     * @param query SQL query to analyze
     * @return String with conversion suggestions
     */
    public String getConversionSuggestions(String query) {
        StringBuilder suggestions = new StringBuilder();
        String lowerQuery = query.toLowerCase();
        
        if (lowerQuery.contains("rownum")) {
            suggestions.append("- Replace ROWNUM with LIMIT clause\n");
        }
        if (lowerQuery.contains("sysdate")) {
            suggestions.append("- Replace SYSDATE with NOW() or CURDATE()\n");
        }
        if (lowerQuery.contains("to_char")) {
            suggestions.append("- Replace TO_CHAR with DATE_FORMAT\n");
        }
        if (lowerQuery.contains("to_timestamp")) {
            suggestions.append("- Replace TO_TIMESTAMP with STR_TO_DATE\n");
        }
        if (lowerQuery.contains("nvl(")) {
            suggestions.append("- Replace NVL with IFNULL\n");
        }
        if (lowerQuery.contains("substr(")) {
            suggestions.append("- Replace SUBSTR with SUBSTRING or RIGHT\n");
        }
        if (lowerQuery.contains("decode(")) {
            suggestions.append("- Replace DECODE with CASE WHEN\n");
        }
        if (lowerQuery.contains("instr(")) {
            suggestions.append("- Replace INSTR with LOCATE\n");
        }
        
        return suggestions.toString();
    }
}
