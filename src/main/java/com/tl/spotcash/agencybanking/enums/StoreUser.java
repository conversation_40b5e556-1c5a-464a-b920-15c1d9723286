package com.tl.spotcash.agencybanking.enums;

public enum StoreUser {
    STORE_USER_ID("storeUserId"),
    AGENT_STORE_ID("agentStoreId"),
    CONTACT_NAME("contactName"),
    CONTACT_MSISDN("contactMsisdn"),
    AGENT_ID("agentId"),
    CLIENT_ID("clientId"),
    WORKING_HOURS("workingHours"),
    DATA_KEY("dataKey"),
    DEVICE_ID("deviceId");

    private String value;
    StoreUser(String value){this.value = value; }

    public String getValue() {return value;}
}
