package com.tl.spotcash.agencybanking;

import com.tl.spotcash.agencybanking.custommodels.PosResponseMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class JsonResponseConstructor {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(JsonResponseConstructor.class);
    
    public PosResponseMapper posResponse(ApiResponseModel resp) {
        PosResponseMapper responseMapper = new PosResponseMapper();
        try {
            Map<String, String> jsonresponseHeader = new HashMap<>();
            Map<String, Object> jsonresponseData = new HashMap<>();
            Map<String, String> httpResponse = new HashMap<>();
            
            if (null != resp.getAmt()) {
                jsonresponseData.put("amt", resp.getAmt());
            }
            if (null != resp.getEod()) {
                jsonresponseData.put("eod", resp.getAmt());
            }
            
            if (null != resp.getFeeamt()) {
                jsonresponseData.put("feeamt", resp.getFeeamt());
            }
            
            if (null != resp.getMsm()) {
                jsonresponseData.put("msm", resp.getMsm()); //msisdn field
            }
            
            if (null != resp.getResponsedata_sc()) {
                jsonresponseData.put("sc", resp.getResponsedata_sc());
            }
            
            if (null != resp.getAgent_id()) {
                jsonresponseData.put("agtId", resp.getAgent_id());
            }
            
            if (null != resp.getResponseheader_sc()) {
                jsonresponseHeader.put("sc", resp.getResponseheader_sc());
            }
            
            if (null != resp.getSd()) {
                jsonresponseHeader.put("sd", resp.getSd());
            }
            
            if (null != resp.getSod()) {
                jsonresponseData.put("sod", resp.getSod());
            }
            
            if (null != resp.getSt()) {
                jsonresponseHeader.put("st", resp.getSt());
            }
            
            if (null != resp.getTrx_id()) {
                jsonresponseHeader.put("txnId", resp.getTrx_id());
                jsonresponseData.put("txnId", resp.getTrx_id());
            }
            
            if (null != resp.getXid()) {
                jsonresponseData.put("xid", resp.getXid());
            }
            
            if (resp.getHttpStatus() != null) {
                responseMapper.setResponseCode(resp.getHttpStatus());
            }
            
            if (resp.getUtilityPaymentResponse() != null) {
//                jsonresponseData.put("utilityPaymentResponse", new JSONObject(resp.getUtilityPaymentResponse()));
                jsonresponseData.put("utilityPaymentResponse", resp.getUtilityPaymentResponse());
            }
            
            if (null != resp.getTrx_balance()) {
                jsonresponseHeader.put("txnBalance", resp.getTrx_balance());
                jsonresponseData.put("txnBalance", resp.getTrx_balance());
            }
            responseMapper.setHeader(jsonresponseHeader);
            responseMapper.setResponseData(jsonresponseData);
        } catch (Exception e) {
            LOGGER.error("Error occured {}", e.getMessage(), e);
        }
        return responseMapper;
    }
}
