package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 * @co-author mwend<PERSON><PERSON><PERSON>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "name",
    "nationalId"
})
public class Member {

    @JsonProperty("name")
    private String name;
    @JsonProperty("nationalId")
    private String nationalId;
    @JsonProperty("MemberImage")
    private String MemberImage;
    @JsonProperty("MemberNo")
    private String MemberNo;
    @JsonProperty("PhoneNo")
    private String PhoneNo;
    @Setter
    @Getter
    private boolean mobileWebservice = false;

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("nationalId")
    public String getNationalId() {
        return nationalId;
    }

    @JsonProperty("nationalId")
    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }

    @JsonProperty("MemberImage")
    public String getMemberImage() {
        return MemberImage;
    }

    @JsonProperty("MemberImage")
    public void setMemberImage(String MemberImage) {
        this.MemberImage = MemberImage;
    }

    @JsonProperty("MemberNo")
    public String getMemberNo() {
        return MemberNo;
    }

    @JsonProperty("MemberNo")
    public void setMemberNo(String MemberNo) {
        this.MemberNo = MemberNo;
    }

    @JsonProperty("PhoneNo")
    public String getPhoneNo() {
        return PhoneNo;
    }

    @JsonProperty("PhoneNo")
    public void setPhoneNo(String PhoneNo) {
        this.PhoneNo = PhoneNo;
    }

}