package com.tl.spotcash.agencybanking.custommodels;

import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 * @co-author mwendwakelvin
 */
public class CbsRequestData {

    private String msisdn;
    private String amount;
    private String clientId;
    private String service_charge;
    private String serviceId;
    private String transactionId;
    private String customer_name;
    private String transtemptableid;
    private String account_number;
    private String agent_store_code;
    private String agent_msisdn;
    private String trnx_charges;
    private String customerType;
    private String agentPhoneNumber;
    private String description;
    private String userId;
    private String newPrintDesign;
    private String accountName;
    private String withdrawal_sms;
    private String mobileServiceId;
    @Getter @Setter private String emailAddress;
    @Getter @Setter private String startDate;
    @Getter @Setter private String endDate;
    @Getter @Setter private String startTime;
    @Getter @Setter private String endTime;
    @Getter @Setter private String fKey;
    @Getter @Setter private String cr_account;
    @Getter @Setter private String depositorName;
    @Getter @Setter private String depositorIdNumber;
    @Getter @Setter private String agentNo;
    @Getter @Setter private String storeNo;
    @Getter @Setter private String destinationAccount;
    @Getter @Setter private String agent_transaction_sms;
    @Getter @Setter private String otherAccountNumber;


    public void setAgent_store_code(String agent_store_code) {
        this.agent_store_code = agent_store_code;
    }

    public String getAgent_store_code() {
        return agent_store_code;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public void setService_charge(String service_charge) {
        this.service_charge = service_charge;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public void setTranstemptableid(String transtemptableid) {
        this.transtemptableid = transtemptableid;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public String getAmount() {
        return amount;
    }

    public String getClientId() {
        return clientId;
    }

    public String getService_charge() {
        return service_charge;
    }

    public String getServiceId() {
        return serviceId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public String getTranstemptableid() {
        return transtemptableid;
    }

    public String getAccount_number() {
        return account_number;
    }

    public String getAgent_msisdn() {
        return agent_msisdn;
    }

    public void setAgent_msisdn(String agent_msisdn) {
        this.agent_msisdn = agent_msisdn;
    }

    public String getTrnx_charges() {
        return trnx_charges;
    }

    public void setTrnx_charges(String trnx_charges) {
        this.trnx_charges = trnx_charges;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getAgentPhoneNumber() {
        return agentPhoneNumber;
    }

    public void setAgentPhoneNumber(String agentPhoneNumber) {
        this.agentPhoneNumber = agentPhoneNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setNewPrintDesign(String newPrintDesign) {
        this.newPrintDesign = newPrintDesign;
    }

    public String getNewPrintDesign() {
        return newPrintDesign;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getWithdrawal_sms() { return withdrawal_sms; }

    public void setWithdrawal_sms(String withdrawal_sms) { this.withdrawal_sms = withdrawal_sms; }

    public String getMobileServiceId() { return mobileServiceId; }

    public void setMobileServiceId(String mobileServiceId) { this.mobileServiceId = mobileServiceId; }



    @Override
    public String toString() {
        return "CbsRequestData{" +
                "msisdn='" + msisdn + '\'' +
                ", amount='" + amount + '\'' +
                ", clientId='" + clientId + '\'' +
                ", service_charge='" + service_charge + '\'' +
                ", serviceId='" + serviceId + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", customer_name='" + customer_name + '\'' +
                ", transtemptableid='" + transtemptableid + '\'' +
                ", account_number='" + account_number + '\'' +
                ", agent_store_code='" + agent_store_code + '\'' +
                ", agent_msisdn='" + agent_msisdn + '\'' +
                ", trnx_charges='" + trnx_charges + '\'' +
                ", customerType='" + customerType + '\'' +
                ", agentPhoneNumber='" + agentPhoneNumber + '\'' +
                ", description='" + description + '\'' +
                ", userId='" + userId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", withdrawal_sms='" + withdrawal_sms + '\'' +
                ", otherAccountNumber='"+otherAccountNumber+'\'' +
                '}';
    }
}
