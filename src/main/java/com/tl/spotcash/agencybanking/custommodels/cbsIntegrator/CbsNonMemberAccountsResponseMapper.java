package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

/**
 *
 * <AUTHOR>
 */
public class CbsNonMemberAccountsResponseMapper {

    NonMemberAccounts AccountDetails;
    String errorString;
    String responseCode;
    String encryptedAccounts;


    public NonMemberAccounts getAccountDetails() {
        return AccountDetails;
    }

    public void setAccountDetails(NonMemberAccounts accountDetails) {
        AccountDetails = accountDetails;
    }

    public String getErrorString() {
        return errorString;
    }

    public void setErrorString(String errorString) {
        this.errorString = errorString;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getEncryptedAccounts() {
        return encryptedAccounts;
    }

    public void setEncryptedAccounts(String encryptedAccounts) {
        this.encryptedAccounts = encryptedAccounts;
    }
}
