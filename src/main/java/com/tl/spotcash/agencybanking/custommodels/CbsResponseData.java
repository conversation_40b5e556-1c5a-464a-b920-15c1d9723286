package com.tl.spotcash.agencybanking.custommodels;

import com.tl.spotcash.agencybanking.pojo.AgentBalanceResponseMessage;
import springfox.documentation.service.ResponseMessage;

/**
 *
 * <AUTHOR>
 */
public class CbsResponseData {

	private String requestId;
	private String phoneNo;
	private String transactionType;
	private String amount;
	private String agentPhoneNo;
	private String accountNumber;
	private String responseMessage;




	private String trnx_charges;
	private String status;
	private String balance;
	private String message;
	private String response;



	private String returnValue;

	public String getRequestId() {
		return requestId;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getPhoneNo() {
		return phoneNo;
	}

	public void setPhoneNo(String phoneNo) {
		this.phoneNo = phoneNo;
	}

	public String getTransactionType() {
		return transactionType;
	}
	public String getReturnValue() {
		return returnValue;
	}

	public void setReturnValue(String returnValue) {
		this.returnValue = returnValue;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getAgentPhoneNo() {
		return agentPhoneNo;
	}

	public void setAgentPhoneNo(String agentPhoneNo) {
		this.agentPhoneNo = agentPhoneNo;
	}

	public String getCharges() {
		return trnx_charges;
	}

	public void setCharges(String charges) {
		this.trnx_charges = charges;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getBalance() {
		return balance;
	}

	public void setBalance(String balance) {
		this.balance = balance;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getResponse() {
		return response;
	}

	public void setResponse(String response) {
		this.response = response;
	}

	public String getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}

	@Override
	public String toString() {
		return "CbsResponseData{" + "requestId=" + requestId + ", phoneNo=" + phoneNo + ", transactionType=" + transactionType + ", amount=" + amount + ", agentPhoneNo=" + agentPhoneNo + ", trnx_charges=" + trnx_charges + ", status=" + status + ", balance=" + balance + ", message=" + message + ", response=" + response + ", responseMessage=" + responseMessage + '}';
	}

}
