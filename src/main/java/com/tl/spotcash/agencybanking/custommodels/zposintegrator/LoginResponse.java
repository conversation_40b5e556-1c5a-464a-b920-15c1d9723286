package com.tl.spotcash.agencybanking.custommodels.zposintegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginResponse {
	
	public LoginResponse() {
	}
	@JsonIgnoreProperties("message")
	private String message;
	@JsonIgnoreProperties("`response_code")
	private String responseCode;
	@JsonIgnoreProperties("name")
	private String name;
	@JsonIgnoreProperties("token")
	private String token;
	
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getResponseCode() {
		return responseCode;
	}
	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	
	

}
