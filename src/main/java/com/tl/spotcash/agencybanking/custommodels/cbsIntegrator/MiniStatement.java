package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MiniStatement {

    @JsonProperty("description")
    private String description;
    @JsonProperty("Debit")
    private String debit;
    @JsonProperty("Credit")
    private String credit;
    @JsonProperty("postingDate")
    private String date;
    @JsonProperty("balance")
    private String balance;
    @JsonProperty("documentNo")
    private String documentNo;
    @JsonProperty("TransactionType")
    private String transactionType;

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("Debit")
    public String getDebit() {
        return debit;
    }

    @JsonProperty("Debit")
    public void setDebit(String debit) {
        this.debit = debit;
    }

    @JsonProperty("Credit")
    public String getCredit() {
        return credit;
    }

    @JsonProperty("Credit")
    public void setCredit(String credit) {
        this.credit = credit;
    }

    @JsonProperty("postingDate")
    public String getDate() {
        return date;
    }

    @JsonProperty("postingDate")
    public void setDate(String date) {
        this.date = date;
    }

    @JsonProperty("balance")
    public String getBalance() {
        return balance;
    }

    @JsonProperty("balance")
    public void setBalance(String balance) {
        this.balance = balance;
    }

    @JsonProperty("documentNo")
    public String getDocumentNo() {
        return documentNo;
    }

    @JsonProperty("documentNo")
    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }

    @JsonProperty("TransactionType")
    public String getTransactionType() {
        return transactionType;
    }

    @JsonProperty("TransactionType")
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }
}
