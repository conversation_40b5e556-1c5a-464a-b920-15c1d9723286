package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder({
    "accountNo",
    "accountName",
    "canWithdraw",
    "canDeposit",
    "balance",
    "maxWithdrawable",
    "maxDeposit"
})
public class Account {

    @JsonProperty("accountNo")
    private String accountNo;
    @JsonProperty("accountName")
    private String accountName;
    @JsonProperty("canWithdraw")
    private String canWithdraw;
    @JsonProperty("canDeposit")
    private String canDeposit;
    @JsonProperty("balance")
    private String balance;
    @JsonProperty("maxWithdrawable")
    private String maxWithdrawable;
    @JsonProperty("maxDeposit")
    private String maxDeposit;
    @JsonProperty("status")
    private String status;
    @JsonProperty("canOverdraw")
    private String canOverdraw;
    @JsonProperty("availableOverdraftAmount")
    private String availableOverdraftAmount;
    @JsonProperty("interestRate")
    private String interestRate;

    @JsonProperty("accountNo")
    public String getAccountNo() {
        return accountNo;
    }

    @JsonProperty("accountNo")
    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    @JsonProperty("accountName")
    public String getAccountName() {
        return accountName;
    }

    @JsonProperty("accountName")
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    @JsonProperty("canWithdraw")
    public String getCanWithdraw() {
        return canWithdraw;
    }

    @JsonProperty("canWithdraw")
    public void setCanWithdraw(String canWithdraw) {
        this.canWithdraw = canWithdraw;
    }

    @JsonProperty("canDeposit")
    public String getCanDeposit() {
        return canDeposit;
    }

    @JsonProperty("canDeposit")
    public void setCanDeposit(String canDeposit) {
        this.canDeposit = canDeposit;
    }

    @JsonProperty("balance")
    public String getBalance() {
        return balance;
    }

    @JsonProperty("balance")
    public void setBalance(String balance) {
        this.balance = balance;
    }

    @JsonProperty("maxWithdrawable")
    public String getMaxWithdrawable() {
        return maxWithdrawable;
    }

    @JsonProperty("maxWithdrawable")
    public void setMaxWithdrawable(String maxWithdrawable) {
        this.maxWithdrawable = maxWithdrawable;
    }

    @JsonProperty("maxDeposit")
    public String getMaxDeposit() {
        return maxDeposit;
    }

    @JsonProperty("maxDeposit")
    public void setMaxDeposit(String maxDeposit) {
        this.maxDeposit = maxDeposit;
    }

    @JsonProperty("status")
    public String getStatus() {
        return status;
    }

    @JsonProperty("status")
    public void setStatus(String status) {
        this.status = status;
    }
    @JsonProperty("canOverdraw")
    public String getCanOverdraw() {
        return canOverdraw;
    }
    @JsonProperty("canOverdraw")
    public void setCanOverdraw(String canOverdraw) {
        this.canOverdraw = canOverdraw;
    }
    @JsonProperty("availableOverdraftAmount")
    public String getAvailableOverdraftAmount() {
        return availableOverdraftAmount;
    }
    @JsonProperty("availableOverdraftAmount")
    public void setAvailableOverdraftAmount(String availableOverdraftAmount) {
        this.availableOverdraftAmount = availableOverdraftAmount;
    }
    @JsonProperty("interestRate")
    public String getInterestRate() {
        return interestRate;
    }
    @JsonProperty("interestRate")
    public void setInterestRate(String interestRate) {
        this.interestRate = interestRate;
    }
}
