package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "memberDetails"
})
public class MobileMemberDetails {

    @JsonProperty("memberDetails")
    private MobileMember member;

    @JsonProperty("memberDetails")
    public MobileMember getMember() {
        return member;
    }

    @JsonProperty("memberDetails")
    public void setMember(MobileMember member) {
        this.member = member;
    }

}
