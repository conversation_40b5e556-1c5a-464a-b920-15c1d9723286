package com.tl.spotcash.agencybanking.custommodels;

/**
 *
 * <AUTHOR>
 */
public enum PosCustomResponse {
    INSUFFICIENT_MEMBER_BAL("Member funds insufficient"),
    INACTIVE_MEMBER_ACC("Inactive Member account"),
    SYS_ERROR("System error"),
    AMT_LESS_THAN_MIN("Amount less than minimum allowed"),
    AMT_HIGHER_THAN_MAX("Amount higher than maximum allowed"),
    MAX_DAILY_EXCEEDED("Maximum daily amount exceeded"),
    TRX_EXPIRED("Transaction expired"),
    AMT_WILL_FALL_BELOW_MIN("Account will fall below minimum balance"),
    FAILED_TRANSACTION("Transaction failed to complete");

    private String poscutomresponses;

    private PosCustomResponse(String poscutomresponses) {
        this.poscutomresponses = poscutomresponses;
    }

    public String get_pos_reponse() {
        return poscutomresponses;
    }
}
