package com.tl.spotcash.agencybanking.custommodels;

/**
 *
 * <AUTHOR>
 */

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "cbsUser",
    "cbsPassword"
})
public class SOACbsAuthParams implements Serializable {

    @JsonProperty("cbsUser")
    private String cbsUser;
    @JsonProperty("cbsPassword")
    private String cbsPassword;
    private final static long serialVersionUID = 3986680960726619398L;

    @JsonProperty("cbsUser")
    public String getCbsUser() {
        return cbsUser;
    }

    @JsonProperty("cbsUser")
    public void setCbsUser(String cbsUser) {
        this.cbsUser = cbsUser;
    }

    @JsonProperty("cbsPassword")
    public String getCbsPassword() {
        return cbsPassword;
    }

    @JsonProperty("cbsPassword")
    public void setCbsPassword(String cbasPassword) {
        this.cbsPassword = cbasPassword;
    }
}
