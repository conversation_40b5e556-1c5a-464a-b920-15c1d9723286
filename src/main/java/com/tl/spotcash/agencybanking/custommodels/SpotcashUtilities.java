package com.tl.spotcash.agencybanking.custommodels;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tl.spotcash.agencybanking.adapter.ISO.Iso8583serverinvoker;
import com.tl.spotcash.agencybanking.exceptions.CustomException;
import com.tl.spotcash.agencybanking.xiputils.HttpProcessorRequest;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

/**
 *
 * <AUTHOR>
 */
public class SpotcashUtilities {

    private static final Logger LOGGER = LoggerFactory.getLogger(Iso8583serverinvoker.class);

    /**
     * Return base 64 encoded and decoded string
     *
     * @param s
     * @return
     */
    public String decode(String s) {
        return StringUtils.newStringUtf8(Base64.decodeBase64(s));
    }

    public String encode(String s) {
        return Base64.encodeBase64String(StringUtils.getBytesUtf8(s));
    }

    public String nextTransactionId(String text) {
        // We will not process empty string
        int len = text.length();
        if (len == 0) {
            return text;
        }

        // Determine where does the first alpha-numeric starts.
        boolean alphaNum = false;
        int alphaNumPos = -1;
        for (char c : text.toCharArray()) {
            alphaNumPos++;
            if (Character.isDigit(c) || Character.isLetter(c)) {
                alphaNum = true;
                break;
            }
        }

        // Now we go calculate the next successor char of the given text.
        StringBuilder buf = new StringBuilder(text);
        if (!alphaNum || alphaNumPos == 0 || alphaNumPos == len) {
            // do the entire input text
            next(buf, buf.length() - 1, alphaNum);
        } else {
            // Strip the input text for non alpha numeric prefix. We do not need to process these prefix but to save and
            // re-attach it later after the result.
            String prefix = text.substring(0, alphaNumPos);
            buf = new StringBuilder(text.substring(alphaNumPos));
            next(buf, buf.length() - 1, alphaNum);
            buf.insert(0, prefix);
        }

        // We are done.
        return buf.toString();
    }

    /**
     * Internal method to calculate string successor value on alpha numeric
     * chars only.
     */
    private void next(StringBuilder buf, int pos, boolean alphaNum) {
        // We are asked to carry over next value for the left most char
        if (pos == -1) {
            char c = buf.charAt(0);
            String rep = null;
            if (Character.isDigit(c)) {
                rep = "1";
            } else if (Character.isLowerCase(c)) {
                rep = "a";
            } else if (Character.isUpperCase(c)) {
                rep = "A";
            } else {
                rep = Character.toString((char) (c + 1));
            }
            buf.insert(0, rep);
            return;
        }

        // We are asked to calculate next successor char for index of pos.
        char c = buf.charAt(pos);
        if (Character.isDigit(c)) {
            if (c == '9') {
                buf.replace(pos, pos + 1, "0");
                next(buf, pos - 1, alphaNum);
            } else {
                buf.replace(pos, pos + 1, Character.toString((char) (c + 1)));
            }
        } else if (Character.isLowerCase(c)) {
            if (c == 'z') {
                buf.replace(pos, pos + 1, "a");
                next(buf, pos - 1, alphaNum);
            } else {
                buf.replace(pos, pos + 1, Character.toString((char) (c + 1)));
            }
        } else if (Character.isUpperCase(c)) {
            if (c == 'Z') {
                buf.replace(pos, pos + 1, "A");
                next(buf, pos - 1, alphaNum);
            } else {
                buf.replace(pos, pos + 1, Character.toString((char) (c + 1)));
            }
        } else // If input text has any alpha num at all then we are to calc next these characters only and ignore the
        // we will do this by recursively call into next char in buf.
        {
            if (alphaNum) {
                next(buf, pos - 1, alphaNum);
            } else // However if the entire input text is non alpha numeric, then we will calc successor by simply
            // increment to the next char in range (including non-printable char!)
            {
                if (c == Character.MAX_VALUE) {
                    buf.replace(pos, pos + 1, Character.toString(Character.MIN_VALUE));
                    next(buf, pos - 1, alphaNum);
                } else {
                    buf.replace(pos, pos + 1, Character.toString((char) (c + 1)));
                }
            }
        }
    }

    public String callwebservice(String town) {
        String response = "";
        try {
            URL url = new URL("http://localhost/simulator/index.php");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Accept", "application/json");
            if (conn.getResponseCode() != 200) {
                throw new RuntimeException("Failed : HTTP error code : "
                        + conn.getResponseCode());
            }
            BufferedReader br = new BufferedReader(new InputStreamReader(
                    (conn.getInputStream())));
            String output;

            while ((output = br.readLine()) != null) {
                response = output;
                System.out.println("Output from Server .... \n" + output + "\n");
            }
            conn.disconnect();

        }
        catch (MalformedURLException e) {
        }
        catch (IOException e) {
        }
        return response;
    }

    /*
     * Calls PaymentGateway for (Mpesa,AirtelMoney,Airtime purchase)
     */
    public String callJsonWebservice(String URL, String payload) throws CustomException {
        String line;
        StringBuilder jsonResponse = new StringBuilder();
        try {
            URL url = new URL(URL);
            //escape the double quotes in json string
            //String payload="{\"jsonrpc\":\"2.0\",\"method\":\"changeDetail\",\"params\":[{\"id\":11376}],\"id\":2}";
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            try (OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream(), "UTF-8")) {
                writer.write(payload);
            }
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                while ((line = br.readLine()) != null) {
                    jsonResponse.append(line);

                }
            }
            connection.disconnect();
        }
        catch (Exception e) {
            throw new CustomException(e.getMessage(), e);
            
        }
        return jsonResponse.toString();
    }

    public int randInt(int min, int max) {
        SecureRandom rand = new SecureRandom();
        return rand.nextInt((max - min) + 1) + min;
    }

    public String md5password(String password){// throws CustomException {

        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(password.getBytes());
            byte byteData[] = md.digest();
            //convert the byte to hex format method 1
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < byteData.length; i++) {
                sb.append(Integer.toString((byteData[i] & 0xff) + 0x100, 16).substring(1));
            }
            return sb.toString();
        }
        catch (NoSuchAlgorithmException ex) {
            LOGGER.info("ERROR CREATING HASHED PASSWORD " + ex.toString());
            return null;
//            throw new CustomException("Error occured while hashing password", ex);
        }
    }

    /**
     *
     * @param passedAmount
     * @param returnedBalance
     * @return
     */
    public boolean isAgentBalanceEnough(BigDecimal passedAmount, BigDecimal returnedBalance) {
        boolean isEnough = false;
        if (returnedBalance != null) {
            if (passedAmount.intValue()<returnedBalance.intValue()) {
                isEnough = true;
            }
        } else {
            return isEnough;
        }
        return isEnough;
    }

    /**
     *
     * @param request
     * @return
     * @throws IOException
     */
    public HttpProcessorRequest returnMappedXipRequest(String request) throws IOException {
        HttpProcessorRequest httpProcessorRequest = null;

        ObjectMapper mapper = new ObjectMapper();
        httpProcessorRequest = mapper.readValue(request, HttpProcessorRequest.class);
        LOGGER.info("GETTING MAPPED DATA "+httpProcessorRequest);
        return httpProcessorRequest;
    }
}
