package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tl.spotcash.agencybanking.custommodels.event.RedeemItem;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCustomerRewardsCbsResponse {

    @JsonProperty("responseMessage")
    private String responseMessage;

    @JsonProperty("responseCode")
    private String responseCode;

    @JsonProperty("errorMessage")
    private String errorMessage;

    @JsonProperty("failedItems")
    List<RedeemItem> failedItems;

}
