package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "transactionID",
        "postingDate",
        "postingTime",
        "customerNo",
        "customerName",
        "customerPhoneNo",
        "description",
        "amount"
})
public class NewMembersMpesa {
    @JsonProperty("memberNo")
    private String memberNo;
    @JsonProperty("memberName")
    private String memberName;
    @JsonProperty("agentDevicePhoneNo")
    private String agentDevicePhoneNo;
    @JsonProperty("eventName")
    private String eventName;
    @JsonProperty("eventId")
    private String eventId;
    @JsonProperty("documentNo")
    private String documentNo;
    @JsonProperty("phoneNo")
    private String phoneNo;
    @JsonProperty("amount")
    private String amount;

    @JsonProperty("memberNo")
    public String getMemberNo() {
        return memberNo;
    }
    @JsonProperty("memberNo")
    public void setMemberNo(String memberNo) {
        this.memberNo = memberNo;
    }
    @JsonProperty("memberName")
    public String getMemberName() {
        return memberName;
    }
    @JsonProperty("memberName")
    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }
    @JsonProperty("agentDevicePhoneNo")
    public String getAgentDevicePhoneNo() {
        return agentDevicePhoneNo;
    }
    @JsonProperty("agentDevicePhoneNo")
    public void setAgentDevicePhoneNo(String agentDevicePhoneNo) {
        this.agentDevicePhoneNo = agentDevicePhoneNo;
    }
    @JsonProperty("eventName")
    public String getEventName() {
        return eventName;
    }
    @JsonProperty("eventName")
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }
    @JsonProperty("eventId")
    public String getEventId() {
        return eventId;
    }
    @JsonProperty("eventId")
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    @JsonProperty("documentNo")
    public String getDocumentNo() {
        return documentNo;
    }
    @JsonProperty("documentNo")
    public void setDocumentNo(String documentNo) {
        this.documentNo = documentNo;
    }
    @JsonProperty("phoneNo")
    public String getPhoneNo() {
        return phoneNo;
    }
    @JsonProperty("phoneNo")
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }
    @JsonProperty("amount")
    public String getAmount() {
        return amount;
    }
    @JsonProperty("amount")
    public void setAmount(String amount) {
        this.amount = amount;
    }


}
