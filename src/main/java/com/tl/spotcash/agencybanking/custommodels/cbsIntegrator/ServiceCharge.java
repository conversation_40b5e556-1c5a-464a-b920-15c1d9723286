package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

@Data
@JsonPropertyOrder({ "phone_No" })
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceCharge {
    @JsonProperty("phone_No")
    private String phoneNo;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("transcation_Type")
    private String transcationType;
    @JsonProperty("response_Code")
    private String responseCode;
    @JsonProperty("response_Message")
    private String responseMessage;
    private String errorMessage;
}

