package com.tl.spotcash.agencybanking.custommodels;

//Stores additional information that may not be in the cbs response and request data

public class AdditionalMsgResponse {

    private String miniStatement;
    private String generatedToken;
    private String generatedTokenReceipt;
    private String tokenUnits;
    private String kplcPostPayCustomerMessage;
    private String kplcPostPayReceipt;
    private String clientName;
    private String agentName;
    private String agentPhone;
    private String agentEmail;

    public String getMiniStatement() {
        return miniStatement;
    }

    public void setMiniStatement(String miniStatement) {
        this.miniStatement = miniStatement;
    }

    public String getGeneratedToken() {
        return generatedToken;
    }

    public void setGeneratedToken(String generatedToken) {
        this.generatedToken = generatedToken;
    }

    public String getGeneratedTokenReceipt() {
        return generatedTokenReceipt;
    }

    public void setGeneratedTokenReceipt(String generatedTokenReceipt) {
        this.generatedTokenReceipt = generatedTokenReceipt;
    }

    public String getTokenUnits() {
        return tokenUnits;
    }

    public void setTokenUnits(String tokenUnits) {
        this.tokenUnits = tokenUnits;
    }

    public String getKplcPostPayCustomerMessage() {
        return kplcPostPayCustomerMessage;
    }

    public void setKplcPostPayCustomerMessage(String kplcPostPayCustomerMessage) {
        this.kplcPostPayCustomerMessage = kplcPostPayCustomerMessage;
    }

    public String getKplcPostPayReceipt() {
        return kplcPostPayReceipt;
    }

    public void setKplcPostPayReceipt(String kplcPostPayReceipt) {
        this.kplcPostPayReceipt = kplcPostPayReceipt;
    }
    public String getClientName() {
        return clientName;
    }
    public void setClientName(String clientName) {
        this.clientName = clientName;
    }
    public String getAgentName() {
        return agentName;
    }
    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }
}
