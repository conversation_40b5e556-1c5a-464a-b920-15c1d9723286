package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MiniStatements {
    @JsonProperty("Transactions")
    private List<MiniStatement> miniStatements = null;

    @JsonProperty("Transactions")
    public List<MiniStatement> getMiniStatements() {
        return miniStatements;
    }

    @JsonProperty("Transactions")
    public void setMiniStatements(List<MiniStatement> miniStatements) {
        this.miniStatements = miniStatements;
    }
}

