package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 * @co-author mwend<PERSON><PERSON><PERSON>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "memberName",
    "idNumber"
})
public class MobileMember {

    @JsonProperty("memberName")
    private String name;
    @JsonProperty("idNumber")
    private String nationalId;
    @JsonProperty("MemberImage")
    private String MemberImage;
    @JsonProperty("memberNo")
    private String MemberNo;
    @JsonProperty("mobileNumber")
    private String PhoneNo;

    @JsonProperty("memberName")
    public String getName() {
        return name;
    }

    @JsonProperty("memberName")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("idNumber")
    public String getNationalId() {
        return nationalId;
    }

    @JsonProperty("idNumber")
    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }

    @JsonProperty("MemberImage")
    public String getMemberImage() {
        return MemberImage;
    }

    @JsonProperty("MemberImage")
    public void setMemberImage(String MemberImage) {
        this.MemberImage = MemberImage;
    }

    @JsonProperty("memberNo")
    public String getMemberNo() {
        return MemberNo;
    }

    @JsonProperty("memberNo")
    public void setMemberNo(String MemberNo) {
        this.MemberNo = MemberNo;
    }

    @JsonProperty("mobileNumber")
    public String getPhoneNo() {
        return PhoneNo;
    }

    @JsonProperty("mobileNumber")
    public void setPhoneNo(String PhoneNo) {
        this.PhoneNo = PhoneNo;
    }
}