package com.tl.spotcash.agencybanking.custommodels.agent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "name",
    "phone",
    "float",
    "account_no",
    "account_name",
    "count",
    "date_from",
    "date_to"
})
public class Agent {

    @JsonProperty("name")
    private String name;
    @JsonProperty("phone")
    private String phone;
    @JsonProperty("float")
    private String _float;
    @JsonProperty("account_no")
    private String accountNo;
    @JsonProperty("account_name")
    private String accountName;
    @JsonProperty("count")
    private String count;
    @JsonProperty("date_from")
    private String dateFrom;
    @JsonProperty("date_to")
    private String dateTo;

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }

    @JsonProperty("phone")
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @JsonProperty("float")
    public String getFloat() {
        return _float;
    }

    @JsonProperty("float")
    public void setFloat(String _float) {
        this._float = _float;
    }

    @JsonProperty("account_no")
    public String getAccountNo() {
        return accountNo;
    }

    @JsonProperty("account_no")
    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    @JsonProperty("account_name")
    public String getAccountName() {
        return accountName;
    }

    @JsonProperty("account_name")
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    @JsonProperty("count")
    public String getCount() {
        return count;
    }

    @JsonProperty("count")
    public void setCount(String count) {
        this.count = count;
    }

    @JsonProperty("date_from")
    public String getDateFrom() {
        return dateFrom;
    }

    @JsonProperty("date_from")
    public void setDateFrom(String dateFrom) {
        this.dateFrom = dateFrom;
    }

    @JsonProperty("date_to")
    public String getDateTo() {
        return dateTo;
    }

    @JsonProperty("date_to")
    public void setDateTo(String dateTo) {
        this.dateTo = dateTo;
    }

}
