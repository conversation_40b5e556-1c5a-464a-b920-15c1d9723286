package com.tl.spotcash.agencybanking.custommodels.zposintegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentResponse {
	@JsonProperty("reqRef")
	private String reqRef;
	@JsonProperty("resRef")
	private String resRef;
	@JsonProperty("responseCode")
	private String responseCode;
	@JsonProperty("responseMessage")
	private String responseMessage;
	@JsonProperty("customerName")
	private String customerName;
	@JsonProperty("customerId")
	private String customerId;
	@JsonProperty("transactionAmount")
	private String transactionAmount;
	@JsonProperty("functionCode")
	private String functionCode;
	@JsonProperty("customerNo")
	private String customerNo;
	@JsonProperty("receiptNumber")
	private String receiptNo;
	
	public PaymentResponse() {
	}

	public String getReqRef() {
		return reqRef;
	}

	public void setReqRef(String reqRef) {
		this.reqRef = reqRef;
	}

	public String getResRef() {
		return resRef;
	}

	public void setResRef(String resRef) {
		this.resRef = resRef;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getResponseMessage() {
		return responseMessage;
	}

	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(String transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public String getFunctionCode() {
		return functionCode;
	}

	public void setFunctionCode(String functionCode) {
		this.functionCode = functionCode;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getReceiptNo() {
		return receiptNo;
	}

	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	
	
}
