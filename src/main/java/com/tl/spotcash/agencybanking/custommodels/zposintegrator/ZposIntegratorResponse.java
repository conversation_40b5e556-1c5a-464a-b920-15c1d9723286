package com.tl.spotcash.agencybanking.custommodels.zposintegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ZposIntegratorResponse {
	@JsonProperty("gen_trx_id")
	private String generatedTransactionId;
	@JsonProperty("message")
	private String message;
	@JsonProperty("status")
	private String status;
	@JsonProperty("searchResponse")
	SearchResponse searchResponse;
	@JsonProperty("paymentResponse")
	PaymentResponse paymentResponse;
	
	public ZposIntegratorResponse() {
	}

	public String getGeneratedTransactionId() {
		return generatedTransactionId;
	}

	public void setGeneratedTransactionId(String generatedTransactionId) {
		this.generatedTransactionId = generatedTransactionId;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public SearchResponse getSearchResponse() {
		return searchResponse;
	}

	public void setSearchResponse(SearchResponse searchResponse) {
		this.searchResponse = searchResponse;
	}

	public PaymentResponse getPaymentResponse() {
		return paymentResponse;
	}

	public void setPaymentResponse(PaymentResponse paymentResponse) {
		this.paymentResponse = paymentResponse;
	}
	
	
	

}
