package com.tl.spotcash.agencybanking.custommodels.zposintegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SearchResponse {
	@JsonProperty("reqRef")
	private String reqRef;
	@JsonProperty("resRef")
	private String resRef;
	@JsonProperty("responseCode")
	private String responseCode;
	@JsonProperty("responseMessage")
	private String responseMessage;
	@JsonProperty("customerName")
	private String customerName;
	@JsonProperty("customerId")
	private String customerId;
	@JsonProperty("transactionAmount")
	private String transactionAmount;
	@JsonProperty("receiptNumber")
	private String receiptNumber;
	
	public SearchResponse() {
	}
	
	public String getReqRef() {
		return reqRef;
	}
	public void setReqRef(String reqRef) {
		this.reqRef = reqRef;
	}
	public String getResRef() {
		return resRef;
	}
	public void setResRef(String resRef) {
		this.resRef = resRef;
	}
	public String getResponseCode() {
		return responseCode;
	}
	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}
	public String getResponseMessage() {
		return responseMessage;
	}
	public void setResponseMessage(String responseMessage) {
		this.responseMessage = responseMessage;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public String getTransactionAmount() {
		return transactionAmount;
	}
	public void setTransactionAmount(String transactionAmount) {
		this.transactionAmount = transactionAmount;
	}
	public String getReceiptNumber() {
		return receiptNumber;
	}
	public void setReceiptNumber(String receiptNumber) {
		this.receiptNumber = receiptNumber;
	}
	
	

}
