package com.tl.spotcash.agencybanking.custommodels;

import com.tl.spotcash.agencybanking.entity.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConfigurationResponseMapper {

    private SpAgents agent;
    private SpAgentStores spAgentStores;
    private List<SpAgentAuthentication> agentAuthenticationList;
    private SpClients spClient;
    private SpStoreUsers spAgentStoreUser;
    private String key;

    public SpAgents getAgent() {
        return agent;
    }

    public void setAgent(SpAgents agent) {
        this.agent = agent;
    }

    public SpAgentStores getAgentStores() {
        return spAgentStores;
    }

    public void setAgentStores(SpAgentStores spAgentStores) {
        this.spAgentStores = spAgentStores;
    }

    public List<SpAgentAuthentication> getAuthenticationList() {
        return agentAuthenticationList;
    }

    public void setAuthenticationList(List<SpAgentAuthentication> agentAuthenticationList) {
        this.agentAuthenticationList = agentAuthenticationList;
    }

    public SpClients getClient() {
        return spClient;
    }

    public void setClient(SpClients spClient) {
        this.spClient = spClient;
    }

    public SpStoreUsers getAgentStoreUsers() {
        return spAgentStoreUser;
    }

    public void setAgentStoreUsers(SpStoreUsers spAgentStoreUser) {
        this.spAgentStoreUser = spAgentStoreUser;
    }

    public String getKey() { return key; }

    public void setKey(String key) { this.key = key; }
}
