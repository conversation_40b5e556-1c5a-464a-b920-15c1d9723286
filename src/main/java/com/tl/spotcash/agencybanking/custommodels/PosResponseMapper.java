package com.tl.spotcash.agencybanking.custommodels;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PosResponseMapper {
    Map<String, String> jsonresponseHeader = new HashMap<>();
    Map<String, Object> jsonresponseData = new HashMap<>();
    String responseCode;

    public Map<String, String> getHeader() {
        return jsonresponseHeader;
    }

    public void setHeader(Map<String, String> jsonresponseHeader) {
        this.jsonresponseHeader = jsonresponseHeader;
    }

    public Map<String, Object> getResponseData() {
        return jsonresponseData;
    }

    public void setResponseData(Map<String, Object> jsonresponseData) {
        this.jsonresponseData = jsonresponseData;
    }

    public String getResponseCode(){
        return responseCode;
    }

    public void setResponseCode(String responseCode){
        this.responseCode = responseCode;
    }
}
