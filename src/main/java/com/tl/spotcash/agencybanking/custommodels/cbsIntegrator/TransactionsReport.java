package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "Transactions"
})
public class TransactionsReport {

    @JsonProperty("Transactions")
    private List<Transaction> transactions = null;

    @JsonProperty("Transactions")
    public List<Transaction> getTransactions() {
        return transactions;
    }

    @JsonProperty("Transactions")
    public void setTransactions(List<Transaction> transactions) {
        this.transactions = transactions;
    }
}
