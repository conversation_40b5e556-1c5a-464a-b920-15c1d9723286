package com.tl.spotcash.agencybanking.custommodels;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public enum SpotcashTransactionService {
	CLIENT_DEPOSIT("57"),
	EXTERNAL_DEPOSIT("60"),
	CLIENT_WITHDRAWAL("56"),
	CLIENT_MINISTATEMENT("12"),
	CLIENT_BALANCE("58"),
	AGENT_BALANCE("59"),
	CLIENT_AIRTIME("3"),
	NM_DEPOSIT("63"),
	KPLC_TOKENS("70"),
	KPLC_POSPAID("71"),
	EVENTS("798"),
	MPESA_FLOAT_PURCHASE("821"),
	INTER_ACCOUNT_TRANSFER("822"),
	UNKNOWN("");

	private String spotcashtransactionserviceid;
	private static Map<String, SpotcashTransactionService> map = new HashMap<>();

	SpotcashTransactionService(String serviceid) {
		this.spotcashtransactionserviceid = serviceid;
	}

	public String getServiceId() {
		return spotcashtransactionserviceid;

	}

	static {
		for (SpotcashTransactionService service_id : SpotcashTransactionService.values()) {
			map.put(service_id.spotcashtransactionserviceid, service_id);
		}
	}

	public static SpotcashTransactionService getvalueof(String serviceid) {
		return map.get(serviceid);
	}
}
