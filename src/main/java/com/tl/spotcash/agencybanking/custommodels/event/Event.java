package com.tl.spotcash.agencybanking.custommodels.event;

import lombok.Data;

@Data
public class Event {
    private String  id;
    private String name;
    private String description;
    private String location;
    private String startDate; // Format: month/day/year
    private String endDate; // Format: month/day/year
    private String redeem;
    private String createdBy;
    private String createdDate;
    private String status;
    private String deviceCount;
    private String disbursementMethod;
    private String nonMemberCount;
    private String defaultDisbursementMethod;
    private String defaultAuthenticationMethod;
    private String validateMember;

}
