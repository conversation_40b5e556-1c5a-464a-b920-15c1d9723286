package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "transactionID",
        "postingDate",
        "postingTime",
        "customerNo",
        "customerName",
        "customerPhoneNo",
        "description",
        "amount"
})
public class Transaction {

    @JsonProperty("transactionID")
    private String transactionID;
    @JsonProperty("postingDate")
    private String postingDate;
    @JsonProperty("postingTime")
    private String postingTime;
    @JsonProperty("customerNo")
    private String customerNo;
    @JsonProperty("customerName")
    private String customerName;
    @JsonProperty("customerName")
    private String customerPhoneNo;
    @JsonProperty("customerPhoneNo")
    private String description;
    @JsonProperty("amount")
    private String amount;

    @JsonProperty("transactionID")
    public String getTransactionID() {
        return transactionID;
    }

    @JsonProperty("transactionID")
    public void setTransactionID(String transactionID) {
        this.transactionID = transactionID;
    }

    @JsonProperty("postingDate")
    public String getPostingDate() {
        return postingDate;
    }

    @JsonProperty("postingDate")
    public void setPostingDate(String postingDate) {
        this.postingDate = postingDate;
    }

    @JsonProperty("postingTime")
    public String getPostingTime() {
        return postingTime;
    }

    @JsonProperty("postingTime")
    public void setPostingTime(String postingTime) {
        this.postingTime = postingTime;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("amount")
    public String getAmount() {
        return amount;
    }

    @JsonProperty("amount")
    public void setAmount(String amount) {
        this.amount = amount;
    }

    @JsonProperty("customerNo")
    public String getCustomerNo() {
        return customerNo;
    }

    @JsonProperty("customerNo")
    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    @JsonProperty("customerName")
    public String getCustomerName() {
        return customerName;
    }

    @JsonProperty("customerName")
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @JsonProperty("customerPhoneNo")
    public String getCustomerPhoneNo() {
        return customerPhoneNo;
    }

    @JsonProperty("customerPhoneNo")
    public void setCustomerPhoneNo(String customerPhoneNo) {
        this.customerPhoneNo = customerPhoneNo;
    }
}
