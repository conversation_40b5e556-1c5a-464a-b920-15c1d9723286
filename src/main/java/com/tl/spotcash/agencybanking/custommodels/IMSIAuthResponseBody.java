package com.tl.spotcash.agencybanking.custommodels;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class IMSIAuthResponseBody {
    /**
     *  {
     "access_token": "SVuCuiAT1t8MXIMglfsfVAa1nbiy",
     "expires_in": "3599"
     }
     */
    private String access_token;
    private String expires_in;

    public IMSIAuthResponseBody(String access_token, String expires_in) {
        this.access_token = access_token;
        this.expires_in = expires_in;
    }
}
