package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import lombok.Builder;
import lombok.Data;


public class FetchMemberByMpesaCbsResponse {
     String responseCode;
     String responseMessage;
     NewMembersPayload newMembersPayload;

     public String getResponseCode() {
         return responseCode;
     }
     public void setResponseCode(String responseCode) {
         this.responseCode = responseCode;
     }
     public String getResponseMessage() {
         return responseMessage;
     }
     public void setResponseMessage(String responseMessage) {
         this.responseMessage = responseMessage;
     }
     public NewMembersPayload getNewMembersPayload() {
         return newMembersPayload;
     }
     public void setNewMembersPayload(NewMembersPayload newMembersPayload) {
         this.newMembersPayload = newMembersPayload;
     }

}
