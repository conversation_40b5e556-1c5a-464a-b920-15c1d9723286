package com.tl.spotcash.agencybanking.custommodels;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "firstName",
    "middleName",
    "branchCode",
    "surname",
    "iDNo",
    "pinNo",
    "address",
    "applicationNo",
    "gender",
    "occupation",
    "phoneNo",
    "email",
    "passportPhoto",
    "frontID",
    "backID",
    "signature",
    "cbsResponse",
    "responseCode"
})
public class NewMember {

    @JsonProperty("firstName")
    private String firstName;
    @JsonProperty("middleName")
    private String middleName;
    @JsonProperty("branchCode")
    private String branchCode;
    @JsonProperty("surname")
    private String surname;
    @JsonProperty("iDNo")
    private String iDNo;
    @JsonProperty("pinNo")
    private String pinNo;
    @JsonProperty("address")
    private String address;
    @JsonProperty("applicationNo")
    private String applicationNo;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("occupation")
    private String occupation;
    @JsonProperty("phoneNo")
    private String phoneNo;
    @JsonProperty("email")
    private String email;
    @JsonProperty("passportPhoto")
    private String passportPhoto;
    @JsonProperty("frontID")
    private String frontID;
    @JsonProperty("backID")
    private String backID;
    @JsonProperty("signature")
    private String signature;
    @JsonProperty("cbsResponse")
    private String cbsResponse;
    @JsonProperty("responseCode")
    private String responseCode;

    @JsonProperty("firstName")
    public String getFirstName() {
        return firstName;
    }

    @JsonProperty("firstName")
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    @JsonProperty("middleName")
    public String getMiddleName() {
        return middleName;
    }

    @JsonProperty("middleName")
    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    @JsonProperty("branchCode")
    public String getBranchCode() {
        return branchCode;
    }

    @JsonProperty("branchCode")
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    @JsonProperty("surname")
    public String getSurname() {
        return surname;
    }

    @JsonProperty("surname")
    public void setSurname(String surname) {
        this.surname = surname;
    }

    @JsonProperty("iDNo")
    public String getIDNo() {
        return iDNo;
    }

    @JsonProperty("iDNo")
    public void setIDNo(String iDNo) {
        this.iDNo = iDNo;
    }

    @JsonProperty("pinNo")
    public String getPinNo() {
        return pinNo;
    }

    @JsonProperty("pinNo")
    public void setPinNo(String pinNo) {
        this.pinNo = pinNo;
    }

    @JsonProperty("address")
    public String getAddress() {
        return address;
    }

    @JsonProperty("address")
    public void setAddress(String address) {
        this.address = address;
    }

    @JsonProperty("applicationNo")
    public String getApplicationNo() {
        return applicationNo;
    }

    @JsonProperty("applicationNo")
    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    @JsonProperty("gender")
    public String getGender() {
        return gender;
    }

    @JsonProperty("gender")
    public void setGender(String gender) {
        this.gender = gender;
    }

    @JsonProperty("occupation")
    public String getOccupation() {
        return occupation;
    }

    @JsonProperty("occupation")
    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    @JsonProperty("phoneNo")
    public String getPhoneNo() {
        return phoneNo;
    }

    @JsonProperty("phoneNo")
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("passportPhoto")
    public String getPassportPhoto() {
        return passportPhoto;
    }

    @JsonProperty("passportPhoto")
    public void setPassportPhoto(String passportPhoto) {
        this.passportPhoto = passportPhoto;
    }

    @JsonProperty("frontID")
    public String getFrontID() {
        return frontID;
    }

    @JsonProperty("frontID")
    public void setFrontID(String frontID) {
        this.frontID = frontID;
    }

    @JsonProperty("backID")
    public String getBackID() {
        return backID;
    }

    @JsonProperty("backID")
    public void setBackID(String backID) {
        this.backID = backID;
    }

    @JsonProperty("signature")
    public String getSignature() {
        return signature;
    }

    @JsonProperty("signature")
    public void setSignature(String signature) {
        this.signature = signature;
    }

    @JsonProperty("cbsResponse")
    public String getCbsResponse() {
        return cbsResponse;
    }

    @JsonProperty("cbsResponse")
    public void setCbsResponse(String cbsResponse) {
        this.cbsResponse = cbsResponse;
    }

    @JsonProperty("responseCode")
    public String getResponseCode() {
        return responseCode;
    }

    @JsonProperty("responseCode")
    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

}
