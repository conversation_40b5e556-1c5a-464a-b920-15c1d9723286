package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "fetchMpesaMembers"
})
public class NewMembersPayload {
    @JsonProperty("fetchMpesaMembers")
    private List<NewMembersMpesa> newMembersMpesa = null;

    @JsonProperty("fetchMpesaMembers")
    public List<NewMembersMpesa> getNewMembersMpesa() {
        return newMembersMpesa;
    }

    @JsonProperty("fetchMpesaMembers")
    public void setNewMembersMpesa(List<NewMembersMpesa> newMembersMpesa) {
        this.newMembersMpesa = newMembersMpesa;
    }
}
