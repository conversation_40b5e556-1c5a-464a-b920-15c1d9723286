package com.tl.spotcash.agencybanking.custommodels;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public enum ResponseCodes {
    SUCCESSFULL_TRANSACTION("00"),
    MEMBER_FUNDS_INSUFFICIENT("01"),
    UNSUFFICIENT_MEMBER_BALANCE("Insufficient Account balance"),
    INACTIVE_ACCOUNT("02"),
    DEACTIVATED_ACCOUNT("03"),
    SYSTEM_ERROR("06"),
    AMNT_LESS_THAN_MIN("11"),
    AMNT_MORE_THAN_MAX("12"),
    DAILY_MAX_EXCEEDED("13"),
    <PERSON><PERSON>L<PERSON><PERSON>E_TRANSACTION("14"),
    EXPIRED_TRANSACTION("15"),
    ACCOUNT_WILL_FALL_LOWER("51"),
    BAL_WILL_FALL_LOWER("051"),
    PHONE_NUMBER_NOT_LINKED("10"),
    ACCOUNT_NOT_FOUND("22"),
    AGENT_FUNDS_INSUFFICIENT("05"),
    REQUEST_ERROR("30"),
    MEMBER_EXISTS("07"),
    TRANSACTION_NOT_POSTED("04"),
    FAILED_REVERSIBLE_TRANSACTION("33"),
    INVALID_EMAIL_ADDRESS("57"),
    FAILED_NO_RESPONSE("39"),
    ACCOUNT_NOT_OK("27"),
    UNKNOWN("");

    private String responsecode;
    private static Map<String, ResponseCodes> map = new HashMap<>();

    ResponseCodes(String responsecode) {
        this.responsecode = responsecode;
    }

    public String getResponsecode() {
        return responsecode;
    }

    static {
        for (ResponseCodes respcodes : ResponseCodes.values()) {
            map.put(respcodes.responsecode, respcodes);
        }
    }

    public static ResponseCodes getvalueof(String respcode) {
        return map.get(respcode);
    }
}
