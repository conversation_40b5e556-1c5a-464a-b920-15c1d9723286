package com.tl.spotcash.agencybanking.custommodels.zposintegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ZposRequestData {
	@JsonProperty("account_number")
	private String accountNumber;
	@JsonProperty("amount")
	private String amount;
	@JsonProperty("utility")
	private String utility;
	@JsonProperty("transaction_id")
	private String transactionId;
	@JsonProperty("customer_msisdn")
	private String customerMSISDN;
	
	public ZposRequestData() {
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public String getUtility() {
		return utility;
	}

	public void setUtility(String utility) {
		this.utility = utility;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getCustomerMSISDN() {
		return customerMSISDN;
	}

	public void setCustomerMSISDN(String customerMSISDN) {
		this.customerMSISDN = customerMSISDN;
	}
	
	
}
