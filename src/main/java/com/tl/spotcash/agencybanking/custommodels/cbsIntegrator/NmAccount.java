package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "Code",
        "Name",
        "PaymentReceipt",
})
public class NmAccount {
    @JsonProperty("Code")
    private String Code;
    @JsonProperty("Name")
    private String Name;
    @JsonProperty("PaymentReceipt")
    private String PaymentReceipt;
    @JsonProperty("Code")
    public String getCode(){return Code;}

    @JsonProperty("Code")
    public void setCode(String Code) {this.Code = Code; }

    @JsonProperty("Name")
    public String getName() {return Name;}

    @JsonProperty("Name")
    public void setName(String Name) {this.Name = Name; }

    @JsonProperty("PaymentReceipt")
    public String getPaymentReceipt() {
        return PaymentReceipt;
    }

    @JsonProperty("PaymentReceipt")
    public void setPaymentReceipt(String PaymentReceipt) {
        this.PaymentReceipt = PaymentReceipt;
    }
}


