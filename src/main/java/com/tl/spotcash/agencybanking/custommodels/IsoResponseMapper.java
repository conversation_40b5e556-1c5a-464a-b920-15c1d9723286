package com.tl.spotcash.agencybanking.custommodels;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class IsoResponseMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(IsoResponseMapper.class);

    private String mti;

    private String msisdn;

    private String processing_code;

    private String amount;

    private String charges;

    private String date;

    private String trace;
    private String dt_short;
    private String smp;

    private String othercharges;

    private String institution_id;

    private String transaction_id;

    private String auth_code;

    private String response_code;

    private String names;

    private String product;

    private String msg;

    private String currency_code;

    private String bal;

    private String client_name;

    private String dob;

    private String mpesa;

    private String customer_account;

    private String credit_account;

    private String desc;

    private String iso_response_code;

    private String response_field;

    private String id_no;

    private String reg_date;

    private String msisdn_mnum;

    private String isomessage;

    private String originalTrxId;

    private String transTempTableId;

    public String getTransTempTableId() {
        return transTempTableId;
    }

    public void setTransTempTableId(String transTempTableId) {
        this.transTempTableId = transTempTableId;
    }

    public String getOriginalTrxId() {
        return originalTrxId;
    }

    public void setOriginalTrxId(String originalTrxId) {
        this.originalTrxId = originalTrxId;
    }

    public void setClient_name(String client_name) {
        this.client_name = client_name;
    }

    public String getClient_name() {
        return client_name;
    }

    public String getDt_short() {
        return dt_short;
    }

    public void setDt_short(String dt_short) {
        this.dt_short = dt_short;
    }

    public void setIsomessage(String isomessage) {
        this.isomessage = isomessage;
    }

    public String getIsomessage() {
        return isomessage;
    }

    public void setMti(String mti) {
        this.mti = mti;
        LOGGER.info("MTI " + mti);

    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
        LOGGER.info("2.MSISDN " + msisdn);
    }

    public void setProcessing_code(String processing_code) {
        this.processing_code = processing_code;
        LOGGER.info("3.PROCESS CODE " + processing_code);
    }

    public void setAmount(String amount) {
        this.amount = amount;
        LOGGER.info("4.AMOUNT " + amount);
    }

    public void setCharges(String charges) {
        this.charges = charges;
        LOGGER.info("6.CHARGES " + charges);
    }

    public void setDate(String date) {
        this.date = date;
        LOGGER.info("7.DATE " + date);

    }

    public void setTrace(String trace) {
        this.trace = trace;
        LOGGER.info("11.TRACE " + trace);
    }

    public void setSmp(String smp) {
        this.smp = smp;
        LOGGER.info("24.TRACE " + trace);
    }

    public void setOthercharges(String othercharges) {
        this.othercharges = othercharges;
        LOGGER.info("28.OTHER CHARGES " + othercharges);
    }

    public void setInstitution_id(String institution_id) {
        this.institution_id = institution_id;
        LOGGER.info("32.INSTITUTION " + institution_id);
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
        LOGGER.info("37.TRANSACTION ID " + transaction_id);
    }

    public void setAuth_code(String auth_code) {
        this.auth_code = auth_code;
        LOGGER.info("38.AUTH CODE " + auth_code);
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
        LOGGER.info("39.RESP CODE " + response_code);

    }

    public void setNames(String names) {
        this.names = names;
        LOGGER.info("44.NAME " + names);

    }

    public void setProduct(String product) {
        this.product = product;
        LOGGER.info("46.PRODUCT " + product);

    }

    public void setMsg(String msg) {
        this.msg = msg;
        LOGGER.info("48.MSG " + msg);

    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
        LOGGER.info("49.CURRENCY_CODE " + currency_code);
    }

    public void setBal(String bal) {
        this.bal = bal;
        LOGGER.info("54.AVAIL BAL " + bal);
    }

    public void setDob(String dob) {
        this.dob = dob;
        LOGGER.info("66.DOB " + dob);
    }

    public void setMpesa(String mpesa) {
        this.mpesa = mpesa;
        LOGGER.info("98.MPESA " + mpesa);
    }

    public void setCustomer_account(String customer_account) {
        this.customer_account = customer_account;
        LOGGER.info("102.ACC NO " + customer_account);
    }

    public void setCredit_account(String credit_account) {
        this.credit_account = credit_account;
        LOGGER.info("103.ACC NO " + credit_account);
    }

    public void setDesc(String desc) {
        this.desc = desc;
        LOGGER.info("112.DESC " + desc);
    }

    public void setIso_response_code(String iso_response_code) {
        this.iso_response_code = iso_response_code;
        LOGGER.info("121.RESPONSE CODE 121 " + iso_response_code);

    }

    public void setResponse_field(String response_field) {
        this.response_field = response_field;
        LOGGER.info("122.RESPONSE FIELD " + response_field);
    }

    public void setId_no(String id_no) {
        this.id_no = id_no;
        LOGGER.info("124.ID NO " + id_no);
    }

    public void setReg_date(String reg_date) {
        this.reg_date = reg_date;
        LOGGER.info("125.REG DATE " + reg_date);
    }

    public void setMsisdn_mnum(String msisdn_mnum) {
        this.msisdn_mnum = msisdn_mnum;
        LOGGER.info("126.MOBILE NO " + msisdn_mnum);
    }

    public String getMti() {
        return mti;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public String getProcessing_code() {
        return processing_code;
    }

    public String getAmount() {
        return amount;
    }

    public String getCharges() {
        return charges;
    }

    public String getDate() {
        return date;
    }

    public String getTrace() {
        return trace;
    }

    public String getSmp() {
        return smp;
    }

    public String getOthercharges() {
        return othercharges;
    }

    public String getInstitution_id() {
        return institution_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public String getAuth_code() {
        return auth_code;
    }

    public String getResponse_code() {
        return response_code;
    }

    public String getNames() {
        return names;
    }

    public String getProduct() {
        return product;
    }

    public String getMsg() {
        return msg;
    }

    public String getCurrency_code() {
        return currency_code;
    }

    public String getBal() {
        return bal;
    }

    public String getDob() {
        return dob;
    }

    public String getMpesa() {
        return mpesa;
    }

    public String getCustomer_account() {
        return customer_account;
    }

    public String getCredit_account() {
        return credit_account;
    }

    public String getDesc() {
        return desc;
    }

    public String getIso_response_code() {
        return iso_response_code;
    }

    public String getResponse_field() {
        return response_field;
    }

    public String getId_no() {
        return id_no;
    }

    public String getReg_date() {
        return reg_date;
    }

    public String getMsisdn_mnum() {
        return msisdn_mnum;
    }

}
