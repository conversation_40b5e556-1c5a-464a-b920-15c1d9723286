package com.tl.spotcash.agencybanking.custommodels;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class IMSIResponseBody {
    /**
     *  {
     "requestRefID": "18479-2178936-2",
     "responseCode": "200",
     "responseDesc": "Success",
     "imsi": "****************",
     "lastSwapDate": "01-01-1900",
     "msisdnRegistrationDate": "01-01-1900",
     "customerNumber": "************"
     }
     *
     *
     {
     "requestRefID": "18479-2178936-5",
     "responseCode": "404",
     "responseDesc": "Not found"
     }
     *
     *
     {
     "responseId": "18479-2178936-6",
     "desc": "Bad Request - Invalid CustomerNumber, the value is required and must match a Kenyan phone number i.e 2547xx",
     "status": "Active",
     "version": "1",
     "lastModified": "2021-10-27T05:58:05Z"
     }
     */
    private String requestRefID;
    private String responseCode;
    private String responseDesc;
    private String imsi;
    private String lastSwapDate;
    private String msisdnRegistrationDate;
    private String customerNumber;

    private String responseId;
    private String desc;
    private String status;
    private String version;
    private String lastModified;

    public IMSIResponseBody(String requestRefID, String responseCode, String responseDesc, String imsi, String lastSwapDate, String msisdnRegistrationDate, String customerNumber) {
        this.requestRefID = requestRefID;
        this.responseCode = responseCode;
        this.responseDesc = responseDesc;
        this.imsi = imsi;
        this.lastSwapDate = lastSwapDate;
        this.msisdnRegistrationDate = msisdnRegistrationDate;
        this.customerNumber = customerNumber;
    }

    public IMSIResponseBody(String responseId, String desc, String status, String version, String lastModified) {
        this.responseId = responseId;
        this.desc = desc;
        this.status = status;
        this.version = version;
        this.lastModified = lastModified;
    }
}
