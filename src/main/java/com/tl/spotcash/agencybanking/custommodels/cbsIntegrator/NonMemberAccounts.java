package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;
/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "AccountDetails"
})
public class NonMemberAccounts {

    @JsonProperty("AccountDetails")
    private List <NmAccount> AccountDetails =null;
    @JsonProperty("AccountDetails")
    public List<NmAccount> getAccountDetails() {
        return AccountDetails;
    }
    @JsonProperty("AccountDetails")
    public void setAccountDetails(List<NmAccount> AccountDetails) {
        this.AccountDetails = AccountDetails;
    }

}

