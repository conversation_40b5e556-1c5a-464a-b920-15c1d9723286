package com.tl.spotcash.agencybanking.custommodels.cbsIntegrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "OwnerName",
        "AccountName",
        "MemberImage",
        "MobileNo",
        "IDNo"
})

public class AccountDetails {

    @JsonProperty("OwnerName")
    private String OwnerName;
    @JsonProperty("AccountName")
    private String AccountName;
    @JsonProperty("MemberImage")
    private String MemberImage;
    @JsonProperty("MobileNo")
    private String MobileNo;
    @JsonProperty("IDNo")
    private String IDNo;
    @JsonProperty("CustomerId")
    private String CustomerId;

    @JsonProperty("OwnerName")
    public String getOwnerName() {
        return OwnerName;
    }

    @JsonProperty("OwnerName")
    public void setOwnerName(String OwnerName) {
        this.OwnerName = OwnerName;
    }

    @JsonProperty("AccountName")
    public String getAccountName() {
        return AccountName;
    }

    @JsonProperty("AccountName")
    public void setAccountName(String AccountName) {
        this.AccountName = AccountName;
    }

    @JsonProperty("MemberImage")
    public String getMemberImage() {
        return MemberImage;
    }

    @JsonProperty("MemberImage")
    public void setMemberImage(String MemberImage) {
        this.MemberImage = MemberImage;
    }

    @JsonProperty("MobileNo")
    public String getMobileNo() {
        return MobileNo;
    }

    @JsonProperty("MobileNo")
    public void setMobileNo(String MobileNo) {
        this.MobileNo = MobileNo;
    }

    @JsonProperty("IDNo")
    public String getIDNo() {
        return IDNo;
    }

    @JsonProperty("IDNo")
    public void setIDNo(String IDNo) {
        this.IDNo = IDNo;
    }

    @JsonProperty("CustomerId")
    public String getCustomerId() { return CustomerId; }

    @JsonProperty("CustomerId")
    public void setCustomerId(String customerId) { CustomerId = customerId; }


}