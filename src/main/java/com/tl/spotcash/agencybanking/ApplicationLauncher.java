package com.tl.spotcash.agencybanking;

import com.tl.spotcash.agencybanking.configuration.Jpaconfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

//@EnableAsync
@EnableScheduling
@Import(Jpaconfiguration.class)
@SpringBootApplication(scanBasePackages = {"com.tl.spotcash.agencybanking"})
public class ApplicationLauncher  {

	public static void main(String[] args) {
		SpringApplication.run(ApplicationLauncher.class, args);
	}
}
