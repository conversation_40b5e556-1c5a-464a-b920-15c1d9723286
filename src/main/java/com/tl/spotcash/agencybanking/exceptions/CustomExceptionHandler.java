package com.tl.spotcash.agencybanking.exceptions;

import com.tl.spotcash.agencybanking.enums.ResponseCodes;
import com.tl.spotcash.agencybanking.pojo.AuthResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

//@ControllerAdvice
public class CustomExceptionHandler {

    //@ExceptionHandler(value = UsernameNotFoundException.class)
    public ResponseEntity<?> handle(final UsernameNotFoundException exception) {
        AuthResponse response = new AuthResponse();

        //set headers, response attributes and response body
        response.setSuccessStatus(ResponseCodes.FAILURE.getCode());
        response.setErrorNarration(exception.getMessage());

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
    }
}
