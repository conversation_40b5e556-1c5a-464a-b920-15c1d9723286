package com.tl.spotcash.agencybanking.xiputils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestHeader {

    @JsonProperty("uxid")
    private String userXid;

    @JsonProperty("umsisdn")
    private String umsisdn;

    @JsonProperty("msisdn")
    private String msisdn;

    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("pms")
    private String parentMsisdn;

    @JsonProperty("ams")
    private String altMsisdn;
    @JsonProperty
    private String accountNumber;

    @JsonProperty("authcode")
    private String authCode;

    @JsonProperty("ln")
    private String loginName;

    @JsonProperty("ps")
    private String password;

    @JsonProperty("dt")
    private String date;
    
    @JsonProperty("categoryName")
    private List<String> categoryName;

    @JsonProperty("userId")
    private String userId;

    public String getUserXid() {
        return userXid;
    }

    public void setUserXid(String userXid) {
        this.userXid = userXid;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getParentMsisdn() {
        return parentMsisdn;
    }

    public void setParentMsisdn(String parentMsisdn) {
        this.parentMsisdn = parentMsisdn;
    }

    public String getAltMsisdn() {
        return altMsisdn;
    }

    public void setAltMsisdn(String altMsisdn) {
        this.altMsisdn = altMsisdn;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public List<String> getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(List<String> categoryName) {
        this.categoryName = categoryName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getCustomerMsisdn() {
        return umsisdn;
    }

    public void setCustomerMsisdn(String umsisdn) {
        this.umsisdn = umsisdn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
