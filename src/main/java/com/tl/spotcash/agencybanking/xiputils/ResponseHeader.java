package com.tl.spotcash.agencybanking.xiputils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseHeader {

    @JsonProperty("txnId")
    private String processTxnId;

    @JsonProperty("sc")
    private String txnCode;

    @JsonProperty("sd")
    private String txnDescription;

    @JsonProperty("st")
    private Integer txnStatus;

    @JsonProperty("responseTime")
    private String txnResponseTime;

    @JsonProperty("ac")
    private String authCode;

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getProcessTxnId() {
        return processTxnId;
    }

    public void setProcessTxnId(String processTxnId) {
        this.processTxnId = processTxnId;
    }

    public String getTxnCode() {
        return txnCode;
    }

    public void setTxnCode(String txnCode) {
        this.txnCode = txnCode;
    }

    public String getTxnDescription() {
        return txnDescription;
    }

    public void setTxnDescription(String txnDescription) {
        this.txnDescription = txnDescription;
    }

    public Integer getTxnStatus() {
        return txnStatus;
    }

    public void setTxnStatus(Integer txnStatus) {
        this.txnStatus = txnStatus;
    }

    public String getTxnResponseTime() {
        return txnResponseTime;
    }

    public void setTxnResponseTime(String txnResponseTime) {
        this.txnResponseTime = txnResponseTime;
    }

    @Override
    public String toString() {
        return "ResponseHeader [processTxnId=" + processTxnId + ", txnCode=" + txnCode + ", txnDescription="
                + txnDescription + ", txnStatus=" + txnStatus + ", txnResponseTime=" + txnResponseTime + ", authCode="
                + authCode + "]";
    }

}
