package com.tl.spotcash.agencybanking.xiputils;

import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public class SpotcashTrxDetails {

    private String amount;
    private String customer_msisdn;
    private String spotcash_service_id;
    private String customer_name;
    private BigInteger spotcash_agent_id;
    private BigInteger spotcash_agent_store_id;
    private String accountNumber;
    private String accountName;
    private String trnx_charges;
    private String client_id;
    private String description;
    private String utilityKeyword;
    private String transactionId;
    private String utilityAccountNumber;
    private String customerType;
    private String agentPhoneNumber;
    @Getter @Setter private String agentCommission;

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setCustomer_msisdn(String customer_msisdn) {
        this.customer_msisdn = customer_msisdn;
    }

    public void setSpotcash_service_id(String spotcash_service_id) {
        this.spotcash_service_id = spotcash_service_id;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAmount() {
        return amount;
    }

    public String getCustomer_msisdn() {
        return customer_msisdn;
    }

    public String getSpotcash_service_id() {
        return spotcash_service_id;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public BigInteger getSpotcash_agent_id() {
        return spotcash_agent_id;
    }

    public void setSpotcash_agent_id(BigInteger spotcash_agent_id) {
        this.spotcash_agent_id = spotcash_agent_id;
    }

    public BigInteger getSpotcash_agent_store_id() {
        return spotcash_agent_store_id;
    }

    public void setSpotcash_agent_store_id(BigInteger spotcash_agent_store_id) {
        this.spotcash_agent_store_id = spotcash_agent_store_id;
    }

    public String getTrnx_charges() {
        return trnx_charges;
    }

    public void setTrnx_charges(String trnx_charges) {
        this.trnx_charges = trnx_charges;
    }

    public String getUtilityKeyword() {
        return utilityKeyword;
    }

    public void setUtilityKeyword(String utilityKeyword) {
        this.utilityKeyword = utilityKeyword;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getUtilityAccountNumber() {
        return utilityAccountNumber;
    }

    public void setUtilityAccountNumber(String utilityAccountNumber) {
        this.utilityAccountNumber = utilityAccountNumber;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getAgentPhoneNumber() {
        return agentPhoneNumber;
    }

    public void setAgentPhoneNumber(String agentPhoneNumber) {
        this.agentPhoneNumber = agentPhoneNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
}
