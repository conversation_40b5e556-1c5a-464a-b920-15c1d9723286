package com.tl.spotcash.agencybanking.xiputils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RequestData {

    @JsonProperty("txt")
    private String txnType;

    @JsonProperty("name")
    private String name;

    @JsonProperty("ams")
    private String agentMsisdn;

    @JsonProperty("xid")
    private String xid;

    @JsonProperty("mcc")
    private String mcc;

    @JsonProperty("mnc")
    private String mnc;

    @JsonProperty("cmp")
    private String custPin;

    @JsonProperty("amp")
    private String agentPin;

    @JsonProperty("custid")
    private String custid;

    @JsonProperty("cnm")
    private String custName;

    @JsonProperty("onm")
    private String orgName;

    @JsonProperty("amt")
    private String amount;

    @JsonProperty("pw")
    private String password;

    @JsonProperty("type")
    private String type;

    @JsonProperty("asid")
    private String agentStoreId;

    @JsonProperty("agentStoreId")
    private String agentSid;

    @JsonProperty("pk")
    private String passkey;

    @JsonProperty("trxId")
    private String trxId;
    
     @JsonProperty("accNo")
    private String accNo;

    @JsonProperty("utilityAccNumber")
    private String utilityAccNumber;

    @JsonProperty("utilityKeyword")
    private String utilityKeyword;

    @JsonProperty("customerType")
    private String customerType;
    @JsonProperty("agentPhoneNumber")
    private String agentPhoneNumber;

    @JsonProperty("description")
    private String description;

    @JsonProperty("accName")
    private String accName;

    @JsonProperty("latitude")
    private String latitude;

    @JsonProperty("longitude")
    private String longitude;

    @JsonProperty("locality")
    private String locality;

    @JsonProperty("depositorName")
    private String depositorName;

    @JsonProperty("depositorIdNumber")
    private String depositorIdNumber;
    @JsonProperty("agentNo")
    private String agentNo;
    @JsonProperty("storeNo")
    private String storeNo;
    @JsonProperty("destinationAccount")
    private String destinationAccount;
    @JsonProperty("destinationAccountName")
    private String destinationAccountName;


    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAgentMsisdn() {
        return agentMsisdn;
    }

    public void setAgentMsisdn(String agentMsisdn) {
        this.agentMsisdn = agentMsisdn;
    }

    public String getXid() {
        return xid;
    }

    public void setXid(String xid) {
        this.xid = xid;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getCustPin() {
        return custPin;
    }

    public void setCustPin(String custPin) {
        this.custPin = custPin;
    }

    public String getAgentPin() {
        return agentPin;
    }

    public void setAgentPin(String agentPin) {
        this.agentPin = agentPin;
    }

    public String getCustid() {
        return custid;
    }

    public void setCustid(String custid) {
        this.custid = custid;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(String agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public String getPasskey() {
        return passkey;
    }

    public void setPasskey(String passkey) {
        this.passkey = passkey;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getAccNo() {
        return accNo;
    }

    public void setAccNo(String accNo) {
        this.accNo = accNo;
    }  

    public String getUtilityAccNumber() {
        return utilityAccNumber;
    }

    public void setUtilityAccNumber(String utilityAccNumber) {
        this.utilityAccNumber = utilityAccNumber;
    }

    public String getUtilityKeyword() {
        return utilityKeyword;
    }

    public void setUtilityKeyword(String utilityKeyword) {
        this.utilityKeyword = utilityKeyword;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getAgentPhoneNumber() {
        return agentPhoneNumber;
    }

    public void setAgentPhoneNumber(String agentPhoneNumber) {
        this.agentPhoneNumber = agentPhoneNumber;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAccName() {
        return accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    public String getAgentSid() {return agentSid;}

    public void setAgentSid(String agentSid) {this.agentSid = agentSid;}

    public String getDepositorName() { return depositorName; }

    public void setDepositorName(String depositorName) { this.depositorName = depositorName; }

    public String getDepositorIdNumber() { return depositorIdNumber; }

    public void setDepositorIdNumber(String depositorIdNumber) { this.depositorIdNumber = depositorIdNumber; }
    public String getAgentNo(){
        return agentNo;
    }
    public void setAgentNo(String agentNo){
        this.agentNo = agentNo;

    }
    public String getStoreNo(){
        return storeNo;
    }
    public void setStoreNo(String storeNo){
        this.storeNo = storeNo;
    }
    public String getDestinationAccount() {
        return destinationAccount;
    }
    public void setDestinationAccount(String destinationAccount) {
        this.destinationAccount = destinationAccount;
    }
    public String getDestinationAccountName() {
        return destinationAccountName;
    }
    public void setDestinationAccountName(){
        this.destinationAccountName = destinationAccountName;
    }


}
