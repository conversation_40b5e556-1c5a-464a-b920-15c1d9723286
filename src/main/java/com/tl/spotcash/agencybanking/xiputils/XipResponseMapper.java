package com.tl.spotcash.agencybanking.xiputils;

/**
 *
 * <AUTHOR>
 */
public enum XipResponseMapper {
    INSUFFICIENT_MERCHANT("Merchant funds insufficient"),
    INSUFFICIENT_MEMBER("Member funds insufficient"),
    AUTH_FAILED_MEMBER("Member password is wrong"),
    AUTH_FAILED_MERCHANT("Merchant password is wrong");

    private String xip_message;

    XipResponseMapper(String xip_message) {
        this.xip_message = xip_message;
    }

    public String xip_message() {
        return xip_message;
    }
}
