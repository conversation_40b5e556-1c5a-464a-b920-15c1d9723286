package com.tl.spotcash.agencybanking.xiputils;

public class TxnDetail {

    private Integer txnId;
    private String txnType;
    private String requestNo;
    private String txnAmount;
    private String userXid;
    private String xid;
    private String txnDate;
    private String userMsisdn; // agent-msisdn
    private String msisdn; // client-msisdn
    private String txnExecutionTime;
    private String agentName; // 15/12/2014
    private String agentMsisdn; // 15/12/2014
    // Newly added props
    private String trxId;
    private String txnCode;
    private String txnDescription;
    private String clientName;
    private String queryTimeComplete;
    private String queryCode;
    private String queryDescription;
    private String custId;
    private String orgName;
    private String agentStoreId;
    private String passKey;
    private String queryTrxStatus;

    //for safaricomVoucher
    private String serialNumber;
    private String voucherPin;
    private String amount;
    private String expiryDate;

    private String portrait;

    private String authCode;

    public String getTxnExecutionTime() {
        return txnExecutionTime;
    }

    public void setTxnExecutionTime(String txnExecutionTime) {
        this.txnExecutionTime = txnExecutionTime;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getPortrait() {
        return portrait;
    }

    public void setPortrait(String portrait) {
        this.portrait = portrait;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getVoucherPin() {
        return voucherPin;
    }

    public void setVoucherPin(String voucherPin) {
        this.voucherPin = voucherPin;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getQueryTrxStatus() {
        return queryTrxStatus;
    }

    public void setQueryTrxStatus(String queryTrxStatus) {
        this.queryTrxStatus = queryTrxStatus;
    }

    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    public String getTxnType() {
        return txnType;
    }

    public void setTxnType(String txnType) {
        this.txnType = txnType;
    }

    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    public String getTxnAmount() {
        return txnAmount;
    }

    public void setTxnAmount(String txnAmount) {
        this.txnAmount = txnAmount;
    }

    public String getUserXid() {
        return userXid;
    }

    public void setUserXid(String userXid) {
        this.userXid = userXid;
    }

    public String getXid() {
        return xid;
    }

    public void setXid(String xid) {
        this.xid = xid;
    }

    public String getTxnDate() {
        return txnDate;
    }

    public void setTxnDate(String txnDate) {
        this.txnDate = txnDate;
    }

    public String getUserMsisdn() {
        return userMsisdn;
    }

    public void setUserMsisdn(String userMsisdn) {
        this.userMsisdn = userMsisdn;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentMsisdn() {
        return agentMsisdn;
    }

    public void setAgentMsisdn(String agentMsisdn) {
        this.agentMsisdn = agentMsisdn;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public String getTxnCode() {
        return txnCode;
    }

    public void setTxnCode(String txnCode) {
        this.txnCode = txnCode;
    }

    public String getTxnDescription() {
        return txnDescription;
    }

    public void setTxnDescription(String txnDescription) {
        this.txnDescription = txnDescription;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getQueryTimeComplete() {
        return queryTimeComplete;
    }

    public void setQueryTimeComplete(String queryTimeComplete) {
        this.queryTimeComplete = queryTimeComplete;
    }

    public String getQueryCode() {
        return queryCode;
    }

    public void setQueryCode(String queryCode) {
        this.queryCode = queryCode;
    }

    public String getQueryDescription() {
        return queryDescription;
    }

    public void setQueryDescription(String queryDescription) {
        this.queryDescription = queryDescription;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getAgentStoreId() {
        return agentStoreId;
    }

    public void setAgentStoreId(String agentStoreId) {
        this.agentStoreId = agentStoreId;
    }

    public String getPassKey() {
        return passKey;
    }

    public void setPassKey(String passKey) {
        this.passKey = passKey;
    }

}
