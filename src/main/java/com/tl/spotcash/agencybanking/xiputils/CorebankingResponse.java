package com.tl.spotcash.agencybanking.xiputils;

/**
 *
 * <AUTHOR>
 */
public class CorebankingResponse {

	private String CorebankingResponseCode;
	private String CorebankingResponseMessage;
	private String CorebankingResponseDescription;
	private String spotcash_transaction_id;
	private String spotcash_agent_id;
	private String spotcash_transaction_status;
	private String POSresponsecode;
	private boolean isPrintable;
	private String CorebankingBalance;

	public String getCorebankingResponseCode() {
		return CorebankingResponseCode;
	}

	public void setCorebankingResponseCode(String CorebankingResponseCode) {
		this.CorebankingResponseCode = CorebankingResponseCode;
	}

	public String getCorebankingResponseMessage() {
		return CorebankingResponseMessage;
	}

	public void setCorebankingResponseMessage(String CorebankingResponseMessage) {
		this.CorebankingResponseMessage = CorebankingResponseMessage;
	}

	public String getCorebankingResponseDescription() {
		return CorebankingResponseDescription;
	}

	public void setCorebankingResponseDescription(String CorebankingResponseDescription) {
		this.CorebankingResponseDescription = CorebankingResponseDescription;
	}

	public String getSpotcash_transaction_id() {
		return spotcash_transaction_id;
	}

	public void setSpotcash_transaction_id(String spotcash_transaction_id) {
		this.spotcash_transaction_id = spotcash_transaction_id;
	}

	public String getSpotcash_agent_id() {
		return spotcash_agent_id;
	}

	public void setSpotcash_agent_id(String spotcash_agent_id) {
		this.spotcash_agent_id = spotcash_agent_id;
	}

	public String getSpotcash_transaction_status() {
		return spotcash_transaction_status;
	}

	public void setSpotcash_transaction_status(String spotcash_transaction_status) {
		this.spotcash_transaction_status = spotcash_transaction_status;
	}

	public String getPOSresponsecode() {
		return POSresponsecode;
	}

	public void setPOSresponsecode(String POSresponsecode) {
		this.POSresponsecode = POSresponsecode;
	}

	public boolean isIsPrintable() {
		return isPrintable;
	}

	public void setIsPrintable(boolean isPrintable) {
		this.isPrintable = isPrintable;
	}

	public String getCorebankingBalance() {
		return CorebankingBalance;
	}

	public void setCorebankingBalance(String CorebankingBalance) {
		this.CorebankingBalance = CorebankingBalance;
	}

}
