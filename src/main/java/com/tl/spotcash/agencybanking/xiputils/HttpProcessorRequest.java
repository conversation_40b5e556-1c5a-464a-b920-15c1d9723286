package com.tl.spotcash.agencybanking.xiputils;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpProcessorRequest {

    @JsonProperty
    private RequestHeader header;

    @JsonProperty
    private RequestData requestData;

    public RequestHeader getHeader() {
        return header;
    }

    public void setHeader(RequestHeader header) {
        this.header = header;
    }

    public RequestData getRequestData() {
        return requestData;
    }

    public void setRequestData(RequestData requestData) {
        this.requestData = requestData;
    }

    @Override
    public String toString() {
        return "HttpProcessorRequest{" + "header=" + header + ", requestData=" + requestData + '}';
    }
    
    

}
