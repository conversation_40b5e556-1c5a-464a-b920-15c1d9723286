package com.tl.spotcash.agencybanking.xiputils;

import com.tl.spotcash.agencybanking.custommodels.zposintegrator.LoginResponse;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposIntegratorResponse;
import com.tl.spotcash.agencybanking.custommodels.zposintegrator.ZposRequestData;
import com.tl.spotcash.agencybanking.exceptions.OzoneAuthenticationException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class ZposHttpRequest {
	private String paymentsUri;
	private String validationUri;
	private String username;
	private String password;
	private Environment environment;
	
	public ZposHttpRequest(Environment environment) {
		this.environment = environment;
		setRequestConfigs();
	}
	
    @Autowired
    private SessionFactory sessionFactory;

    protected Session getSession() {
        return sessionFactory.getCurrentSession();
    }
	
	/**
	 * 
	 * @param zposRequestData The transaction request data being send to Zpos
	 * @return ZposIntegratorResponse object.
	 * @throws Exception 
	 */
	public 	ZposIntegratorResponse postPayment(final ZposRequestData zposRequestData) throws OzoneAuthenticationException {
		final String token = getZposToken();
		if (token == null) throw new OzoneAuthenticationException("Payment authentication failed.");
		
		final RestTemplate restTemplate = new RestTemplate();
		final HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		httpHeaders.set("WatchDog", "Bearer "+ token);
		final HttpEntity<ZposRequestData> httpEntity = new HttpEntity<ZposRequestData>(zposRequestData, httpHeaders);
		
		try {
			final ZposIntegratorResponse integratorResponse =
					restTemplate.postForObject(this.paymentsUri, httpEntity, ZposIntegratorResponse.class);
			return integratorResponse;
		} catch(Exception e) {
			throw e;
		}
		
	}
	
	/**
	 * Return the token used to authenticate each payment request.
	 * @return token used for authenticating Zpos payment requests.
	 */
	private String getZposToken() {
		LoginResponse loginResponse;
		String token = null;
		RestTemplate restTemplate = new RestTemplate();
		Map<String, String> loginRequest = new HashMap<>();
		final HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.set("username", this.username);
		httpHeaders.set("password", this.password);
		
		try {
			loginResponse = restTemplate.postForObject(this.validationUri, httpHeaders, LoginResponse.class);
		} catch(Exception e) {
			throw e;
		}
		if (loginResponse != null) {
			token = loginResponse.getToken();
		}
		return token;
	}
	
	private void setRequestConfigs() {
		this.paymentsUri = this.environment.getRequiredProperty("ozone.payments.endpoint");
		this.validationUri = this.environment.getRequiredProperty("ozone.validation.endpoint");
		this.username = this.environment.getRequiredProperty("ozone.username");
		this.password = this.environment.getRequiredProperty("ozone.password");
	}

	
}

