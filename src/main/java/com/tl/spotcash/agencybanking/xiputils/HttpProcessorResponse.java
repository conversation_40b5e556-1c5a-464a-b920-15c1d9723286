package com.tl.spotcash.agencybanking.xiputils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(value = Include.NON_EMPTY)
public class HttpProcessorResponse {

    @JsonProperty
    private ResponseHeader header;

    @JsonProperty
    private Map<String, Object> responseData = new HashMap<>();

    public ResponseHeader getHeader() {
        return header;
    }

    public HttpProcessorResponse() {
        super();
    }

    public HttpProcessorResponse(ResponseHeader header,
            Map<String, Object> responseData) {
        super();
        this.header = header;
        this.responseData = responseData;
    }

    public void setHeader(ResponseHeader header) {
        this.header = header;
    }

    public Map<String, Object> getResponseData() {
        return responseData;
    }

    public void setResponseData(Map<String, Object> responseData) {
        this.responseData = responseData;
    }

    @Override
    public String toString() {
        return "HttpProcessorResponse [header=" + header + ", responseData=" + responseData + "]";
    }

}
