# MySQL-Enhanced CrudService Guide

## Overview

The CrudService has been enhanced to provide comprehensive support for MySQL-specific syntax and features. This guide explains how to use the new MySQL-specific methods and utilities.

## New MySQL-Specific Methods

### 1. executeMySQLNativeQuery()

Execute MySQL-specific native queries with optimizations for MySQL syntax.

```java
@Autowired
private CrudService crudService;

// Example: INSERT...ON DUPLICATE KEY UPDATE
String query = "INSERT INTO users (id, name, email) VALUES (:id, :name, :email) " +
               "ON DUPLICATE KEY UPDATE name = VALUES(name), email = VALUES(email)";

Map<String, Object> params = new HashMap<>();
params.put("id", 1);
params.put("name", "John Doe");
params.put("email", "<EMAIL>");

int affectedRows = crudService.executeMySQLNativeQuery(query, params);
```

### 2. fetchWithMySQLNativeQuery()

Fetch data using MySQL-specific native queries with pagination support.

```java
// With pagination
String query = "SELECT * FROM users WHERE status = :status";
Map<String, Object> params = Map.of("status", "ACTIVE");
List<Object[]> results = crudService.fetchWithMySQLNativeQuery(query, params, 0, 100);

// Without pagination
List<Object[]> allResults = crudService.fetchWithMySQLNativeQuery(query, params);
```

### 3. executeMySQLBatchQuery()

Execute batch operations for improved performance with bulk operations.

```java
String insertQuery = "INSERT INTO audit_log (action, user_id, timestamp) VALUES (:action, :userId, :timestamp)";

List<Map<String, Object>> batchParams = new ArrayList<>();
for (int i = 1; i <= 1000; i++) {
    Map<String, Object> params = new HashMap<>();
    params.put("action", "LOGIN");
    params.put("userId", i);
    params.put("timestamp", new Date());
    batchParams.add(params);
}

int[] results = crudService.executeMySQLBatchQuery(insertQuery, batchParams);
```

### 4. executeMySQLStoredProcedure()

Execute MySQL stored procedures with parameter support.

```java
Map<String, Object> params = new HashMap<>();
params.put("userId", 123);
params.put("startDate", "2024-01-01");
params.put("endDate", "2024-12-31");

List<Object[]> results = crudService.executeMySQLStoredProcedure("GetUserTransactionSummary", params);
```

## MySQL Query Helper Utility

The `MySQLQueryHelper` class provides methods to build MySQL-specific queries.

### INSERT...ON DUPLICATE KEY UPDATE

```java
@Autowired
private MySQLQueryHelper mysqlHelper;

List<String> columns = Arrays.asList("id", "name", "email", "updated_at");
List<String> updateColumns = Arrays.asList("name", "email", "updated_at");

String query = mysqlHelper.buildInsertOnDuplicateKeyUpdate("users", columns, updateColumns);
// Result: INSERT INTO users (id, name, email, updated_at) VALUES (:id, :name, :email, :updated_at) 
//         ON DUPLICATE KEY UPDATE name = VALUES(name), email = VALUES(email), updated_at = VALUES(updated_at)
```

### REPLACE INTO

```java
List<String> columns = Arrays.asList("id", "session_token", "user_id");
String query = mysqlHelper.buildReplaceInto("user_sessions", columns);
// Result: REPLACE INTO user_sessions (id, session_token, user_id) VALUES (:id, :session_token, :user_id)
```

### JSON Functions

```java
// Extract JSON data
String jsonExtract = mysqlHelper.buildJsonFunction("metadata", "$.preferences", "UNQUOTE");
// Result: JSON_UNQUOTE(JSON_EXTRACT(metadata, '$.preferences'))

// Update JSON data
String jsonUpdate = mysqlHelper.buildJsonFunction("metadata", "$.last_login", "SET");
// Result: JSON_SET(metadata, '$.last_login', ?)
```

### Full-Text Search

```java
List<String> columns = Arrays.asList("title", "description");
String searchClause = mysqlHelper.buildFullTextSearch(columns, "banking transaction", "NATURAL LANGUAGE");
// Result: MATCH(title, description) AGAINST ('banking transaction' IN NATURAL LANGUAGE MODE)
```

### Window Functions

```java
List<String> partitionBy = Arrays.asList("department_id");
List<String> orderBy = Arrays.asList("salary DESC");
String windowFunc = mysqlHelper.buildWindowFunction("ROW_NUMBER", partitionBy, orderBy);
// Result: ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC)
```

### GROUP_CONCAT

```java
String groupConcat = mysqlHelper.buildGroupConcat("role_name", ", ", "role_name ASC");
// Result: GROUP_CONCAT(role_name ORDER BY role_name ASC SEPARATOR ', ')
```

### CASE WHEN

```java
Map<String, String> conditions = new HashMap<>();
conditions.put("status = 'ACTIVE'", "'User is active'");
conditions.put("status = 'INACTIVE'", "'User is inactive'");

String caseWhen = mysqlHelper.buildCaseWhen(conditions, "'Unknown status'");
// Result: CASE WHEN status = 'ACTIVE' THEN 'User is active' 
//              WHEN status = 'INACTIVE' THEN 'User is inactive' 
//              ELSE 'Unknown status' END
```

## MySQL Configuration Enhancements

The application has been configured with MySQL-specific optimizations:

### Connection Pool Optimizations
- `cachePrepStmts=true` - Cache prepared statements
- `prepStmtCacheSize=250` - Cache size for prepared statements
- `rewriteBatchedStatements=true` - Optimize batch operations
- `useServerPrepStmts=true` - Use server-side prepared statements

### Character Encoding
- `characterEncoding=utf8mb4` - Full UTF-8 support including emojis
- `useUnicode=true` - Enable Unicode support

### Performance Settings
- `hibernate.jdbc.batch_size=50` - Batch size for bulk operations
- `hibernate.order_inserts=true` - Order inserts for better performance
- `hibernate.order_updates=true` - Order updates for better performance

## Best Practices

### 1. Use Appropriate Methods
- Use `executeMySQLNativeQuery()` for MySQL-specific syntax
- Use `executeMySQLBatchQuery()` for bulk operations
- Use regular `executeNativeQuery()` for standard SQL

### 2. Parameter Binding
Always use parameter binding to prevent SQL injection:

```java
// Good
String query = "SELECT * FROM users WHERE id = :userId";
Map<String, Object> params = Map.of("userId", 123);

// Bad
String query = "SELECT * FROM users WHERE id = " + userId;
```

### 3. Batch Operations
For bulk operations, use batch methods for better performance:

```java
// For large datasets, use batch operations
if (dataList.size() > 100) {
    crudService.executeMySQLBatchQuery(query, paramsList);
} else {
    // For small datasets, individual operations are fine
    for (Map<String, Object> params : paramsList) {
        crudService.executeMySQLNativeQuery(query, params);
    }
}
```

### 4. JSON Operations
When working with JSON columns, use the helper methods:

```java
// Extract JSON safely
String query = "SELECT id, " + 
    mysqlHelper.buildJsonFunction("metadata", "$.user_id", "UNQUOTE") + 
    " as user_id FROM profiles WHERE " +
    mysqlHelper.buildJsonFunction("metadata", "$.active", "EXTRACT") + " = true";
```

### 5. Error Handling
Always wrap database operations in try-catch blocks:

```java
try {
    int result = crudService.executeMySQLNativeQuery(query, params);
    LOG.info("Operation successful, affected rows: {}", result);
} catch (Exception e) {
    LOG.error("Database operation failed: {}", e.getMessage(), e);
    // Handle error appropriately
}
```

## Migration from Oracle to MySQL

If migrating from Oracle, consider these MySQL equivalents:

| Oracle | MySQL |
|--------|-------|
| `SYSDATE` | `NOW()` |
| `ROWNUM` | `LIMIT` |
| `DECODE` | `CASE WHEN` |
| `NVL` | `IFNULL` or `COALESCE` |
| `SUBSTR` | `SUBSTRING` |
| `LENGTH` | `CHAR_LENGTH` |
| `INSTR` | `LOCATE` |

## Performance Tips

1. **Use Indexes**: Ensure proper indexing for frequently queried columns
2. **Limit Results**: Use pagination for large result sets
3. **Batch Operations**: Use batch methods for bulk operations
4. **Connection Pooling**: The configuration includes optimized connection pooling
5. **Prepared Statements**: Always use parameter binding for better performance

## Troubleshooting

### Common Issues

1. **Character Encoding Issues**: Ensure `utf8mb4` is used for full UTF-8 support
2. **Timezone Issues**: The configuration sets `serverTimezone=UTC`
3. **SSL Issues**: SSL is disabled by default; enable if needed
4. **Connection Timeouts**: Adjust timeout settings in application.yml if needed

### Logging

Enable SQL logging for debugging:

```yaml
datasource:
  spotcash:
    hibernate:
      show_sql: true
      format_sql: true
```

This guide provides comprehensive information on using the MySQL-enhanced CrudService. For specific use cases, refer to the `MySQLExampleService` class for practical examples.
